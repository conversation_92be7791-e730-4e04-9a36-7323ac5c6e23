#include "appmain.h"
//#include "nav_includes.h"
//#include "frame_analysis.h"
#include "gdtypedefine.h"
#include "INS_Data.h"
#include "INS_Output.h"
#include "flash.h"

extern uint8_t g_StartUpdateFirm;//开始升级标志 1:开始升级 0:结束升级

#define	c_outputmode_normal		0					//输出算法结果
#define	c_outputmode_gdw		1					//输出二进制观测量，保留，暂不使用
#define	c_outputmdoe_fpga		2					//输出FPGA的观测量值
#define	c_outputmode			c_outputmdoe_fpga	//设定开机默认输出设置  上位机输出的文件格式为 .XDat


char fpgatesttxt[2048];		//A temporary variable during data conversion
//int	gins912outputmode = c_outputmdoe_fpga;				//设定开机默认输出设置
int	gins912outputmode = c_outputmode_normal;				//runcar 模式
//int	gins912outputmode = UPPER_InsUart_Hd0x66AA ; // 对比 fgpa acc fog 的raw数据和alg1数据
//int	gins912outputmode = UPPER_InsUart_Hd0x00BB ;   // 追踪 acc fog alg1数据 和除此之外的 所有 fgpa raw数据

void output_normal_do(void)
{
    if(SetSdOperateType !=0x03)
	protocol_send();
}

void output_fpgatxt_do(void)
{
	int	i;
	char txbuf[200];		//
	char tmptxt[10];	
	//int bover = 0;	
	
	if (gfpgasenddatalen >= 200) {
		sprintf(txbuf, "\r\n\r\nError: FPGA data length: 0x%04x.\n", gfpgasenddatalen);
		uart4sendmsg(txbuf, strlen(txbuf));
		return;
	}

	memset(fpgatesttxt, 0, sizeof(fpgatesttxt));
	//sprintf(fpgatesttxt, "\r\n#TLH:", ((gfpgadata[1] >> 8) & 0xff));
	sprintf(fpgatesttxt, "\r\n#TLH:");
	
	memcpy(&gfpgadata[gfpgasenddatalen + 2] ,&galgrithomresultTx, sizeof(galgrithomresultTx));
#if 0
	for (i = 0; i < gfpgasenddatalen + 2; i++) {
#else
	for (i = 0; i < gfpgasenddatalen + 2 + sizeof(galgrithomresultTx) / 2; i++) {
#endif
		sprintf(tmptxt, "%04x ", gfpgadata[i]);
		strcat(fpgatesttxt, tmptxt);				
		//bover = 0;
		if (i % 20 == 19 && i != gfpgasenddatalen + 1) {
			sprintf(txbuf, "\r\n");
			strcat(fpgatesttxt, txbuf);
			//bover = 1;
		}
	}
	uart4sendmsg(fpgatesttxt, strlen(fpgatesttxt));	
}

void output_gdw_do(void)
{
	gdwrxdata912TX_t	tempd;
	
	tempd.gdwdata.datalength = gpagedata.DATA_LEN;
	tempd.gdwdata.selftestingcode = gpagedata.NUM_CLK;
	tempd.gdwdata.fpgaversion = gpagedata.VERSION ^ 0xffff;
	tempd.gdwdata.watchversion = 0x01;
	tempd.gdwdata.gears = gcanInfo.data.Gear;
	tempd.gdwdata.flwheelspeed = gcanInfo.data.WheelSpeed_Front_Left;  
	tempd.gdwdata.frwheelspeed = gcanInfo.data.WheelSpeed_Front_Right;
	tempd.gdwdata.blwheelspeed = gcanInfo.data.WheelSpeed_Back_Left;
	tempd.gdwdata.brwheelspeed = gcanInfo.data.WheelSpeed_Back_Right;
	tempd.gdwdata.caninfocounter = gcanInfo.counter;
	tempd.gdwdata.fogx = gpagedata.fog_x;
	tempd.gdwdata.fogy = gpagedata.fog_y;
	tempd.gdwdata.fogz = gpagedata.fog_z;
	tempd.gdwdata.fogtemperaturex = gpagedata.fog_tempx;
	tempd.gdwdata.fogtemperaturey = gpagedata.fog_tempy;
	tempd.gdwdata.fogtemperaturez = gpagedata.fog_tempz;
	tempd.gdwdata.accelerometerx = gpagedata.axis3_accx;
	tempd.gdwdata.accelerometery = gpagedata.axis3_accy;
	tempd.gdwdata.accelerometerz = gpagedata.axis3_accz;
	tempd.gdwdata.accelerometertemp = gpagedata.axis3_temp;
	tempd.gdwdata.reserve1e = 0;
	tempd.gdwdata.reserve1f = 0;
	tempd.gdwdata.Reserve20 = 0;
	tempd.gdwdata.gnssweek = gpagedata.hGPSData_gpsweek;
	tempd.gdwdata.millisecondofweek = gpagedata.hGPSData_gpssecond;
	tempd.gdwdata.secondofweek = gpagedata.gpssecond982;
	tempd.gdwdata.ppsdelay10ns = gpagedata.ppstimesdelay;
	tempd.gdwdata.gpsstarnumber = gpagedata.GPGGA_STAR;
	tempd.gdwdata.rtkstatus = gpagedata.rtkstatus;
	tempd.gdwdata.speedstatus = gpagedata.gnssspeedstatus;
	tempd.gdwdata.truenorthtrack[0] = 0x0102; 
	tempd.gdwdata.truenorthtrack[1] = 0x0609; 
	tempd.gdwdata.truenorthtrack[2] = 0x0304; 
	tempd.gdwdata.northvelocity = gpagedata.hGPSData_vn;
	tempd.gdwdata.eastvelocity = gpagedata.hGPSData_ve;
	tempd.gdwdata.upvelocity = gpagedata.hGPSData_vu;
	
	tempd.gdwdata.northvelocity = 1.9;
	tempd.gdwdata.eastvelocity = 9.1;
	tempd.gdwdata.upvelocity = 3.3;	
	
	//tempd.gdwdata.positionstatus = gpagedata.positionstatus;//位置状态
	//tempd.gdwdata.directionoflat = gpagedata.directionoflat;//纬度方向
        tempd.gdwdata.GnssStaDirLat = gpagedata.GnssStaDirLat;//高8位位置状态,低8位纬度方向

	tempd.gdwdata.latitude = gpagedata.hGPSData_Lat;
	//tempd.gdwdata.directionoflon = directionoflon;//经度方向
        tempd.gdwdata.DirLonHeadingSta = gpagedata.DirLonHeadingSta;//高8位经度方向,低8位航向状态

	tempd.gdwdata.longitude = gpagedata.hGPSData_Lon;
	tempd.gdwdata.altitude = gpagedata.hGPSData_Alt;

	//tempd.gdwdata.Headingstate = gpagedata.Headingstate;//航向状态

	tempd.gdwdata.baselength = gpagedata.baselinelength;
	tempd.gdwdata.roll = gpagedata.hGPSData_Roll;
	tempd.gdwdata.pitch = gpagedata.hGPSData_Pitch;
	tempd.gdwdata.yaw = gpagedata.hGPSData_Yaw;
	//tempd.gdwdata.ECEF_X = gpagedata.ECEF_X;
	//tempd.gdwdata.ECEF_Y = gpagedata.ECEF_Y;
	//tempd.gdwdata.ECEF_Z = gpagedata.ECEF_Z;
	//tempd.gdwdata.geometricprecZ = gpagedata.geometry_z;
	//tempd.gdwdata.positionprecZ = gpagedata.location_z;
	tempd.gdwdata.timeprecisionZ = gpagedata.time_z;
	tempd.gdwdata.verticalprecZ = gpagedata.vertical_z;
	tempd.gdwdata.horizontalprecZ = gpagedata.horizontal_z;
	tempd.gdwdata.northprecisionZ = gpagedata.north_z;
	tempd.gdwdata.eastprecisionZ = gpagedata.east_z;
	tempd.gdwdata.endheightangleZ = gpagedata.endheight_z;
	tempd.gdwdata.checksum = gpagedata.checksum;
	tempd.gdwdata.checksumA = gpagedata.checksumA;
	tempd.gdwdata.frameindex = gdriverspacket;
	memcpy(&tempd.gdwdata.Alongitude, &galgrithomresultTx, sizeof(galgrithomresultTx));

	
	unsigned char *ptmpu8;
	unsigned short checksum;
	ptmpu8 = (unsigned char *)&tempd;
	checksum = 0;
	//gimuorgdatasendtopc.cacv = scha63x_cac_values;
	tempd.head = 0x55aa;
	tempd.len = sizeof(tempd);
	tempd.type = DRIVERSDATATYPE_GDW912;
	tempd.packet = ggdworgdata_packet++;

	//__disable_irq();    //----------------------------------
        disable_global_irq(CSR_MSTATUS_MIE_MASK);
	tempd.packetT = gdriverspacket;
	for (int i = 0; i < sizeof(tempd) - 2; i++) {
		checksum += *ptmpu8++;
	}
	tempd.checksum = checksum;
	if (gdriverdatalist.size < GD_DRIVERSDATA_MAXCOUNT) {
		gdriverdatalist.driversdata[(gdriverdatalist.st + gdriverdatalist.size) % GD_DRIVERSDATA_MAXCOUNT].driversdatatype = DRIVERSDATATYPE_GDW;
		gdriverdatalist.driversdata[(gdriverdatalist.st + gdriverdatalist.size) % GD_DRIVERSDATA_MAXCOUNT].driverspacket = gdriverspacket;
		gdriverdatalist.driversdata[(gdriverdatalist.st + gdriverdatalist.size) % GD_DRIVERSDATA_MAXCOUNT].data.gdw = tempd;
		gdriverspacket++;
		gdriverdatalist.size++;
	}
	//__enable_irq();     //----------------------------------
        enable_global_irq(CSR_MSTATUS_MIE_MASK);
	mcusendtopcdriversdata(0, 0, 0);
}

void output_fpga_void(void)
{	
	gdwrxdata912TX_t	tempd;
	navcanin_t		canin;
	memcpy(&gpagedata.Alongitude, &galgrithomresultTx, sizeof(galgrithomresultTx));
	memcpy(&tempd.gdwdata, &gpagedata, sizeof(gpagedata));
	caninfupdate(&canin);
	memcpy(&tempd.gdwdata.gears, &canin, sizeof(canin));
	tempd.gdwdata.frameindex = gdriverspacket;
	
	unsigned short *ptmpu8x;
	unsigned short checksum;
	
	ptmpu8x = (unsigned short *)&tempd.gdwdata;
	checksum = 0;
	int sizex;
	sizex = (int)(&tempd.gdwdata.checksum) - (int)(&tempd.gdwdata.datalength);
	for (int i = 0; i < sizex / 1; i++) {
		checksum += *ptmpu8x++;
	}
	tempd.gdwdata.checksum = checksum;
	
	
	
	ptmpu8x = (unsigned short *)&tempd.gdwdata;
	checksum = 0;
	sizex = sizeof(tempd.gdwdata) / 2 - 1;
	for (int i = 0; i < sizex; i++) {
		checksum += *ptmpu8x++;
	}
	tempd.gdwdata.checksumA = checksum;
	
	
	
	unsigned char *ptmpu8 = (unsigned char *)&tempd;
	checksum = 0;
	//gimuorgdatasendtopc.cacv = scha63x_cac_values;
	tempd.head = 0x55aa;
	tempd.len = sizeof(tempd);
	tempd.type = DRIVERSDATATYPE_FPGA912;
	tempd.packet = ggdworgdata_packet++;

	//__disable_irq();    //----------------------------------
        disable_global_irq(CSR_MSTATUS_MIE_MASK);
	tempd.packetT = gdriverspacket;
	for (int i = 0; i < sizeof(tempd) - 2; i++) {
		checksum += *ptmpu8++;
	}
	tempd.checksum = checksum;
	if (gdriverdatalist.size < GD_DRIVERSDATA_MAXCOUNT) {
		gdriverdatalist.driversdata[(gdriverdatalist.st + gdriverdatalist.size) % GD_DRIVERSDATA_MAXCOUNT].driversdatatype = DRIVERSDATATYPE_GDW;
		gdriverdatalist.driversdata[(gdriverdatalist.st + gdriverdatalist.size) % GD_DRIVERSDATA_MAXCOUNT].driverspacket = gdriverspacket;
		gdriverdatalist.driversdata[(gdriverdatalist.st + gdriverdatalist.size) % GD_DRIVERSDATA_MAXCOUNT].data.gdw = tempd;
		gdriverspacket++;
		gdriverdatalist.size++;
	}       
	//__enable_irq();     //----------------------------------
        enable_global_irq(CSR_MSTATUS_MIE_MASK);
	mcusendtopcdriversdata(0, 0, 0);
}

//==========================================================================================
//Input: 	算法结果值
//Output:	NULL
//Funtion:	通过串口将数据发送给上位机
//  目前可使用两种设置：算法标准结果输出到标准上位机可以在地图上查看跑车路径
//	另一种输出格式为FPGA观测量加算法结果，使用“天陆海惯导综合开发平台”上位机
//==========================================================================================
void INS912_Output(navoutdata_t *pnavout)
{
#ifdef	test_gyc_INS912_Output_enabled_20240709

    //输出频率设置
    static uint16_t tCnt = 0;
    uint16_t freq = (SETPARA_DATAOUT_FPGA_FREQ / stSetPara.Setfre);
    tCnt++;
    if(tCnt >= freq)
    {
        tCnt = 0;

	//The following code assigns values to the results of the algorithm
	//将算法结果直接赋值是观测量的输出，嵌入式在此位置赋值，就不需要算法工程师关注赋值的部份
	//算法工程师只需要保证算法结果就可以了
	galgrithomresultTx.longitude = gnavout.longitude;
	galgrithomresultTx.latitude = gnavout.latitude;
	galgrithomresultTx.altitude = gnavout.altitude;
	galgrithomresultTx.ve = gnavout.ve;
	galgrithomresultTx.vn = gnavout.vn;
	galgrithomresultTx.vu = gnavout.vu;
	galgrithomresultTx.pitch = gnavout.pitch;
	galgrithomresultTx.roll = gnavout.roll;
	galgrithomresultTx.heading = gnavout.azimuth;
        if((g_StartUpdateFirm != 1)&&(stSetPara.SetDataOutType==0))//不在升级状态时，从才能发送数据
        {
            if (gins912outputmode == c_outputmode_normal) {			//正常结果输出，使用支持地图显示的上位机软件。
              output_normal_do();
            }
            else if (gins912outputmode == c_outputmdoe_fpgatxt) {	//直接使用串口助手，或使用“天陆海惯导综合开发平台”上位机的 "通用串口助手" 功能
              output_fpgatxt_do();
            }
            else if (gins912outputmode == c_outputmode_gdw) {		//输出二进制观测量，保留，暂不使用
              output_gdw_do();
            }
            else if (gins912outputmode == c_outputmdoe_fpga) {	//输出FPGA的观测量值, .XDat格式数据文件    算法工程师需要的观测量数据文件
              output_fpga_void();		
            }
        }
    }

#endif	
}
