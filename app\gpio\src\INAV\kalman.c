/*****************************************************文件说明******************************************************************************/
/*文件名称：Kalman.C                                                                                                                       */
/*版本号：  Ver 0.1                                                                                                                        */
/*编写日期/时间：                                                                                                                          */
/*编写人：                                                                                                                                 */
/*包含文件：const.h、typedefine.h、math.h、DATASTRUCT.h、EXTERNGLOBAL.h、memory.h                                                          */
/*测试用例：由GNSSlocusGen.m文件生成整套软件系统测试用例，单个模块测试用例暂缺                                                              */
/*说明：本文件包含了程序中用到的卡尔曼滤波解算函数。（此文件为初始测试版本，文件中所定义函数仅供程序设计人员参考选用）                     */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include <math.h>
#include "DATASTRUCT.h"
#include "EXTERNGLOBALDATA.h"
#include "FUNCTION.h"
#include <string.h>
#include "deviceconfig.h"
/*********************************函数说明***************************************/
/*函数名称：Kalman_StartUp                                                      */
/*函数功能描述：根据GNSS数据情况调节滤波模式                                    */
/*版本号：  Ver 0.1                                                             */
/*编写日期/时间：                                                               */
/*编写人：                                                                      */
/*输入、输出变量：指针:lp_Kalman,指针:lp_GNSSData,指针:lp_SysVar,指针:lp_Navi   */
/*测试用例：暂缺                                                                */
/*备注1：请在Kalman滤波解算开始前调用该函数进行Kalman滤波结构体的初始化         */
/*备注2：该函数为各单个矩阵初始化函数的封装函数，输入输出变量lp_Kalman为地址传递*/
/*备注3：该函数调用了C语言标准库函数memset（函数声明包含于memory.h中）          */
/*返回值：无                                                                    */
/********************************************************************************/
void Kalman_StartUp(p_Kalman lp_Kalman,p_GNSSData lp_GNSSData,p_SysVar lp_SysVar,p_Navi lp_Navi)
{
	if(lp_GNSSData -> isPosEn == YES)
	{
		//GNSS更新及有效状态字设置，同时将卫导收到时间清0
		lp_SysVar->isGNSS_Update = YES;
		lp_SysVar->isGNSSValid = YES;			  
		//从失锁纯惯状态下恢复组合的处理
		if((lp_SysVar->WorkPhase == PHASE_INS_NAVI)&&(lp_Kalman->isInsRecord == YES))
			{
						
				if(lp_SysVar->Time_INS_Alone > 180.0)
				{
						//失锁超过180s，重置导航经纬高和航向
						DPARA Logi_Err;
						lp_Navi->r_Lati -= lp_Kalman->r_InsLati - lp_GNSSData->r_GNSSLati;
							
						Logi_Err = lp_Kalman->r_InsLogi - lp_GNSSData->r_GNSSLogi;
						if(Logi_Err < -PI)
						{
								Logi_Err += 2 * PI;
						}
						else if(Logi_Err > PI)
						{
								Logi_Err -= 2 * PI;
						}
						lp_Navi->r_Logi -= Logi_Err;
						//东西经正负180度切换的判断
						if(lp_Navi->r_Logi > PI)
						{
								lp_Navi->r_Logi = lp_Navi->r_Logi - 2 * PI; //
						}
							
						if(lp_Navi->r_Logi < -PI)
						{
								lp_Navi->r_Logi = lp_Navi->r_Logi + 2 * PI; //
						}
							lp_Navi->Height -= lp_Kalman->InsHeight - lp_GNSSData->GNSSHeight;
							
						for(int i = 0;i<3;i++)
						{
							lp_Navi->Vn[i] -=  lp_Kalman->InsVn[i] - lp_GNSSData->GNSSVn[i];
						}
						//
						if((lp_Navi->isHeadingchange == NO)&&(lp_GNSSData->isHeadingEn == YES))
						{
							lp_Navi->r_Atti[0] = lp_GNSSData->r_GPSHead;
							AttiToCnb(lp_Navi->r_Atti,lp_Navi->Cnb);
							CnbToQ(lp_Navi->Cnb,lp_Navi->Q);
						}
				}				
//					g_SysVar.Xk_M[7] = g_Kalman.Xk[7];
//          g_SysVar.Pk_M[7] = g_Kalman.Pk[7 * DIM_STATE + 7];
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_SysVar.Xk_M[i] = g_Kalman.Xk[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_SysVar.Pk_M[i] = g_Kalman.Pk[i * DIM_STATE + i];
				}
        Kalman_Init(&g_Kalman);
//          g_Kalman.Xk[7] = g_SysVar.Xk_M[7];
//          g_Kalman.Pk[7 * DIM_STATE + 7] = g_SysVar.Pk_M[7];
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_Kalman.Xk[i] = g_SysVar.Xk_M[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
						g_Kalman.Pk[i * DIM_STATE + i] = g_SysVar.Pk_M[i];
				}
				lp_Kalman->isInsRecord = NO;
				//lp_SysVar->isDampedOK = NO;				
			}
			
		//组合模式的调整
		if((lp_Kalman->isInsRecord == YES)&&(lp_SysVar->WorkPhase == PHASE_INTEGRATED_NAVI))
		{
			if((g_Kalman.Kal_Count >= COUNT_RESET_KALMAN_FILTER) && (g_Navi.isHeadingchange == NO))
			{
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_SysVar.Xk_M[i] = g_Kalman.Xk[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_SysVar.Pk_M[i] = g_Kalman.Pk[i * DIM_STATE + i];
				}
				Kalman_Init(&g_Kalman);
//          g_Kalman.Xk[7] = g_SysVar.Xk_M[7];
//          g_Kalman.Pk[7 * DIM_STATE + 7] = g_SysVar.Pk_M[7];
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_Kalman.Xk[i] = g_SysVar.Xk_M[i];
				}
				for(int i = 9; i < DIM_STATE;i++)
				{
					g_Kalman.Pk[i * DIM_STATE + i] = g_SysVar.Pk_M[i];
				}
				lp_Kalman->isInsRecord = NO;
			}
			else
			{
				//触发滤波任务
				lp_Kalman->Work_Mode = FILTER;
				if(lp_GNSSData->isHeadingEn == YES)
				{
					lp_Kalman->Obv_Fit_Mode = POSVELHEAD_FIT;	
					lp_GNSSData->isHeadingEn = NO;
				}
				else
				{
					lp_Kalman->Obv_Fit_Mode = POSVEL_FIT;					
				}
				Rk_Init(g_Kalman.Rk,g_Kalman.Obv_Fit_Mode); 	
				Hk_Init(g_Kalman.Hk,g_Kalman.Obv_Fit_Mode);
				g_Kalman.isKalmanStart = YES;
				lp_Kalman->isInsRecord = NO;
					
			}
		}
	}
	else
	{
		lp_SysVar->isGNSS_Update = NO;
		lp_GNSSData->isHeadingEn = NO;
		//g_SysVar.isGNSSValid = NO;
		//触发任务
		if((lp_Kalman->isInsRecord == YES)&&(lp_SysVar->WorkPhase == PHASE_INTEGRATED_NAVI))
		{
			lp_Kalman->Work_Mode = PREDICT;
			g_Kalman.isKalmanStart = YES;
			lp_Kalman->isInsRecord = NO;
		}
	}		
}
/*********************************函数说明***************************************/
/*函数名称：Kalman_Init                                                         */
/*函数功能描述：完成Kalman滤波结构体的初始化                                    */
/*版本号：  Ver 0.1                                                             */
/*编写日期/时间：                                                               */
/*编写人：                                                                      */
/*输入、输出变量：指针:Kalman滤波结构体：lp_Kalman                              */
/*测试用例：暂缺                                                                */
/*备注1：请在Kalman滤波解算开始前调用该函数进行Kalman滤波结构体的初始化         */
/*备注2：该函数为各单个矩阵初始化函数的封装函数，输入输出变量lp_Kalman为地址传递*/
/*备注3：该函数调用了C语言标准库函数memset（函数声明包含于memory.h中）          */
/*返回值：无                                                                    */
/********************************************************************************/
void Kalman_Init(p_Kalman lp_Kalman)
{
	  memset(lp_Kalman, 0, sizeof(Kalman));   //将g_Kalman结构体成员变量全部清零
	  lp_Kalman -> Obv_Fit_Mode = POSVEL_FIT;
		//lp_Kalman -> Actual_Dim_Obv = 2;
	  Pk_Init(lp_Kalman -> Pk);               //状态误差协方差矩阵Pk的初始化
	  Qk_Init(lp_Kalman -> Qk);               //系统噪声协方差矩阵Qk的初始化
	  Rk_Init(lp_Kalman -> Rk,lp_Kalman -> Obv_Fit_Mode);               //量测噪声协方差矩阵Rk的初始化
	  Hk_Init(lp_Kalman -> Hk,lp_Kalman -> Obv_Fit_Mode);               //量测矩阵Hk的初始化
	  //滤波状态控制字
	  lp_Kalman -> isInsRecord = NO;
	  lp_Kalman -> isKalmanStart = NO;
	  //lp_Kalman ->  isKalmanComputeStart = NO;
	  lp_Kalman -> isCorrectError = NO;
	  
	  
	
	  //误差修正默认设置
	  lp_Kalman -> isVnCorrect = YES;
	  lp_Kalman -> isPosCorrect = YES;
	  lp_Kalman -> isAttiCorrect = YES;
	  lp_Kalman -> isHeadingCorrect = YES;
	  lp_Kalman -> isGyroDriftCorrect = NO;
	  lp_Kalman -> isAccDriftCorrect = NO;
}

/*********************************************函数说明*******************************************************/
/*函数名称：Pk_Init                                                                                         */
/*函数功能描述：完成状态误差协方差矩阵Pk的初始化                                                            */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入、输出变量：矩阵:Pk                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：Pk矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：Pk矩阵元素值的设定应保证该矩阵的正交性、输入、输出变量Pk为地址传递                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Pk_Init(MATR Pk[DIM_STATE * DIM_STATE])
{
	  
	
	  Pk[0 * DIM_STATE + 0] = 7.839279714436990e-07 * 7.839279714436990e-07;
	  Pk[1 * DIM_STATE + 1] = 7.839279714436990e-07 * 7.839279714436990e-07;
	  Pk[2 * DIM_STATE + 2] = 0.5 * 0.5;
	
	  Pk[3 * DIM_STATE + 3] = 0.35 * 0.35;
	  Pk[4 * DIM_STATE + 4] = 0.35 * 0.35;
	  Pk[5 * DIM_STATE + 5] = 0.35 * 0.35;
	
	  Pk[6 * DIM_STATE + 6] = (0.5 * D2R) * (0.5 * D2R);
	  Pk[7 * DIM_STATE + 7] = (0.5 * D2R) * (0.5 * D2R);
	  Pk[8 * DIM_STATE + 8] = (0.5 * D2R) * (0.5 * D2R);
	
	  Pk[9 * DIM_STATE + 9] = ((1.5 / 3600.0) * D2R) * ((1.5 / 3600.0) * D2R);
	  Pk[10 * DIM_STATE + 10] = ((1.5 / 3600.0) *D2R) *  ((1.5  / 3600.0) *D2R);
	  Pk[11 * DIM_STATE + 11] = ((1.5 / 3600.0) *D2R) *  ((1.5  / 3600.0) *D2R); 
	
	  Pk[12 * DIM_STATE + 12] = (0.00002 * G0) * (0.00002 * G0);
	  Pk[13 * DIM_STATE + 13] = (0.00002 * G0) * (0.00002 * G0);
	  Pk[14 * DIM_STATE + 14] = (0.00002 * G0) * (0.00002 * G0);	

    //Pk[15 * DIM_STATE + 15] = (0.005) * (0.005);
	  //Pk[16 * DIM_STATE + 16] = (0.005) * (0.005);
	  //Pk[17 * DIM_STATE + 17] = (0.005) * (0.005);

    //Pk[18 * DIM_STATE + 18] = (0.005) * (0.005);
	  //Pk[19 * DIM_STATE + 19] = (0.005) * (0.005);
	  //Pk[20 * DIM_STATE + 20] = (0.005) * (0.005);
}

/*********************************************函数说明*******************************************************/
/*函数名称：Qk_Init                                                                                         */
/*函数功能描述：完成系统噪声协方差矩阵Qk的初始化                                                            */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入、输出变量：矩阵:Qk                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：Qk矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：Qk矩阵元素值的设定应保证该矩阵的正交性、输入、输出变量Qk为地址传递                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Qk_Init(MATR Qk[DIM_STATE * DIM_STATE])
{

#ifdef code_test_gyc_old
			Qk[0 * DIM_STATE + 0] = 1.536357661029583e-14 * TIME_FILTER;
			Qk[1 * DIM_STATE + 1] = 1.536357661029583e-14 * TIME_FILTER;
			Qk[2 * DIM_STATE + 2] = 0.506250000000000 * 2  * TIME_FILTER;
	
			Qk[3 * DIM_STATE + 3] = 0.25 * 0.25 * 0.8 * 0.95 * TIME_FILTER;
			Qk[4 * DIM_STATE + 4] = 0.25 * 0.25 * 0.8 * 0.95 * TIME_FILTER;
			Qk[5 * DIM_STATE + 5] = 0.25 * 0.25 * 0.8 * 0.95 * TIME_FILTER;
	
	    Qk[6 * DIM_STATE + 6] = 3e-9  * 0.85 * TIME_FILTER;
			Qk[7 * DIM_STATE + 7] = 3e-9  * 0.85 * TIME_FILTER;
			Qk[8 * DIM_STATE + 8] = 3e-9  * 0.85 * TIME_FILTER;	
#endif	
	
#ifdef code_test_gyc_gao_chen_20240708
	 
			Qk[0 * DIM_STATE + 0] = 1.536357661029583e-15 * 2 * TIME_FILTER;
			Qk[1 * DIM_STATE + 1] = 1.536357661029583e-15 * 2 * TIME_FILTER;
			Qk[2 * DIM_STATE + 2] = 0.2506250000000000 * 2  * TIME_FILTER;
	
			Qk[3 * DIM_STATE + 3] = 0.25 * 0.25 * 0.3 * 0.85 * TIME_FILTER;
			Qk[4 * DIM_STATE + 4] = 0.25 * 0.25 * 0.3 * 0.85 * TIME_FILTER;
			Qk[5 * DIM_STATE + 5] = 0.25 * 0.25 * 0.3 * 0.85 * TIME_FILTER;
	#ifndef DEVICE_TYPE_370_25J_6089//DEVICE_TYPE_370_25J_355
			Qk[6 * DIM_STATE + 6] = 1e-7  * 0.85 * TIME_FILTER;
			Qk[7 * DIM_STATE + 7] = 1e-7  * 0.85 * TIME_FILTER;
			Qk[8 * DIM_STATE + 8] = 1e-7  * 0.85 * TIME_FILTER;
	#endif
	#ifdef DEVICE_TYPE_370_25J_6089
			Qk[6 * DIM_STATE + 6] = 1e-6  * 0.85 * TIME_FILTER;
			Qk[7 * DIM_STATE + 7] = 1e-6  * 0.85 * TIME_FILTER;
			Qk[8 * DIM_STATE + 8] = 1e-6  * 0.85 * TIME_FILTER;	
	#endif
	
#endif	    
	
			Qk[9 * DIM_STATE + 9] = 1e-21  * 0.85 / 100 * TIME_FILTER;
			Qk[10 * DIM_STATE + 10] = 1e-21  * 0.85 / 100 * TIME_FILTER;
			Qk[11 * DIM_STATE + 11] = 1e-21  * 0.85 / 100 * TIME_FILTER;
	
			Qk[12 * DIM_STATE + 12] = 1e-21  * 0.85 / 100 * TIME_FILTER;
			Qk[13 * DIM_STATE + 13] = 1e-21  * 0.85 / 100 * TIME_FILTER;
			Qk[14 * DIM_STATE + 14] = 1e-21  * 0.85 / 100 * TIME_FILTER;
			
			//Qk[15 * DIM_STATE + 15] = 1e-21  * 0.85 / 100 * TIME_FILTER;
			//Qk[16 * DIM_STATE + 16] = 1e-21  * 0.85 / 100 * TIME_FILTER;
	    //Qk[17 * DIM_STATE + 17] = 1e-21  * 0.85 / 100 * TIME_FILTER;
	
			//Qk[18 * DIM_STATE + 18] = 1e-21  * 0.85 / 100 * TIME_FILTER;
			//Qk[19 * DIM_STATE + 19] = 1e-21  * 0.85 / 100 * TIME_FILTER;
	    //Qk[20 * DIM_STATE + 20] = 1e-21  * 0.85 / 100 * TIME_FILTER;		
}


/*********************************************函数说明*******************************************************/
/*函数名称：Rk_Init                                                                                         */
/*函数功能描述：完成系统噪声协方差矩阵Qk的初始化                                                            */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入、输出变量：矩阵:Qk                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：Rk矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：Rk矩阵元素值的设定应保证该矩阵的正交性、输入、输出变量Rk为地址传递                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Rk_Init(MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV],MODE Obv_Fit_Mode)
{ 
	  //memset(Rk, 0.0, DIM_MAX_OBV * DIM_MAX_OBV * sizeof(MATR));
		IPARA i;
	    
		for (i = 0; i < DIM_MAX_OBV * DIM_MAX_OBV; i++)
		{
			Rk[i] = 0;
		}
		switch(Obv_Fit_Mode)        
		{	
			case POS_FIT :
			{				  					 										
				g_Kalman.Actual_Dim_Obv = 3;
				Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
				Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
				Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;				
				break;			 
			}
			case VEL_FIT :
			{		 
				g_Kalman.Actual_Dim_Obv = 3;
	#ifndef DEVICE_TYPE_370_25J_6089//DEVICE_TYPE_370_25J_355
				Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 0.25 * 0.25;
				Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 0.25 * 0.25;
				Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 0.25 * 0.25;	
	#endif 
	#ifdef	DEVICE_TYPE_370_25J_6089
				Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 0.15 * 0.15;
				Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 0.15 * 0.15;
				Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 0.15 * 0.15;					
	#endif				
			  break;	
			}
			case POSVEL_FIT :
			{					 
				g_Kalman.Actual_Dim_Obv = 6; 
				Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
				Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
				Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;	
	#ifndef DEVICE_TYPE_370_25J_6089//DEVICE_TYPE_370_25J_355
				Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 0.25 * 0.25;
				Rk[4 * g_Kalman.Actual_Dim_Obv + 4] = 0.25 * 0.25;
				Rk[5 * g_Kalman.Actual_Dim_Obv + 5] = 0.25 * 0.25;	
	#endif
	#ifdef	DEVICE_TYPE_370_25J_6089
				Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 0.15 * 0.15;
				Rk[4 * g_Kalman.Actual_Dim_Obv + 4] = 0.15 * 0.15;
				Rk[5 * g_Kalman.Actual_Dim_Obv + 5] = 0.15 * 0.15;					
	#endif			
				break;
			}
			case POSVELHEAD_FIT :
			{		
        g_Kalman.Actual_Dim_Obv = 7; 
        Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
			  Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
        Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;	
	#ifndef DEVICE_TYPE_370_25J_6089//DEVICE_TYPE_370_25J_355			
				Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 0.25 * 0.25;
				Rk[4 * g_Kalman.Actual_Dim_Obv + 4] = 0.25 * 0.25;
				Rk[5 * g_Kalman.Actual_Dim_Obv + 5] = 0.25 * 0.25;	
	#endif		
	#ifdef	DEVICE_TYPE_370_25J_6089
				Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 0.15 * 0.15;
				Rk[4 * g_Kalman.Actual_Dim_Obv + 4] = 0.15 * 0.15;
				Rk[5 * g_Kalman.Actual_Dim_Obv + 5] = 0.15 * 0.15;					
	#endif
        Rk[6 * g_Kalman.Actual_Dim_Obv + 6] = 1.5 * D2R * 1.5 * D2R;				
				break;
			}
			case POS_HEAD_FIT :
			{		
        g_Kalman.Actual_Dim_Obv = 4; 
        Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
			  Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
        Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;	
        Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 1.5 * D2R * 1.5 * D2R;				
				break;
			}
			case VEL_HEAD_FIT :
			{		
        g_Kalman.Actual_Dim_Obv = 4; 
#ifndef DEVICE_TYPE_370_25J_6089//DEVICE_TYPE_370_25J_355						
				Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 0.25 * 0.25;
				Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 0.25 * 0.25;
				Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 0.25 * 0.25;		
#endif
#ifdef	DEVICE_TYPE_370_25J_6089
				Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 0.15 * 0.15;
				Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 0.15 * 0.15;
				Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 0.15 * 0.15;					
#endif
				Rk[3 * g_Kalman.Actual_Dim_Obv + 3] = 1.5 * D2R * 1.5 * D2R;				
				break;
			}
			default:
			{
				g_Kalman.Actual_Dim_Obv = 3;
				Rk[0 * g_Kalman.Actual_Dim_Obv + 0] = 1.7e-7 * 1.7e-7 / 8.0;
			  Rk[1 * g_Kalman.Actual_Dim_Obv + 1] = 1.7e-7 * 1.7e-7 / 8.0;
        Rk[2 * g_Kalman.Actual_Dim_Obv + 2] = 1.0 * 1.0 / 8.0;		
				break;
			} 
   }			
}


/*********************************************函数说明*******************************************************/
/*函数名称：Hk_Init                                                                                         */
/*函数功能描述：完成系统量测矩阵Hk的初始化                                                                  */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入、输出变量：矩阵:Hk                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：Hk矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：矩阵输入、输出变量Hk为地址传递                                                                     */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Hk_Init(MATR Hk[DIM_MAX_OBV * DIM_STATE],MODE Obv_Fit_Mode)
{
		register IPARA i;
	
	    for (i = 0; i < DIM_MAX_OBV * DIM_STATE; i++)
		{
			Hk[i] = 0;
		}
		switch(Obv_Fit_Mode)        
		{	
			case POS_FIT :
			{				  					 														
				for(i = 0; i < 3; i++)
				{
					Hk[i * DIM_STATE + i] = 1.0;
				}				
				break;			
			}
			case VEL_FIT :
			{		 
				
				Hk[0 * DIM_STATE + 3] = 1.0;
				Hk[1 * DIM_STATE + 4] = 1.0;
                Hk[2 * DIM_STATE + 5] = 1.0; 				
			    break;	
			}
			case POSVEL_FIT :
			{					 
				for(i = 0; i < 6; i++)
				{
					Hk[i * DIM_STATE + i] = 1.0;
				}		
				break;
			}
			case POSVELHEAD_FIT :
			{		
                for(i = 0; i < 6; i++)
				{
					Hk[i * DIM_STATE + i] = 1.0;
				}		
                Hk[6 * DIM_STATE + 7] = 1.0;				
				break;
			}
			case POS_HEAD_FIT :
			{		
                for(i = 0; i < 3; i++)
				{
					Hk[i * DIM_STATE + i] = 1.0;
				}	
                Hk[3 * DIM_STATE + 7] = 1.0;				
				break;
			}
			case VEL_HEAD_FIT :
			{		
                Hk[0 * DIM_STATE + 3] = 1.0;
				Hk[1 * DIM_STATE + 4] = 1.0;
                Hk[2 * DIM_STATE + 5] = 1.0; 	
				Hk[3 * DIM_STATE + 7] = 1.0; 
				break;
			}
			default:
			{
				for(i = 0; i < 3; i++)
				{
					Hk[i * DIM_STATE + i] = 1.0;
				}					
				break;
			} 
        }
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeFn                                                                                       */
/*函数功能描述：完成一步状态转移矩阵在每个导航周期的累积量部分的累加计算                                    */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Qk                                                                                         */
/*测试用例：暂缺                                                                                            */
/*备注1：Fn矩阵的维数（在CONST.h中定义）、各矩阵元素的物理意义及其值根据具体算法编排确定、此处设定值仅供参考*/
/*备注2：Fn矩阵输入、输出变量Rk为地址传递                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeFn(p_Navi const lp_Navi, MATR Fn[DIM_STATE * DIM_STATE])
{
	register DPARA INVCOSLATI = 1.0 / cos(lp_Navi -> r_Lati);
	register DPARA SINLATI = sin(lp_Navi -> r_Lati);
//	DPARA Fibb[3];
//	DPARA r_Wibb[3];
//	for (int i = 0; i < 3; i++)
//	{
//		Fibb[i] = (lp_Navi ->Fibb[0][i] + lp_Navi ->Fibb[1][i]) * 0.5;
//		r_Wibb[i] = (lp_Navi ->r_Wibb[0][i] + lp_Navi ->r_Wibb[1][i]) * 0.5;
//	}	
	
	Fn[0 * DIM_STATE + 2] += -lp_Navi -> Vn[0] * lp_Navi -> invRm * lp_Navi -> invRm;
	Fn[0 * DIM_STATE + 3] += lp_Navi -> invRm;

    Fn[1 * DIM_STATE + 0] += lp_Navi -> r_Wenn[1] * INVCOSLATI;
    Fn[1 * DIM_STATE + 2] += - lp_Navi -> r_Wenn[0] * lp_Navi -> invRn * INVCOSLATI;
	Fn[1 * DIM_STATE + 5] += lp_Navi -> invRn * INVCOSLATI;

	Fn[2 * DIM_STATE + 4] += 1;
	
	Fn[3 * DIM_STATE + 0] += -1 * (2 * lp_Navi -> Vn[2] * lp_Navi -> r_Wien[0] + lp_Navi -> invRn * (lp_Navi -> Vn[2] * INVCOSLATI) * (lp_Navi -> Vn[2] * INVCOSLATI));	
	Fn[3 * DIM_STATE + 2] += lp_Navi -> Vn[0] * lp_Navi -> Vn[1] * lp_Navi -> invRm * lp_Navi -> invRm + lp_Navi -> r_Wenn[0] * lp_Navi -> r_Wenn[1];
	Fn[3 * DIM_STATE + 3] += -lp_Navi -> Vn[1] * lp_Navi -> invRm;	
    Fn[3 * DIM_STATE + 4] += -lp_Navi -> Vn[0] * lp_Navi -> invRm;
	Fn[3 * DIM_STATE + 5] += -2 * (lp_Navi -> r_Wien[1] + lp_Navi -> r_Wenn[1]);
    Fn[3 * DIM_STATE + 7] += -1 * lp_Navi -> Fibn[2];		
	Fn[3 * DIM_STATE + 8] += lp_Navi -> Fibn[1];

    Fn[3 * DIM_STATE + 12] += lp_Navi -> Cnb[0];
    Fn[3 * DIM_STATE + 13] += lp_Navi -> Cnb[3];
	Fn[3 * DIM_STATE + 14] += lp_Navi -> Cnb[6];

 //   Fn[3 * DIM_STATE + 18] += lp_Navi -> Cnb[0] * Fibb[0];
 //   Fn[3 * DIM_STATE + 19] += lp_Navi -> Cnb[3] * Fibb[1];
 //   Fn[3 * DIM_STATE + 20] += lp_Navi -> Cnb[6] * Fibb[2];
	
	Fn[4 * DIM_STATE + 0] += -2 * lp_Navi -> Vn[2] * lp_Navi -> r_Wien[1];
    Fn[4 * DIM_STATE + 2] += -1*(lp_Navi -> r_Wenn[2] * lp_Navi -> r_Wenn[2] + lp_Navi -> r_Wenn[0] * lp_Navi -> r_Wenn[0]); 
    Fn[4 * DIM_STATE + 3] += -2 * lp_Navi -> r_Wenn[2];
	Fn[4 * DIM_STATE + 5] += 2 * (lp_Navi -> r_Wien[0] + lp_Navi -> r_Wenn[0]);
	Fn[4 * DIM_STATE + 6] += lp_Navi -> Fibn[2];
	Fn[4 * DIM_STATE + 8] += -lp_Navi -> Fibn[0];
    Fn[4 * DIM_STATE + 12] += lp_Navi -> Cnb[1];	
    Fn[4 * DIM_STATE + 13] += lp_Navi -> Cnb[4];
	Fn[4 * DIM_STATE + 14] += lp_Navi -> Cnb[7];
//	Fn[4 * DIM_STATE + 18] += lp_Navi -> Cnb[1] * Fibb[0];
//	Fn[4 * DIM_STATE + 19] += lp_Navi -> Cnb[4] * Fibb[1];
//	Fn[4 * DIM_STATE + 20] += lp_Navi -> Cnb[7] * Fibb[2];

	Fn[5 * DIM_STATE + 0] += 2 * lp_Navi -> Vn[1] * lp_Navi -> r_Wien[1] + 2 * lp_Navi -> Vn[0] * lp_Navi -> r_Wien[0] + lp_Navi -> r_Wenn[0] * lp_Navi -> Vn[0] * INVCOSLATI * INVCOSLATI;
	Fn[5 * DIM_STATE + 2] += lp_Navi -> invRn * lp_Navi -> invRn * (lp_Navi -> Vn[2] * lp_Navi -> Vn[1] - lp_Navi -> Vn[2] * lp_Navi -> Vn[0] * SINLATI * INVCOSLATI);
    Fn[5 * DIM_STATE + 3] += 2 * lp_Navi -> r_Wien[1] + lp_Navi -> r_Wenn[1];
	Fn[5 * DIM_STATE + 4] += -1 * (2 * lp_Navi -> r_Wien[0] + lp_Navi -> r_Wenn[0]);
	Fn[5 * DIM_STATE + 5] += lp_Navi -> invRm * (lp_Navi -> Vn[0] * SINLATI * INVCOSLATI - lp_Navi -> Vn[1]);
	Fn[5 * DIM_STATE + 6] += -lp_Navi -> Fibn[1];
	Fn[5 * DIM_STATE + 7] += lp_Navi -> Fibn[0];
	Fn[5 * DIM_STATE + 12] += lp_Navi -> Cnb[2];
	Fn[5 * DIM_STATE + 13] += lp_Navi -> Cnb[5];
	Fn[5 * DIM_STATE + 14] += lp_Navi -> Cnb[8];
	//Fn[5 * DIM_STATE + 18] += lp_Navi -> Cnb[2] * Fibb[0];
	//  Fn[5 * DIM_STATE + 19] += lp_Navi -> Cnb[5] * Fibb[1];
	//Fn[5 * DIM_STATE + 20] += lp_Navi -> Cnb[8] * Fibb[2];
	
	Fn[6 * DIM_STATE + 0] += -1 * lp_Navi -> r_Wien[1];
    Fn[6 * DIM_STATE + 2] += -lp_Navi -> Vn[2] * lp_Navi -> invRn * lp_Navi -> invRn;
	Fn[6 * DIM_STATE + 5] += lp_Navi -> invRn;

	Fn[6 * DIM_STATE + 7] += lp_Navi -> r_Wenn[2];	
    Fn[6 * DIM_STATE + 8] += -1 * (lp_Navi -> r_Wien[1] + lp_Navi -> r_Wenn[1]);
	Fn[6 * DIM_STATE + 9] += -lp_Navi -> Cnb[0];
	Fn[6 * DIM_STATE + 10] += -lp_Navi -> Cnb[3];
	Fn[6 * DIM_STATE + 11] += -lp_Navi -> Cnb[6];
	//Fn[6 * DIM_STATE + 15] += -lp_Navi -> Cnb[0] * r_Wibb[0];
	//Fn[6 * DIM_STATE + 16] += -lp_Navi -> Cnb[3] * r_Wibb[1];
	//Fn[6 * DIM_STATE + 17] += -lp_Navi -> Cnb[6] * r_Wibb[2];


    Fn[7 * DIM_STATE + 0] += lp_Navi ->r_Wien[0] + lp_Navi -> r_Wenn[0] * INVCOSLATI * INVCOSLATI;
	Fn[7 * DIM_STATE + 2] += -lp_Navi -> r_Wenn[1] * lp_Navi -> invRn;
	Fn[7 * DIM_STATE + 5] += lp_Navi ->invRn * SINLATI * INVCOSLATI;
	Fn[7 * DIM_STATE + 6] += -lp_Navi ->r_Wenn[2];
	Fn[7 * DIM_STATE + 8] += lp_Navi ->r_Wien[0] + lp_Navi ->r_Wenn[0];
	Fn[7 * DIM_STATE + 9] += -lp_Navi ->Cnb[1];
	Fn[7 * DIM_STATE + 10] += -lp_Navi -> Cnb[4];
	Fn[7 * DIM_STATE + 11] += -lp_Navi ->Cnb[7];
	//Fn[7 * DIM_STATE + 15] += -lp_Navi ->Cnb[1] * r_Wibb[0];	
	//Fn[7 * DIM_STATE + 16] += -lp_Navi ->Cnb[4] * r_Wibb[1];	
	//Fn[7 * DIM_STATE + 17] += -lp_Navi ->Cnb[7] * r_Wibb[2];


	Fn[8 * DIM_STATE + 2] += lp_Navi ->Vn[0] * lp_Navi -> invRm * lp_Navi -> invRm;
	Fn[8 * DIM_STATE + 3] += - lp_Navi ->invRm;
	Fn[8 * DIM_STATE + 6] += lp_Navi ->r_Wien[1] + lp_Navi ->r_Wenn[1];
	Fn[8 * DIM_STATE + 7] += -1 * (lp_Navi ->r_Wien[0] + lp_Navi ->r_Wenn[0]);
	Fn[8 * DIM_STATE + 9] += -lp_Navi ->Cnb[2];
	Fn[8 * DIM_STATE + 10] += -lp_Navi ->Cnb[5];		
	Fn[8 * DIM_STATE + 11] += -lp_Navi ->Cnb[8];
	//Fn[8 * DIM_STATE + 15] += -lp_Navi ->Cnb[2] * r_Wibb[0];
	//Fn[8 * DIM_STATE + 16] += -lp_Navi ->Cnb[5] * r_Wibb[1];	
	//Fn[8 * DIM_STATE + 17] += -lp_Navi ->Cnb[8] * r_Wibb[2];
	
	g_Kalman.ComputeFn_Count ++;
}

/*********************************************函数说明*******************************************************/
/*函数名称：KalCompute                                                                                      */
/*函数功能描述：完成一次完整的Kalman滤波计算                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：指针:lp_GNSSData                                                                                 */
/*输出变量：指针：lp_Kalman                                                                                 */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：该函数为Kalman滤波解算全流程的封装函数，用于实时滤波解算                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void KalCompute(p_GNSSData lp_GNSSData, p_Kalman lp_Kalman)
{
	//预测更新
	//ComputeFk(lp_Kalman -> Fn, lp_Kalman -> Fk);
	//Qk_Init(lp_Kalman -> Qk);
	
	//ComputeXkk_1(lp_Kalman -> Fk, lp_Kalman -> Xk, lp_Kalman -> Xkk_1);
	lp_Kalman -> State ++;
	if(lp_Kalman -> State ==1)
	{
		
		
		Rk_Init(lp_Kalman -> Rk,lp_Kalman -> Obv_Fit_Mode); 
		
		Hk_Init(lp_Kalman -> Hk,lp_Kalman -> Obv_Fit_Mode);
		
		ComputePkk_1_Step1(lp_Kalman);
	}
	else if(lp_Kalman -> State ==2)
	{		
		ComputePkk_1_Step2(lp_Kalman);
	}
	else if(lp_Kalman -> State ==3)
	{	
		ComputeKk(lp_Kalman -> Pkk_1, lp_Kalman -> Hk, lp_Kalman -> Rk, lp_Kalman -> Kk);
	}
  else if(lp_Kalman -> State ==4)
  {
		ComputePk(lp_Kalman -> Pkk_1, lp_Kalman -> Hk, lp_Kalman -> Kk,lp_Kalman -> Pk);
	}
	else
	{
		//量测更新
		ComputeZk(lp_GNSSData, lp_Kalman,lp_Kalman -> Obv_Fit_Mode);
		
		ComputeXk(lp_Kalman -> Xkk_1, lp_Kalman -> Hk, lp_Kalman -> Zk, lp_Kalman -> Kk, lp_Kalman -> Xk);
		
		lp_Kalman -> Kal_Count++;
	}
}

/*********************************************函数说明*******************************************************/
/*函数名称：KalPredict                                                                                      */
/*函数功能描述：完成一次完整的Kalman预测计算                                                               */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                            */
/*编写人：                                                                                                   */
/*输入变量：指针:lp_GNSSData                                                                                 */
/*输出变量：指针：lp_Kalman                                                                                 */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：该函数为Kalman滤波解算全流程的封装函数，用于实时滤波解算                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void KalPredict(p_Kalman lp_Kalman)
{
	INT32 i = 0;
	//预测更新
	//ComputeFk(lp_Kalman -> Fn, lp_Kalman -> Fk);

	ComputeXkk_1(lp_Kalman->Fk, lp_Kalman->Xk, lp_Kalman->Xkk_1);

	//ComputePkk_1(lp_Kalman->Fk, lp_Kalman->Pk, lp_Kalman->Qk, lp_Kalman->Pkk_1);

	/*for (i = 0; i < DIM_STATE * DIM_STATE; i++)
	{
		lp_Kalman->Pk[i] = lp_Kalman->Pkk_1[i];
	}*/
	for (i = 0; i < DIM_STATE; i++)
	{
		lp_Kalman->Xk[i] = lp_Kalman->Xkk_1[i];
	}
	lp_Kalman -> Kal_Predict_Count++;
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeFk                                                                                       */
/*函数功能描述：完成一步状态转移矩阵的计算                                                                  */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Fn                                                                                         */
/*输出变量：矩阵：Fk                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：该函数计算完成后，会将一步状态转移矩阵的累加部分清零，以便重新开始累加计算                         */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeFk(MATR Fn[DIM_STATE * DIM_STATE], MATR Fk[DIM_STATE * DIM_STATE])
{
	register IPARA i;
	register IPARA j;
	register DPARA Acc_E = 0.0;
	register DPARA Acc_N = 0.0;	
	register DPARA Acc_U = 0.0;
	
    if(g_Kalman.ComputeFn_Count != 0)
	{
		 Acc_E = Fn[4 * DIM_STATE + 6] / g_Kalman.ComputeFn_Count;
	     Acc_N = Fn[4 * DIM_STATE + 8] / g_Kalman.ComputeFn_Count;
		 Acc_U = Fn[3 * DIM_STATE + 8] / g_Kalman.ComputeFn_Count;		
		 g_Kalman.Acc_Horizontal = sqrt(Acc_E * Acc_E + Acc_N * Acc_N);
		 g_Kalman.Acc_All = sqrt(Acc_E * Acc_E + Acc_N * Acc_N + Acc_U * Acc_U);
	}
	else
	{
		 g_Kalman.Acc_Horizontal = 0.0;
		 g_Kalman.Acc_All = 0.0;
	}
	for(i = 0; i < DIM_STATE; i++)
	{
		for(j = 0; j < DIM_STATE; j++)
		{
			Fk[i * DIM_STATE + j] = Fn[i * DIM_STATE + j] * TIME_NAVI; 
			if(i == j)
			{
				Fk[i * DIM_STATE + j] += 1.0;		
			}
			Fn[i * DIM_STATE + j] = 0.0; //Fn对应元素被清零
		}
	}
	g_Kalman.ComputeFn_Count = 0;
}

/*********************************************????*******************************************************/
/*????:ComputeFk_2                                                                                       */
/*??????:?????????????                                                                  */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                 */
/*???:                                                                                              */
/*????:??:Fn                                                                                         */
/*????:??:Fk                                                                                        */
/*????:??                                                                                            */
/*??1:??????????????????                                                               */
/*??2:????????,?????????????????,??????????                         */
/*???:?                                                                                                */
/************************************************************************************************************/
void ComputeFk_2(MATR Fn[DIM_STATE * DIM_STATE], MATR Fk[DIM_STATE * DIM_STATE])
{
	register IPARA i;
	register IPARA j;
	register DPARA Acc_E = 0.0;
	register DPARA Acc_N = 0.0;
	register DPARA Acc_U = 0.0;
	
    if(g_Kalman.ComputeFn_Count != 0)
	{
		 Acc_E = Fn[4 * DIM_STATE + 6] / g_Kalman.ComputeFn_Count;
	     Acc_N = Fn[4 * DIM_STATE + 8] / g_Kalman.ComputeFn_Count;
		 Acc_U = Fn[3 * DIM_STATE + 8] / g_Kalman.ComputeFn_Count;		
		 g_Kalman.Acc_Horizontal = sqrt(Acc_E * Acc_E + Acc_N * Acc_N);
		 g_Kalman.Acc_All = sqrt(Acc_E * Acc_E + Acc_N * Acc_N + Acc_U * Acc_U);
	}
	else
	{
		 g_Kalman.Acc_Horizontal = 0.0;
		 g_Kalman.Acc_All = 0.0;
	}
	
	MATR Fn_Square[DIM_STATE * DIM_STATE] = { 0.0 };

	Mat_Mul(Fn, Fn,Fn_Square, DIM_STATE, DIM_STATE, DIM_STATE);

	//g_Kalman.Acc_Horizontal = sqrt(Acc_E * Acc_E + Acc_N * Acc_N);


	for (i = 0;i < DIM_STATE * DIM_STATE;i++)
	{
		Fk[i] = 0.0;
	}

	for (i = 0; i < DIM_STATE; i++)
	{
		for (j = 0; j < DIM_STATE; j++)
		{
			Fk[i * DIM_STATE + j] += Fn[i * DIM_STATE + j] * TIME_NAVI + 0.5 * Fn_Square[i * DIM_STATE + j] * TIME_NAVI * TIME_NAVI;
			if (i == j)
			{
				Fk[i * DIM_STATE + j] += 1.0;
			}
			Fn[i * DIM_STATE + j] = 0.0; //Fn???????
		}
	}
	g_Kalman.ComputeFn_Count = 0;
}
/*********************************************函数说明*******************************************************/
/*函数名称：ComputeXkk_1                                                                                    */
/*函数功能描述：完成一步预测误差状态向量的计算                                                              */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Fk、向量：Xk                                                                               */
/*输出变量：向量：Xkk_1                                                                                     */
/*测试用例：暂缺                                                                                            */
/*备注：该函数输入变量、输出变量均为地址传递                                                                */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeXkk_1(MATR const Fk[DIM_STATE * DIM_STATE], VEC const Xk[DIM_STATE],VEC Xkk_1[DIM_STATE])
{
	register IPARA i,j = 0;
	
	for (i = 0; i < DIM_STATE; i++)
	{
			Xkk_1[i] = 0.0;     //对应向量元素先清零
			for (j = 0; j < DIM_STATE; j++)
			{
				Xkk_1[i] += Fk[i * DIM_STATE + j] * Xk[j];
			}	
	}	
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputePkk_1                                                                                    */
/*函数功能描述：完成一步预测状态误差协方差矩阵的计算                                                        */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Fk、矩阵：Pk、矩阵：Qk                                                                     */
/*输出变量：矩阵：Pkk_1                                                                                     */
/*测试用例：暂缺                                                                                            */
/*备注：该函数输入变量、输出变量均为地址传递                                                                */
/*返回值：无                                                                                                */
/************************************************************************************************************/
//void ComputePkk_1(MATR Fk[DIM_STATE * DIM_STATE], MATR Pk[DIM_STATE * DIM_STATE],MATR const Qk[DIM_STATE * DIM_STATE],MATR Pkk_1[DIM_STATE * DIM_STATE])
//{
//		register IPARA i,j = 0;
//		
//		MATR TrFk[DIM_STATE * DIM_STATE];
//		MATR Temp[DIM_STATE * DIM_STATE];
//		
//		Mat_Tr(Fk, TrFk,DIM_STATE,DIM_STATE);
//		Mat_Mul(Fk,Pk,Temp, DIM_STATE, DIM_STATE, DIM_STATE);
//		Mat_Mul(Temp,TrFk,Pkk_1, DIM_STATE, DIM_STATE, DIM_STATE);
//		
//		for(i = 0; i < DIM_STATE; i++)
//		{
//			for(j = 0; j < DIM_STATE; j++)
//			{
//				Pkk_1[i * DIM_STATE + j] += Qk[i * DIM_STATE + j];	
//			}	
//		}
//}
void ComputePkk_1_Step1(p_Kalman lp_Kalman)
{
		//register IPARA i,j = 0;
		
		//MATR TrFk[DIM_STATE * DIM_STATE];
		//MATR Fk_Pk[DIM_STATE * DIM_STATE];
		
		Mat_Tr(lp_Kalman ->Fk , lp_Kalman ->TrFk,DIM_STATE,DIM_STATE);
		Mat_Mul(lp_Kalman ->Fk,lp_Kalman ->Pk,lp_Kalman ->Fk_Pk, DIM_STATE, DIM_STATE, DIM_STATE);
}
void ComputePkk_1_Step2(p_Kalman lp_Kalman)
{
		register IPARA i,j = 0;
			
		Mat_Mul(lp_Kalman ->Fk_Pk,lp_Kalman ->TrFk,lp_Kalman ->Pkk_1, DIM_STATE, DIM_STATE, DIM_STATE);
		
		for(i = 0; i < DIM_STATE; i++)
		{
			for(j = 0; j < DIM_STATE; j++)
			{
				lp_Kalman ->Pkk_1[i * DIM_STATE + j] += lp_Kalman ->Qk[i * DIM_STATE + j];	
			}	
		}
}
/*********************************************函数说明*******************************************************/
/*函数名称：ComputeKk                                                                                       */
/*函数功能描述：完成预测误差修正矩阵的计算                                                                  */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Pkk_1、矩阵：Hk、矩阵：Rk                                                                  */
/*输出变量：矩阵：Kk                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注：该函数输入变量、输出变量均为地址传递                                                                */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeKk(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV], MATR Kk[DIM_STATE * DIM_MAX_OBV])
{
		register IPARA i,j = 0;
		//BOOL isErr;
		MATR Temp1[DIM_STATE * DIM_MAX_OBV];
		MATR Temp2[DIM_MAX_OBV * DIM_MAX_OBV];
		MATR TrHk[DIM_STATE * DIM_MAX_OBV ];
		
		Mat_Tr(Hk,TrHk,g_Kalman.Actual_Dim_Obv,DIM_STATE);
		Mat_Mul(Pkk_1, TrHk,Temp1,DIM_STATE,DIM_STATE,g_Kalman.Actual_Dim_Obv);
		Mat_Mul(Hk, Temp1,Temp2,g_Kalman.Actual_Dim_Obv,DIM_STATE,g_Kalman.Actual_Dim_Obv);
		for(i = 0; i < g_Kalman.Actual_Dim_Obv ; i++)
		{
			for(j = 0; j < g_Kalman.Actual_Dim_Obv ; j++)
			{
				Temp2[i * g_Kalman.Actual_Dim_Obv + j] += Rk[i * g_Kalman.Actual_Dim_Obv + j];
			}	
		}
		Mat_Inv(Temp2,Temp2,g_Kalman.Actual_Dim_Obv);
		Mat_Mul(Temp1,Temp2,Kk,DIM_STATE,g_Kalman.Actual_Dim_Obv,g_Kalman.Actual_Dim_Obv);
		//return isErr;

}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeKkTest                                                                                   */
/*函数功能描述：完成预测误差修正矩阵的计算（仅供测试）                                                      */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Pkk_1、矩阵：Hk、矩阵：Rk                                                                  */
/*输出变量：矩阵：Kk                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：此函数用于某些低成本平台降低矩阵计算量用，仅供测试                                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeKkTest(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR const Rk[DIM_MAX_OBV * DIM_MAX_OBV], MATR Kk[DIM_STATE * DIM_MAX_OBV])
{
		register IPARA i,j = 0;
		MATR Temp1[DIM_STATE * DIM_MAX_OBV];
		MATR Temp2[DIM_MAX_OBV * DIM_MAX_OBV];
		MATR TrHk[DIM_STATE * DIM_MAX_OBV ];
		
		Mat_Tr(Hk,TrHk,g_Kalman.Actual_Dim_Obv,DIM_STATE);
		Mat_Mul(Pkk_1, TrHk,Temp1,DIM_STATE,DIM_STATE,g_Kalman.Actual_Dim_Obv);
		//Mat_Mul(Hk, Temp1,Temp2,g_Kalman.Actual_Dim_Obv,DIM_STATE,g_Kalman.Actual_Dim_Obv);
		for(i = 0; i < g_Kalman.Actual_Dim_Obv ; i++)
		{
			for(j = 0; j < g_Kalman.Actual_Dim_Obv ; j++)
			{
				Temp2[ i * g_Kalman.Actual_Dim_Obv + j] = Pkk_1[i * DIM_STATE + j]+ Rk[i * g_Kalman.Actual_Dim_Obv + j];					
			}	
		}
		Mat_Inv(Temp2,Temp2,g_Kalman.Actual_Dim_Obv);
		Mat_Mul(Temp1,Temp2,Kk,DIM_STATE,g_Kalman.Actual_Dim_Obv,g_Kalman.Actual_Dim_Obv);
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputePk                                                                                       */
/*函数功能描述：完成状态误差协方差矩阵的计算                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Pkk_1、矩阵：Hk、矩阵：Kk                                                                  */
/*输出变量：矩阵：Pk                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注：该函数输入变量、输出变量均为地址传递                                                                */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputePk(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR Kk[DIM_STATE * DIM_MAX_OBV], MATR Pk[DIM_STATE * DIM_STATE])
{
	register IPARA i,j = 0;
	MATR Temp[DIM_STATE * DIM_STATE];
	 
	Mat_Mul(Kk,Hk,Temp,DIM_STATE,g_Kalman.Actual_Dim_Obv,DIM_STATE);	
	
	for(i = 0; i < DIM_STATE ; i++)
	{
		for(j = 0; j < DIM_STATE ; j++)
		{
			Temp[i * DIM_STATE + j] = -1 * Temp[i * DIM_STATE + j];
			if(i == j)
			{
				Temp[i * DIM_STATE + j] += 1.0;
			}
		}
	}

	Mat_Mul(Temp,Pkk_1,Pk,DIM_STATE,DIM_STATE,DIM_STATE);		
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputePkPosDef                                                                                 */
/*函数功能描述：完成状态误差协方差矩阵的计算（仅供测试）                                                    */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：矩阵:Pkk_1、矩阵：Hk、矩阵：Kk、矩阵：Rk                                                        */
/*输出变量：矩阵：Pk                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：此函数用于长航时导航计算，可以长时间保证Pk矩阵的正定性不受舍入误差影响，但计算量增大，仅供测试     */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputePkPosDef(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR Kk[DIM_STATE * DIM_MAX_OBV],MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV],MATR Pk[DIM_STATE * DIM_STATE])
{
	register IPARA i,j = 0;
	MATR Temp1[DIM_STATE * DIM_STATE];
	MATR TrTemp1[DIM_STATE * DIM_STATE]; 
	MATR Temp2[DIM_STATE * DIM_STATE];
	MATR Temp3[DIM_STATE * DIM_MAX_OBV];
	MATR Temp4[DIM_STATE * DIM_STATE];
	MATR TrKk[DIM_MAX_OBV * DIM_STATE];
	
	Mat_Mul(Kk,Hk,Temp1,DIM_STATE,g_Kalman.Actual_Dim_Obv,DIM_STATE);	
	Mat_Tr(Kk,TrKk,DIM_STATE,g_Kalman.Actual_Dim_Obv);
		for(i = 0; i < DIM_STATE ; i++)
		{
			for(j = 0; j < DIM_STATE ; j++)
			{
				Temp1[i * DIM_STATE + j] = -1 * Temp1[i * DIM_STATE + j];
				if(i == j)
				{
						Temp1[i * DIM_STATE + j] += 1.0;
				}
			}
		}
	Mat_Tr(Temp1,TrTemp1,DIM_STATE,DIM_STATE);
	Mat_Mul(Temp1,Pkk_1,Temp2,DIM_STATE,DIM_STATE,DIM_STATE);	
	Mat_Mul(Temp2,TrTemp1,Pk,DIM_STATE,DIM_STATE,DIM_STATE);
	Mat_Mul(Kk,Rk,Temp3,DIM_STATE,g_Kalman.Actual_Dim_Obv,g_Kalman.Actual_Dim_Obv);	
	Mat_Mul(Temp3,TrKk,Temp4,DIM_STATE,g_Kalman.Actual_Dim_Obv,DIM_STATE);	
	for(i = 0; i < DIM_STATE ; i++)
	{
		for(j = 0; j < DIM_STATE ; j++)
		{
			Pk[i * DIM_STATE + j] += Temp4[i * DIM_STATE + j];
		}
	}	 
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeZk                                                                                       */
/*函数功能描述：完成量测向量的计算                                                                          */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：指针:lp_GNSSData                                                                                */
/*输出变量：指针：lp_Kalman                                                                                 */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：请确保计算Zk前已GNSS数据及GNSS时刻的惯导数据已分别存储于相应的结构体中                             */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeZk(p_GNSSData const lp_GNSSData, p_Kalman lp_Kalman,MODE Obv_Fit_Mode)
{
		switch(Obv_Fit_Mode)        
		{	
			case POS_FIT :
			{				  					 														
				lp_Kalman -> Zk[0] = lp_Kalman -> r_InsLati - lp_GNSSData ->r_GNSSLati;
		        lp_Kalman -> Zk[1] = lp_Kalman -> r_InsLogi - lp_GNSSData ->r_GNSSLogi;	
				if(lp_Kalman -> Zk[1] < -PI)
				{
					 lp_Kalman -> Zk[1] += 2 * PI;
				}
				else if(lp_Kalman -> Zk[1] > PI)
				{
					 lp_Kalman -> Zk[1] -= 2 * PI;
				}
                lp_Kalman -> Zk[2] = lp_Kalman ->InsHeight - lp_GNSSData->GNSSHeight;				
				break;			
			}
			case VEL_FIT :
			{		 				
				lp_Kalman -> Zk[0] = lp_Kalman -> InsVn[0] - lp_GNSSData->GNSSVn[0];
		        lp_Kalman -> Zk[1] = lp_Kalman -> InsVn[1] - lp_GNSSData->GNSSVn[1];			
                lp_Kalman -> Zk[2] = lp_Kalman -> InsVn[2] - lp_GNSSData->GNSSVn[2];				
			    break;	
			}
			case POSVEL_FIT :
			{					 
				lp_Kalman -> Zk[0] = lp_Kalman -> r_InsLati - lp_GNSSData ->r_GNSSLati;
		        lp_Kalman -> Zk[1] = lp_Kalman -> r_InsLogi - lp_GNSSData ->r_GNSSLogi;	
				if(lp_Kalman -> Zk[1] < -PI)
				{
					 lp_Kalman -> Zk[1] += 2 * PI;
				}
				else if(lp_Kalman -> Zk[1] > PI)
				{
					 lp_Kalman -> Zk[1] -= 2 * PI;
				}
                lp_Kalman -> Zk[2] = lp_Kalman ->InsHeight - lp_GNSSData->GNSSHeight;

                lp_Kalman -> Zk[3] = lp_Kalman -> InsVn[0] - lp_GNSSData->GNSSVn[0];
		        lp_Kalman -> Zk[4] = lp_Kalman -> InsVn[1] - lp_GNSSData->GNSSVn[1];			
                lp_Kalman -> Zk[5] = lp_Kalman -> InsVn[2] - lp_GNSSData->GNSSVn[2];				
				break;
			}
			case POSVELHEAD_FIT :
			{		
                lp_Kalman -> Zk[0] = lp_Kalman -> r_InsLati - lp_GNSSData ->r_GNSSLati;
		        lp_Kalman -> Zk[1] = lp_Kalman -> r_InsLogi - lp_GNSSData ->r_GNSSLogi;	
				if(lp_Kalman -> Zk[1] < -PI)
				{
					 lp_Kalman -> Zk[1] += 2 * PI;
				}
				else if(lp_Kalman -> Zk[1] > PI)
				{
					 lp_Kalman -> Zk[1] -= 2 * PI;
				}
                lp_Kalman -> Zk[2] = lp_Kalman ->InsHeight - lp_GNSSData->GNSSHeight;

                lp_Kalman -> Zk[3] = lp_Kalman -> InsVn[0] - lp_GNSSData->GNSSVn[0];
		        lp_Kalman -> Zk[4] = lp_Kalman -> InsVn[1] - lp_GNSSData->GNSSVn[1];			
                lp_Kalman -> Zk[5] = lp_Kalman -> InsVn[2] - lp_GNSSData->GNSSVn[2];
                lp_Kalman -> Zk[6] = lp_GNSSData -> r_GPSHead - lp_Kalman -> r_InsFai;
                if(lp_Kalman -> Zk[6] < -PI)
				{
					 lp_Kalman -> Zk[6] += 2 * PI;
				}
				else if(lp_Kalman -> Zk[6] > PI)
				{
					 lp_Kalman -> Zk[6] -= 2 * PI;
				}				
				break;
			}
			case POS_HEAD_FIT :
			{		
                lp_Kalman -> Zk[0] = lp_Kalman -> r_InsLati - lp_GNSSData ->r_GNSSLati;
		        lp_Kalman -> Zk[1] = lp_Kalman -> r_InsLogi - lp_GNSSData ->r_GNSSLogi;	
				if(lp_Kalman -> Zk[1] < -PI)
				{
					 lp_Kalman -> Zk[1] += 2 * PI;
				}
				else if(lp_Kalman -> Zk[1] > PI)
				{
					 lp_Kalman -> Zk[1] -= 2 * PI;
				}
                lp_Kalman -> Zk[2] = lp_Kalman ->InsHeight - lp_GNSSData->GNSSHeight;	
				lp_Kalman -> Zk[3] = lp_GNSSData -> r_GPSHead - lp_Kalman -> r_InsFai;
                if(lp_Kalman -> Zk[3] < -PI)
				{
					 lp_Kalman -> Zk[3] += 2 * PI;
				}
				else if(lp_Kalman -> Zk[3] > PI)
				{
					 lp_Kalman -> Zk[3] -= 2 * PI;
				}	
				break;
			}
			case VEL_HEAD_FIT :
			{		
                lp_Kalman -> Zk[0] = lp_Kalman -> InsVn[0] - lp_GNSSData->GNSSVn[0];
		        lp_Kalman -> Zk[1] = lp_Kalman -> InsVn[1] - lp_GNSSData->GNSSVn[1];			
                lp_Kalman -> Zk[2] = lp_Kalman -> InsVn[2] - lp_GNSSData->GNSSVn[2];			
				lp_Kalman -> Zk[3] = lp_GNSSData -> r_GPSHead - lp_Kalman -> r_InsFai;
                if(lp_Kalman -> Zk[3] < -PI)
				{
					 lp_Kalman -> Zk[3] += 2 * PI;
				}
				else if(lp_Kalman -> Zk[3] > PI)
				{
					 lp_Kalman -> Zk[3] -= 2 * PI;
				}	
				break;
			}
			default:
			{
				lp_Kalman -> Zk[0] = lp_Kalman -> r_InsLati - lp_GNSSData ->r_GNSSLati;
		        lp_Kalman -> Zk[1] = lp_Kalman -> r_InsLogi - lp_GNSSData ->r_GNSSLogi;	
				if(lp_Kalman -> Zk[1] < -PI)
				{
					 lp_Kalman -> Zk[1] += 2 * PI;
				}
				else if(lp_Kalman -> Zk[1] > PI)
				{
					 lp_Kalman -> Zk[1] -= 2 * PI;
				}
                lp_Kalman -> Zk[2] = lp_Kalman ->InsHeight - lp_GNSSData->GNSSHeight;						
				break;
			} 
  }
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeXk                                                                                       */
/*函数功能描述：完成量测向量的计算                                                                          */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：向量:Xkk_1、矩阵Hk、向量Zk，矩阵Kk                                                              */
/*输出变量：向量：Xk                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeXk(VEC Xkk_1[DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], VEC const Zk[DIM_MAX_OBV],MATR Kk[DIM_STATE * DIM_MAX_OBV],VEC Xk[DIM_STATE])
{
	register IPARA i;
	
	VEC Temp1[DIM_MAX_OBV];
	VEC Temp2[DIM_MAX_OBV];
	VEC Temp3[DIM_STATE];
	
	Mat_Mul(Hk,Xkk_1,Temp1,g_Kalman.Actual_Dim_Obv,DIM_STATE,1);
		
	for(i = 0; i < g_Kalman.Actual_Dim_Obv; i++)
	{
			Temp2[i] = Zk[i] - Temp1[i];	
	}
	
	Mat_Mul(Kk,Temp2,Temp3,DIM_STATE,g_Kalman.Actual_Dim_Obv,1);
	
	for(i = 0; i < DIM_STATE; i++)
	{
			Xk[i] = Xkk_1[i] + Temp3[i];	
	}
}

/*********************************************函数说明*******************************************************/
/*函数名称：SaveGNSSData                                                                                     */
/*函数功能描述：保存DVL、GNSS数据抵达时刻的惯导数据                                                         */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：指针:lp_Navi、指针：lp_Kalman                                                                   */
/*输出变量：                                                                                                */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void SaveINSData(p_Navi const lp_Navi, p_Kalman lp_Kalman)
{
	
	lp_Kalman -> InsVn[0] = lp_Navi -> Vn[0]; 
	 				
	lp_Kalman -> InsVn[1] = lp_Navi -> Vn[1];
	 				
	lp_Kalman -> InsVn[2] = lp_Navi -> Vn[2]; 
	
	lp_Kalman -> r_InsFai = lp_Navi -> r_Atti[0]; 	
	
	lp_Kalman -> r_InsLati = lp_Navi -> r_Lati;
	
	lp_Kalman -> r_InsLogi = lp_Navi -> r_Logi;
	
	lp_Kalman -> InsHeight = lp_Navi -> Height;
}

/*********************************************函数说明*******************************************************/
/*函数名称：ErrStore_1s                                                                                  */
/*函数功能描述：完成一次误差校正计算                                                                        */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：指针:lp_Kalman                                                                                  */
/*输出变量：指针：lp_Navi                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：该函数为误差修正的封装函数，用于实时误差修正                                                       */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ErrStore_1s(p_Navi lp_Navi, p_Kalman lp_Kalman, p_GNSSData lp_GNSSData)
{
	  lp_Navi -> Correct_Count = (int)(TIME_FILTER / TIME_NAVI);
	  //pos  
	  if((lp_Kalman->Obv_Fit_Mode == VEL_FIT)||(lp_Kalman->Obv_Fit_Mode == VEL_HEAD_FIT))
	  {
			lp_Navi -> r_Lati_Err	= lp_Kalman -> r_InsLati - lp_GNSSData  -> r_GNSSLati;
			lp_Navi -> r_Logi_Err = lp_Kalman -> r_InsLogi - lp_GNSSData  -> r_GNSSLogi;
		    lp_Navi -> Height_Err = lp_Kalman -> InsHeight - lp_GNSSData  ->GNSSHeight; 
			
			if(lp_Navi -> r_Logi_Err > PI)
				lp_Navi -> r_Logi_Err -= 2 * PI;
			else if(lp_Navi -> r_Logi_Err < -PI)
				lp_Navi -> r_Logi_Err += 2 * PI;
			
			
			if(fabs(lp_Navi -> r_Lati_Err * lp_Navi -> Rm) >= 1.0)
				lp_Navi -> K_Correct_Lati = 0.15;
			else if((fabs(lp_Navi -> r_Lati_Err * lp_Navi -> Rm) < 1.0) && (fabs(lp_Navi -> r_Lati_Err * lp_Navi -> Rm) >= 0.5))
				lp_Navi -> K_Correct_Lati = 0.47;
			else if((fabs(lp_Navi -> r_Lati_Err * lp_Navi -> Rm) < 0.5) && (fabs(lp_Navi -> r_Lati_Err * lp_Navi -> Rm) >= 0.3))
				lp_Navi -> K_Correct_Lati = 0.68;
			else if((fabs(lp_Navi -> r_Lati_Err * lp_Navi -> Rm) < 0.3) && (fabs(lp_Navi -> r_Lati_Err * lp_Navi -> Rm) >= 0.2))
				lp_Navi -> K_Correct_Lati = 0.85;
			else
				lp_Navi -> K_Correct_Lati = 0.98;
			
			if(fabs(lp_Navi -> r_Logi_Err * cos(lp_Navi -> r_Lati) * lp_Navi -> Rn) >= 1.0)
				lp_Navi -> K_Correct_Logi = 0.15;
			else if((fabs(lp_Navi -> r_Logi_Err * cos(lp_Navi -> r_Lati) * lp_Navi -> Rn) < 1.0) && (fabs(lp_Navi -> r_Logi_Err * cos(lp_Navi -> r_Lati) * lp_Navi -> Rn) >= 0.5))
				lp_Navi -> K_Correct_Logi = 0.47;
			else if((fabs(lp_Navi -> r_Logi_Err * cos(lp_Navi -> r_Lati) * lp_Navi -> Rn) < 0.5) && (fabs(lp_Navi -> r_Logi_Err * cos(lp_Navi -> r_Lati) * lp_Navi -> Rn) >= 0.3))
				lp_Navi -> K_Correct_Logi = 0.68;
			else if((fabs(lp_Navi -> r_Logi_Err * cos(lp_Navi -> r_Lati) * lp_Navi -> Rn) < 0.3) && (fabs(lp_Navi -> r_Logi_Err * cos(lp_Navi -> r_Lati) * lp_Navi -> Rn) >= 0.2))
				lp_Navi -> K_Correct_Logi = 0.85;
			else
				lp_Navi -> K_Correct_Logi = 0.98;

 			if(fabs(lp_Navi -> Height_Err) >= 5.0)
				lp_Navi -> K_Correct_Height = 0.15;
			else if((fabs(lp_Navi -> Height_Err) < 5.0) && (fabs(lp_Navi -> Height_Err) >= 2.5))
				lp_Navi -> K_Correct_Height = 0.47;
			else if((fabs(lp_Navi -> Height_Err) < 2.5) && (fabs(lp_Navi -> Height_Err) >= 1.5))
				lp_Navi -> K_Correct_Height = 0.68;
			else if((fabs(lp_Navi -> Height_Err) < 0.5) && (fabs(lp_Navi -> Height_Err) >= 1.0))
				lp_Navi -> K_Correct_Height = 0.85;
			else
				lp_Navi -> K_Correct_Height = 0.98;
			
		}
		else
		{
			lp_Navi -> r_Lati_Err = lp_Kalman -> Xk[0];
			lp_Navi -> r_Logi_Err = lp_Kalman -> Xk[1];
			lp_Navi -> Height_Err = lp_Kalman -> Xk[2];
		}
		
		
		
		//Horizontal V
		lp_Navi -> VnErr[0] = lp_Kalman -> Xk[3];
	    lp_Navi -> VnErr[1] = lp_Kalman -> Xk[4];
		lp_Navi -> VnErr[2] = lp_Kalman -> Xk[5];
		
		//Atti
		lp_Navi -> r_AttiErr_n[1] = lp_Kalman -> Xk[7];
		
//        if(fabs(lp_Kalman -> Xk[6])>5 * D2R)
//		{
//			int a;
//			a = 0;
//		
//		}
//		if(fabs(lp_Kalman -> Xk[8])>5 * D2R)
//		{
//			int a;
//			a = 0;
//		
//		}	
		if((g_Kalman.Acc_Horizontal >= THRESHOLD_HORIZONTAL_ACC)||(g_Kalman.Kal_Count<= (int)(5 / TIME_FILTER))||(g_Kalman.Acc_All <= THRESHOLD_ALL_ACC))
		{
			lp_Navi -> r_AttiErr_n[0] = 0.0;	
			lp_Navi -> r_AttiErr_n[2] = 0.0;	
			//滤波器对应变量清0
			lp_Kalman -> Xk[6] = 0.0;	
			lp_Kalman -> Xk[8] = 0.0;
			          			
		}	
	  else
		{
			lp_Navi -> r_AttiErr_n[0] = lp_Kalman -> Xk[6];	
			lp_Navi -> r_AttiErr_n[2] = lp_Kalman -> Xk[8];

		}
        
		
//		if((g_Kalman.Acc_Horizontal >= THRESHOLD_HORIZONTAL_ACC)&&(g_Kalman.Kal_Count<= 100))//临时添加100s
//		{
//			lp_Navi -> r_AttiErr_n[0] = 0.0;	
//			lp_Navi -> r_AttiErr_n[2] = 0.0;	
//		}
//		else
//		{
//			lp_Navi -> r_AttiErr_n[0] = lp_Kalman -> Xk[2];	
//			lp_Navi -> r_AttiErr_n[2] = lp_Kalman -> Xk[4];
//		}
		
}


/*********************************************函数说明*******************************************************/
/*函数名称：ErrCorrect_1_Navi_Time                                                                          */
/*函数功能描述：完成一次误差校正计算                                                                        */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：指针:lp_Kalman                                                                                  */
/*输出变量：指针：lp_Navi                                                                                   */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：该函数为误差修正的封装函数，用于实时误差修正                                                       */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ErrCorrect_1_Navi_Time(p_Navi lp_Navi, p_Kalman lp_Kalman)
{
	  VEC VnErr[3]; //
	  ATTI r_AttiErr_b[3];
	  ATTI r_AttiErr_n[3];
	  //ANGRATE EstGyroBias[3];
	  //ACCELER EstAccBias[3];
	  
	  //位置误差修正
	  if((lp_Kalman->Obv_Fit_Mode == VEL_FIT)||(lp_Kalman->Obv_Fit_Mode == VEL_HEAD_FIT))
	  {
			  lp_Navi -> r_Lati  -= lp_Navi -> K_Correct_Lati * lp_Navi -> r_Lati_Err * TIME_NAVI / TIME_FILTER;
			  lp_Navi -> r_Logi  -= lp_Navi -> K_Correct_Logi * lp_Navi -> r_Logi_Err * TIME_NAVI / TIME_FILTER;
			  lp_Navi -> Height  -= lp_Navi -> K_Correct_Height * lp_Navi -> Height_Err * TIME_NAVI / TIME_FILTER;
		  
		      lp_Kalman -> Xk[0]  -= lp_Navi -> K_Correct_Lati * lp_Navi -> r_Lati_Err * TIME_NAVI / TIME_FILTER;
			  lp_Kalman -> Xk[1]  -= lp_Navi -> K_Correct_Logi * lp_Navi -> r_Logi_Err * TIME_NAVI / TIME_FILTER;
			  lp_Kalman -> Xk[2]  -= lp_Navi -> K_Correct_Height * lp_Navi -> Height_Err * TIME_NAVI / TIME_FILTER;		      
	  }
	  else
	  {
			lp_Navi -> r_Lati  -= lp_Navi -> r_Lati_Err * TIME_NAVI / TIME_FILTER;
			lp_Navi -> r_Logi  -= lp_Navi -> r_Logi_Err * TIME_NAVI / TIME_FILTER;
			lp_Navi -> Height  -= lp_Navi -> Height_Err * TIME_NAVI / TIME_FILTER;	 
            
		    lp_Kalman -> Xk[0]  -= lp_Navi -> r_Lati_Err * TIME_NAVI / TIME_FILTER;
			lp_Kalman -> Xk[1]  -= lp_Navi -> r_Logi_Err * TIME_NAVI / TIME_FILTER;
			lp_Kalman -> Xk[2]  -= lp_Navi -> Height_Err * TIME_NAVI / TIME_FILTER;
            		  
	  }
	  //速度误差校正
		if(lp_Kalman -> isVnCorrect == YES)
		{
			VnErr[0] = lp_Navi -> VnErr[0] * TIME_NAVI / TIME_FILTER;
			VnErr[1] = lp_Navi -> VnErr[1] * TIME_NAVI / TIME_FILTER;
			VnErr[2] = lp_Navi -> VnErr[2] * TIME_NAVI / TIME_FILTER;
			
			CorrectVn(lp_Navi -> Vn, VnErr);
			//减去已修正部分
			lp_Kalman -> Xk[3] -= VnErr[0];
			lp_Kalman -> Xk[4] -= VnErr[1];
			lp_Kalman -> Xk[5] -= VnErr[2];
		}
	 
		
		//姿态误差修正
		if(lp_Kalman -> isAttiCorrect == YES)
		{
			
				r_AttiErr_n[1] = 0.0;
				r_AttiErr_n[2] = 0.0;	
				r_AttiErr_n[0] = lp_Navi -> r_AttiErr_n[0] * TIME_NAVI / TIME_FILTER;//北向失准角
				lp_Kalman -> Xk[6] -= r_AttiErr_n[0];//减去已修正部分
				
				Mat_Mul(lp_Navi -> Cnb, r_AttiErr_n , r_AttiErr_b, 3, 3, 1);
				CorrectAtti(lp_Navi -> Q, r_AttiErr_b);
			
				r_AttiErr_n[0] = 0.0;
				r_AttiErr_n[1] = 0.0;
				r_AttiErr_n[2] = lp_Navi -> r_AttiErr_n[2] * TIME_NAVI / TIME_FILTER;//东向失准角
				lp_Kalman -> Xk[8] -= r_AttiErr_n[2];//减去已修正部分
				
				Mat_Mul(lp_Navi -> Cnb, r_AttiErr_n , r_AttiErr_b, 3, 3, 1);
				CorrectAtti(lp_Navi -> Q, r_AttiErr_b);	
		}
		//航向误差修正
		if((lp_Kalman -> Obv_Fit_Mode == POS_HEAD_FIT) || ((lp_Kalman -> Obv_Fit_Mode == POSVELHEAD_FIT))||(lp_Kalman -> Obv_Fit_Mode == VEL_HEAD_FIT))
		{
			
				r_AttiErr_n[0] = 0.0;
				r_AttiErr_n[2] = 0.0;
				r_AttiErr_n[1] = lp_Navi -> r_AttiErr_n[1] * TIME_NAVI / TIME_FILTER;//天向失准角
				lp_Kalman -> Xk[7] -= r_AttiErr_n[1];//减去已修正部分
				
				Mat_Mul(lp_Navi -> Cnb, r_AttiErr_n , r_AttiErr_b, 3, 3, 1);
				CorrectAtti(lp_Navi -> Q, r_AttiErr_b);
			
	    }
		lp_Navi -> Correct_Count--;
}
/*********************************************????*******************************************************/
/*????:ErrStore_1s                                                                                  */
/*??????:??????????                                                                        */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                                           */
/*???:                                                                                                  */
/*????:??:lp_Kalman                                                                                  */
/*????:??:lp_Navi                                                                                   */
/*????:??                                                                                            */
/*??1:??????????????????                                                               */
/*??2:?????????????,????????                                                       */
/*???:?                                                                                                */
/************************************************************************************************************/
void ErrCorrect_1_Navi_Time_For_INS(p_Navi lp_Navi, p_Kalman lp_Kalman)
{
	DPARA r_AttiErr_n[3],r_AttiErr_b[3];
	
	//lp_Navi -> r_Lati_Err = lp_Kalman -> Xk[0];
	//lp_Navi -> r_Logi_Err = lp_Kalman -> Xk[1];
	//lp_Navi -> Height_Err = lp_Kalman -> Xk[2];
	
	lp_Navi -> r_Lati  -= lp_Kalman -> Xk[0];
	lp_Navi -> r_Logi  -= lp_Kalman -> Xk[1];
	lp_Navi -> Height  -= lp_Kalman -> Xk[2];
	
	lp_Kalman->Xk[0] = 0.0;
    lp_Kalman->Xk[1] = 0.0;
	lp_Kalman->Xk[2] = 0.0;
	
	
	lp_Navi->VnErr[0] = lp_Kalman->Xk[3];
	lp_Navi->VnErr[1] = lp_Kalman->Xk[4];
	lp_Navi->VnErr[2] = lp_Kalman->Xk[5];	
	CorrectVn(lp_Navi->Vn, lp_Navi -> VnErr);
	lp_Kalman->Xk[3] = 0.0;
    lp_Kalman->Xk[4] = 0.0;
	lp_Kalman->Xk[5] = 0.0;
	
	g_Navi.r_AttiErr_n[0] = g_Kalman.Xk[6];
	g_Navi.r_AttiErr_n[1] = g_Kalman.Xk[7];
	g_Navi.r_AttiErr_n[2] = g_Kalman.Xk[8];

	r_AttiErr_n[1] = 0.0;
	r_AttiErr_n[2] = 0.0;
	r_AttiErr_n[0] = g_Navi.r_AttiErr_n[0];//?????
	g_Kalman.Xk[6] = 0.0;//???????

	Mat_Mul(g_Navi.Cnb, r_AttiErr_n, r_AttiErr_b, 3, 3, 1);
	CorrectAtti(g_Navi.Q, r_AttiErr_b);

	r_AttiErr_n[0] = 0.0;
	r_AttiErr_n[1] = 0.0;
	r_AttiErr_n[2] = g_Navi.r_AttiErr_n[2];//?????
	g_Kalman.Xk[8] = 0.0;//???????

	Mat_Mul(g_Navi.Cnb, r_AttiErr_n, r_AttiErr_b, 3, 3, 1);
	CorrectAtti(g_Navi.Q, r_AttiErr_b);
	
//	r_AttiErr_n[0] = 0.0;
//	r_AttiErr_n[2] = 0.0;
//	r_AttiErr_n[1] = g_Navi.r_AttiErr_n[1];//?????
//	g_Kalman.Xk[7] = 0.0;//???????

//	Mat_Mul(g_Navi.Cnb, r_AttiErr_n, r_AttiErr_b, 3, 3, 1);
//	CorrectAtti(g_Navi.Q, r_AttiErr_b);
	
}
/*********************************************????*******************************************************/
/*????:ErrCorrect_1_Navi_Time                                                                          */
/*??????:??????????                                                                        */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                                           */
/*???:                                                                                                  */
/*????:??:lp_Kalman                                                                                  */
/*????:??:lp_Navi                                                                                   */
/*????:??                                                                                            */
/*??1:??????????????????                                                               */
/*??2:?????????????,????????                                                       */
/*???:?                                                                                                */
/************************************************************************************************************/
/*void ErrCorrect_1_Navi_Time_For_INS(p_Navi lp_Navi, p_Kalman lp_Kalman)
{
	VEC VnErr[3]; //
	//ATTI r_AttiErr_b[3];
	//ATTI r_AttiErr_n[3];
	//??????
	if (lp_Kalman->isVnCorrect == YES)
	{
		VnErr[0] = lp_Navi->VnErr[0] * TIME_NAVI;
		VnErr[1] = 0.0;
		VnErr[2] = lp_Navi->VnErr[2] * TIME_NAVI;

		CorrectVn(lp_Navi->Vn, VnErr);
		//???????
		lp_Kalman->Xk[0] = lp_Kalman->Xk[0] - lp_Navi->VnErr[0] * TIME_NAVI;
		//lp_Kalman -> Xk[1] = 0.0;
		lp_Kalman->Xk[1] = lp_Kalman->Xk[1] - lp_Navi->VnErr[2] * TIME_NAVI;
	}
	lp_Navi->Correct_Count--;
}*/
/*********************************************函数说明*******************************************************/
/*函数名称：CorrectPos                                                                                      */
/*函数功能描述：完成位置误差的校正                                                                          */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：向量:PosErr                                                                                     */
/*输出变量：纬度：lp_InsLati、经度：lp_InsLogi、高度：lp_InsHeight                                          */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：误差修正量PosErr在修正完成后会被清零                                                               */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void CorrectPos(LATI *lp_InsLati, LOGI *lp_InsLogi, HEIGHT *lp_InsHeight, VEC PosErr[3])
{
	register IPARA i;
	*lp_InsLati -= PosErr[0];
	*lp_InsLogi -= PosErr[1];
	*lp_InsHeight -= PosErr[2];
	
	for (i = 0; i < 3; i++)
	{
		PosErr[i] = 0.0;
	}
}

/*********************************************函数说明*******************************************************/
/*函数名称：CorrectVn                                                                                       */
/*函数功能描述：完成速度误差的校正                                                                          */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：向量:VnErr                                                                                      */
/*输出变量：速度：Vn                                                                                        */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：误差修正量VnErr在修正完成后会被清零                                                                */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void CorrectVn(VEC InsVn[3], VEC VnErr[3])
{
	register IPARA i;
		
	for (i = 0; i < 3; i++)
	{
		InsVn[i] -= VnErr[i]; 
	}	
}

/*********************************************函数说明*******************************************************/
/*函数名称：CorrectAtti                                                                                     */
/*函数功能描述：完成姿态误差的校正                                                                          */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：向量:AttiErr                                                                                    */
/*输出变量：四元数：Q                                                                                       */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：误差修正量AttiErr在修正完成后会被清零                                                              */
/*备注3：误差修正的对象为姿态四元数，适用于四元数更新的导航算法                                             */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void CorrectAtti(QUAT Q[4], VEC AttiErr[3])
{
  register IPARA i;
  register DPARA DeltaSeta;
  register DPARA C;
  register DPARA S;
  register DPARA InvQSquRoot;
//  BOOL isCorSucc;
  
  QUAT QErr[4],TempQ[4];
           
  DeltaSeta = sqrt(AttiErr[0] * AttiErr[0] + AttiErr[1] * AttiErr[1] + AttiErr[2] * AttiErr[2]);
	
  C = cos(0.5 * DeltaSeta);

  S = (fabs(DeltaSeta) >= MIN_DATA) ? sin(0.5 * DeltaSeta) / DeltaSeta : 0.5;//除零保护
   
  QErr[0] = C;
  QErr[1] = S * AttiErr[0];
  QErr[2] = S * AttiErr[1];
  QErr[3] = S * AttiErr[2];
    
  Qua_Mul(Q, QErr,TempQ);
   
  for(i = 0; i < 4 ; i++)
  {		
  	Q[i] = TempQ[i];
  }
  
  //四元数归一化
  InvQSquRoot = 1.0 / sqrt(Q[0] * Q[0] + Q[1] * Q[1] + Q[2] * Q[2] + Q[3] * Q[3]);
  Q[0] = Q[0] * InvQSquRoot; 
  Q[1] = Q[1] * InvQSquRoot;
  Q[2] = Q[2] * InvQSquRoot;
  Q[3] = Q[3] * InvQSquRoot;
  
  for(i = 0; i < 3 ; i++)
  {
  	AttiErr[i] = 0.0;
  } 
  //return isCorSucc;
}

/*********************************************函数说明*******************************************************/
/*函数名称：CorrectGyroBias                                                                                 */
/*函数功能描述：完成陀螺零偏误差的校正                                                                      */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：角速率:EstGyroBias陀螺零偏误差估计值                                                            */
/*输出变量：角速率：陀螺零偏误差                                                                            */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：误差估计量EstGyroBias在修正完成后会被清零                                                          */
/*备注3：误差修正的对象为导航结构体中存储的陀螺零偏误差修正量，采用增量方法进行修正                         */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void CorrectGyroBias(ANGRATE GyroBias[3], ANGRATE EstGyroBias[3])
{
		register IPARA i;
		
		for(i = 0; i < 3; i++)
		{
				GyroBias[i] += EstGyroBias[i];
				EstGyroBias[i] = 0.0;
		}
}

/*********************************************函数说明*******************************************************/
/*函数名称：CorrectAccBias                                                                                  */
/*函数功能描述：完成加速度计零偏误差的校正                                                                  */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：加速度:EstAccBias                                                                               */
/*输出变量：加速度:AccBias                                                                                  */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：误差估计量EstAccBias在修正完成后会被清零                                                           */
/*备注3：误差修正的对象为导航结构体中存储的加速度计零偏误差修正量，采用增量方法进行修正                     */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void CorrectAccBias(ACCELER AccBias[3], ACCELER EstAccBias[3])
{
	register IPARA i;
		
	for(i = 0; i < 3; i++)
	{
		AccBias[i] += EstAccBias[i];
		EstAccBias[i] = 0.0;
	}
}

/*********************************************函数说明*******************************************************/
/*函数名称：CorrectGyroScaleFacErr                                                                          */
/*函数功能描述：完成陀螺刻度因子误差的校正                                                                  */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：参数:EstErr                                                                                     */
/*输出变量：参数:GyroScaleFacErr                                                                            */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：误差估计量EstErr在修正完成后会被清零                                                               */
/*备注3：误差修正的对象为导航结构体中存储的陀螺刻度因子误差修正量，采用增量方法进行修正                     */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void CorrectGyroScaleFacErr(DPARA GyroScaleFacErr[3], DPARA EstErr[3])
{
		register IPARA i;
		
		for(i = 0; i < 3; i++)
		{
			GyroScaleFacErr[i] += EstErr[i];
			EstErr[i] = 0;
		}
}

/*********************************************函数说明*******************************************************/
/*函数名称：CorrectAccScaleFacErr                                                                           */
/*函数功能描述：完成加速度计刻度因子误差的校正                                                              */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                 */
/*编写人：                                                                                              */
/*输入变量：参数:EstErr                                                                                     */
/*输出变量：参数:AccScaleFacErr                                                                             */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量、输出变量均为地址传递                                                               */
/*备注2：误差估计量EstErr在修正完成后会被清零                                                               */
/*备注3：误差修正的对象为导航结构体中存储的加速度计刻度因子误差修正量，采用增量方法进行修正                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void CorrectAccScaleFacErr(DPARA AccScaleFacErr[3], DPARA EstErr[3])
{
		register IPARA i;
		
		for(i = 0; i < 3; i++)
		{
			AccScaleFacErr[i] += EstErr[i];
			EstErr[i] = 0;
		}
}
