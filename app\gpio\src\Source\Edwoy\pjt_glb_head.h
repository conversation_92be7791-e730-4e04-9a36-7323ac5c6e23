#ifndef __PJT_GLB_HEAD__
#define __PJT_GLB_HEAD__
#include "stdio.h"
#include "stdlib.h"
#include "stdbool.h"
#include "string.h"
#include "deviceconfig.h"


//#define  MCU_HEAD         "gd32f4xx.h"
#define  EVAL_BOARD_HEAD  "pjt_board.h"   //"gd32f470i_eval.h"


#ifdef CMPL_CODE_EDWOY
#include "types.h"
#include "convert.h" //2024.3.23
//--test8---------------------------------------------------------------------------
 //update 2024.2.27
#include "config.h"
//--test8---------------------------------------------------------------------------

///==global paras=============================================================================================================
#define  PRP_INS422          UART4
#define  PRP_SD_SPI          SPI4
#define  DBCOM_Ptf           app_ins422_printf
#define  g_dbcom             g_ins422  //g_ins422_rxtkn_ovr
/*********************
 \brief g_use_gdw_print
        In gdwatch.h at line80, enable printf GDW message
 \value  true, enable printf
 \value  false, disable printf 
**********************/
extern bool g_use_gdw_print ;

typedef enum { 
		FirmVrf_Init    = 0,  //
		FirmVrf_Valid   = 1,  //
		FirmVrf_Lose    = 2,  //
		FirmVrf_Invalid = 3,  //
}emFirmVrf_t;

extern emFirmVrf_t  firmware_datas_vrf   ;      //()

typedef enum { 
		Boot_Init       = 0,  //
		Boot_Orgn       = 1,  //orgn(0x08004000 default) boot
		Boot_Cache      = 2,  //cache(0x08080000 default) boot
}emBootSel_t;

//--test12------------------------------------------------------------------------
//update 2024.3.4
#define MCU_SYSCLK_xMHZ  (SystemCoreClock / 1000000U)
//--end test12--------------------------------------------------------------------
///==end global paras=====================================================================================end global paras====

///--iap_code-------------------------------------------------------------------------------------------
// check APP_LOADED_ADDR in config.h 
//#define FW_APP_ORGN_OFFSET   ((unsigned int)0x00004000)   
//#define FW_APP_ORGN_ADDR     (NVIC_VECTTAB_FLASH + FW_APP_ORGN_OFFSET) //Sector1,16KB
#define FW_APP_ORGN_ADDR     (NVIC_VECTTAB_FLASH + APP_LOADED_ADDR) //Sector1,16KB
#define FW_APP_ORGN_OFFSET   APP_LOADED_ADDR   

//#define FW_APP_CACHE_ADDR     0x08080000U  //Sector8,128KB
#define FW_APP_CACHE_ADDR      USER_DAPSAVE_PAGE_ADDR   //Sector17,0x08120000U,128KB
#define FW_APP_CACHE_OFFSET   (FW_APP_CACHE_ADDR - FLASH_BASE)
#define FW_BACKUP_SECSN        CTL_SN(8)        
#define FW_1FRAME_LEN          0x400   //= 1024  1KB
#define Fm_MaxSize            (FW_1FRAME_LEN + 0x0A) //

#define FW_APP_BKUP_ADDR     0x08160000U //Sector19,128KB
#define FW_APP_BKUP_OFFSET   (FW_APP_BKUP_ADDR - FLASH_BASE)

#define INS_422_PRP            UART4
#define TxMsg_Size             128    //
extern unsigned char page_4KB_buff[0x1000] ;
extern unsigned char r_txmsg[TxMsg_Size] ;

extern unsigned int   firmware_size  ;    //,unit is byte
extern unsigned short firmware_accu_get   ;    //
extern unsigned short firmware_accu_calc    ;   //
extern unsigned char  firmware_fm_curcnt  ;   //
extern unsigned char  firmware_fm_qty ;   // 

//--test4-----------------------------------------------------------------------
//2024.2.22
#define  FmcSmp_Pos            13312  //fmc-flash = 0x3400 = 13 * 1024   
#define  G_Firmw_Sz        (FW_1FRAME_LEN * 72)   
extern  u8_t  g_firmw_totalbuff[G_Firmw_Sz];
//#define  FmcSmp_Qty       
//--test4-----------------------------------------------------------------------

///---test10------------------------------------------------------------------------------------
//2024,2,29
#define FW_IAP_FLAG_ADDR     0x08100000U //Sector12,16KB
///---end test10-------------------------------------------------------------------end test10---	

#define  IapInfo_Qty        48 
/********************************************************
 \brief g_iap_info 
        g_iap_flag verified at 2024.2.28 20:35
 \param
		[0], iap flag, 
				 true--> goto iap
		[1], firmware size
		[2], firmware accu value
		[3], r_msp_sramaddr_cache
		[4], orgnApp address
		[5], cacheApp address
		[6], App boot selection
					= 1, boot from orgnApp address
					= 2, boot from cacheApp address
		[7], sample qty
    [8], sample offset pos		
********************************************************/
extern u32_t  g_iap_info[IapInfo_Qty] ;
extern __I u32_t  g_iap_flag[IapInfo_Qty]  __attribute__ ((at(FW_IAP_FLAG_ADDR)));

typedef  void  (*pAppFunction) (void);
#define FW_APP_CACHE_ENTRY      ((pAppFunction)(*(__IO unsigned int*)(FW_APP_CACHE_ADDR + 4)))  //verified

bool validate_application(unsigned int Addr) ;
bool validate_fwapp(unsigned int Addr) ;
void iap_jumpto_usraddr(__IO unsigned int const Addr);
void app_iapinfo_ewr(void);
void app_iapinfo_init(emBootSel_t boot, unsigned int offset);
///--end iap_code----------------------------------------------------------------------end iap_code-----

///--fmc_flash------------------------------------------------------------------------------------------
#define FMC_PAGE_SIZE          0x1000   //MCU 
#define FLASH_END              0x081FFFFFU
#define FLASH_END_OFFSET       (u32_t)(FLASH_END - FLASH_BASE)

bool fmcfls_erspages_wt32bit(unsigned int page_addr , const unsigned int* src, unsigned int qty);
bool fmcfls_ers1page_wt32bit(unsigned int page_addr , const unsigned int* src);
bool fmcfls_ers1page_wt8bit(unsigned int page_addr , const unsigned char* src);
bool fmcfls_erase_1page(unsigned int page_addr);

/*******************************************************************************************
  \brief fmc datas
  \param cmd,command
	\notice 
  \update
	    v1.1,2024.2.22,validating....
		 
********************************************************************************************/
bool app_fmc_sampling(unsigned int pos, unsigned int qty);

/*************************************************************
    \brief   read datas form the given address per a byte
    \param  address
    \param  length
    \retval     none
*****************************************************************/
void fmcfls_rd8bit(unsigned int addr_src, unsigned int length, unsigned char* dest);


/*************************************************************
    \brief   read datas form the given address per a halfword
    \param  address
    \param  length
    \retval     none
*****************************************************************/
void fmcfls_rd16bit(unsigned int addr_src, unsigned int length, unsigned short* dest);

/*************************************************************
    \brief   read datas form the given address per a word
    \param  address
    \param  length
    \retval     none
*****************************************************************/
void fmcfls_rd32bit(unsigned int addr_src, unsigned int length, unsigned int* dest);

///--end fmc_flash---------------------------------------------------------------------end fmc_flash----

//extern bool            firmware_flg_wrovr ;  //
//extern bool            firmware_flg_verf  ;  //

//--test4-----------------------------------------------------------------------
//2024.2.22
#define  FmcSmp_Pos            13312  //fmc-flash = 0x3400 = 13 * 1024   
//#define  FmcSmp_Qty       
//--test4-----------------------------------------------------------------------


void comm_firmdatas_verify(unsigned short cmd);
void comm_firm_update(unsigned short cmd);
void comm_report_stop(unsigned short cmd);
/****
  
*****/
void comm_report_startup(unsigned short cmd);
	
/****
  
*****/
void comm_read_ver_rsp_v2(unsigned short cmd) ;

/****
  
  validating....
*****/
void comm_read_dev_type_rsp_v2(unsigned short cmd);
void comm_para_ehco_rsp_v2(unsigned short cmd);

//--test13---------------------------------------------------------------------
//update 2024.3.5 11:11
extern unsigned int gprotocol_send_baudrate;
void gd_eval_com_init(unsigned int, unsigned int); //gprotocol_send_baudrate,BAUD_RATE_460800
//--end test13-----------------------------------------------------------------

#endif //<#ifdef CMPL_CODE_EDWOY>


////==========================================================ALL END ==============================================================================////



#endif //<__PJT_GLB_HEAD__>