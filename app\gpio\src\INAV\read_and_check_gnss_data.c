/***********************************************************************************************************************************/
/*NAVI.C                                                                                                                         */
/*  Ver 0.1                                                                                                                        */
/*/                                                                                                */
/*                                                                                                                             */
/*const.htypedefine.hmath.hDATASTRUCT.hEXTERNGLOBALDATA.hFUNCTION.hmemory.h                                          */
/*GNSSlocusGen.m                                                                  */
/*             */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include "math.h"
#include "DATASTRUCT.h"
//#include "GPS_DataType.h"
#include "EXTERNGLOBALDATA.h"
#include "FUNCTION.h"

//#include "GPS_rec.h"
/*********************************************函数说明*******************************************************/
/*函数名称：Read_And_Check_GNSS_Data                                                                        */
/*函数功能描述：转存天陆海解析出的GNSS数据包                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：指针：lp_GNSSData ，指针：PAOCHE_FRAME_STRUCT* lp_paochedata                                    */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void Read_And_Check_GNSS_Data(p_GNSSData lp_GNSSData,PAOCHE_FRAME_STRUCT* lp_paochedata,p_Navi lp_Navi,p_SysVar lp_SysVar)
{
	//读取GNSS数据
	SaveGNSSData(lp_GNSSData,lp_paochedata,lp_Navi,lp_SysVar);
	//GNSS数据有效性判断
	GNSSAndHeadDataTest(lp_GNSSData,lp_Navi);
	
	if(lp_GNSSData -> isPosEn == YES)
	{
		lp_GNSSData -> GNSS_Valid_Count++;
	}
	else
	{
		lp_GNSSData -> GNSS_Valid_Count=0;
	}
	if(lp_GNSSData -> GNSS_Valid_Count>= THRESHOLD_VALID_3S)
	{
		lp_GNSSData -> isGNSS_Valid_3s = YES;
	}	
}
/*********************************************函数说明*******************************************************/
/*函数名称：SaveGNSSData                                                                                    */
/*函数功能描述：转存天陆海解析出的GNSS数据包                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：指针：lp_GNSSData ，指针：PAOCHE_FRAME_STRUCT* lp_paochedata                                    */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void SaveGNSSData(p_GNSSData lp_GNSSData,PAOCHE_FRAME_STRUCT* lp_paochedata,p_Navi lp_Navi,p_SysVar lp_SysVar)
{
  //int i = 0;
  
	lp_GNSSData -> r_GNSSLati =  lp_paochedata -> gnss_lat * D2R;
	if(lp_paochedata -> gnss_NS=='S')
	{
		lp_GNSSData -> r_GNSSLati = -lp_GNSSData -> r_GNSSLati;	
	}
	
	lp_GNSSData -> r_GNSSLogi =  lp_paochedata -> gnss_lon * D2R;
	if(lp_paochedata -> gnss_EW=='W')
	{
		lp_GNSSData -> r_GNSSLogi = -lp_GNSSData -> r_GNSSLogi;	
	}
	
	lp_GNSSData -> GNSSHeight =  lp_paochedata -> gnss_hgt;
	
	lp_GNSSData -> GNSSVn[0] =  lp_paochedata -> gnss_vn;
	lp_GNSSData -> GNSSVn[1] =  lp_paochedata -> gnss_vu;
	lp_GNSSData -> GNSSVn[2] =  lp_paochedata -> gnss_ve;
	
	if(lp_Navi->Navi_Count>=1)
	{
		//LEN Sn_r[3] = {0.0};
		ComputeLeverArmSn(lp_Navi->Cnb,lp_SysVar->Arm_GNSSToINS_b,lp_SysVar->Arm_GNSSToINS_n);
	  lp_GNSSData -> r_GNSSLati -= lp_SysVar->Arm_GNSSToINS_n[0] * g_Navi.invRm;
	  lp_GNSSData -> r_GNSSLogi -= lp_SysVar->Arm_GNSSToINS_n[2] * g_Navi.invRn;
		if(lp_GNSSData -> r_GNSSLati > PI / 2.0)
		{
			lp_GNSSData -> r_GNSSLati =  PI / 2.0;
		}
		else if(lp_GNSSData -> r_GNSSLati < -PI / 2.0)
		{
			lp_GNSSData -> r_GNSSLati = -PI / 2.0;
		}
		//东西经正负180度切换的判断
		if(lp_GNSSData -> r_GNSSLogi > PI)
		{
				lp_GNSSData -> r_GNSSLogi = lp_GNSSData -> r_GNSSLogi - 2 * PI; //
		}

		if(lp_GNSSData -> r_GNSSLogi < -PI)
		{
				lp_GNSSData -> r_GNSSLogi = lp_GNSSData -> r_GNSSLogi + 2 * PI; //
		} 
    lp_GNSSData -> GNSSHeight -= lp_SysVar->Arm_GNSSToINS_n[1];	
		
		VEL Vn_r[3] = {0.0};
		ComputeLeverArmVn(lp_Navi->Cnb,lp_Navi->r_Wnbb[1],lp_SysVar->Arm_GNSSToINS_b,Vn_r);
		lp_GNSSData -> GNSSVn[0] -= Vn_r[0];
		lp_GNSSData -> GNSSVn[1] -= Vn_r[1];
		lp_GNSSData -> GNSSVn[2] -= Vn_r[2];
	}
	
	lp_GNSSData -> Hdop = lp_paochedata -> gnss_hdop;
	
	lp_GNSSData -> Vdop = lp_paochedata -> gnss_vdop;
	
	lp_GNSSData -> GNSS_State = lp_paochedata -> gnss_rtkstate;
	
	lp_GNSSData -> UseSatNum = lp_paochedata -> gnss_sat;
	
	lp_GNSSData -> GNSS_POS_State = lp_paochedata -> gnss_ps;
	
	lp_GNSSData -> GNSS_V_State = lp_paochedata -> gnss_vs;
	
	lp_GNSSData ->  GNSS_Head_State = lp_paochedata -> twoant_hstate;
	
	lp_GNSSData -> GNSS_V = sqrt(lp_GNSSData -> GNSSVn[0] * lp_GNSSData -> GNSSVn[0] + lp_GNSSData -> GNSSVn[2] * lp_GNSSData -> GNSSVn[2]);
	
	//ComputeTrackAtti(lp_GNSSData -> GNSSVn, &lp_GNSSData -> r_TrackAtti);
	
	lp_GNSSData -> r_GPSHead = (double)lp_paochedata->twoant_yaw * D2R;
		
//	lp_GNSSData -> r_GPSHead = lp_GNSSData -> r_GPSHead  - ( PI/2) ;//调整航向，PI是Π=3.14
//	
//	if(lp_GNSSData -> r_GPSHead < 0)		//如果航向角小于0，则需要加360°，原因是航向角范围是0-360°
//	{
//		lp_GNSSData -> r_GPSHead = lp_GNSSData -> r_GPSHead + 2 * PI ;
//	}
//	else
//	{
//		lp_GNSSData -> r_GPSHead = lp_GNSSData -> r_GPSHead;
//	}
	if(lp_GNSSData -> r_GPSHead >= PI)
	{
		lp_GNSSData -> r_GPSHead = 2 * PI - lp_GNSSData -> r_GPSHead;
	}
	else
	{
		lp_GNSSData -> r_GPSHead = -lp_GNSSData -> r_GPSHead;
	}	
	
	lp_GNSSData -> r_TrackAtti = (double)lp_paochedata->gnss_track * D2R;
	if(lp_GNSSData -> r_TrackAtti >= PI)
	{
		lp_GNSSData -> r_TrackAtti = 2 * PI - lp_GNSSData -> r_TrackAtti;
	}
	else
	{
		lp_GNSSData -> r_TrackAtti = -lp_GNSSData -> r_TrackAtti;
	}
	
}


/*********************************************函数说明*******************************************************/
/*函数名称：GNSSAndHeadDataTest                                                                                    */
/*函数功能描述：转存天陆海解析出的GNSS数据包                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：指针：lp_GNSSData,指针：lp_Navi                                                                               */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void GNSSAndHeadDataTest(p_GNSSData lp_GNSSData,p_Navi lp_Navi)
{
	//默认设置为有效
	lp_GNSSData -> isVelEn = YES;
	lp_GNSSData -> isPosEn = YES;
	lp_GNSSData -> isHeadingEn = YES;//
	lp_GNSSData -> isHeightEn = YES;
	
//#ifdef 	code_test_gyc_old	//按照 高给纯惯导	代码		//20240711 陈可要求注释掉这个判断
//--Update 2024.6.21 ------------------------------------------  
#ifdef CMPL_CODE_EDWOY	//陈工用,由于楼下没有RTK信号，寻北的条件要增加2个：单点和窄固定解，
	///定位状态无效 ，0x00:无效，0x01单点，0x02伪距差分，0x04 RTK固定,0x05 RTK浮点 ，0x07窄固定解
   // use single-point-position & RTK & Narrow fixed solution
#ifndef DEVICE_TYPE_370_25J_6089//DEVICE_TYPE_370_25J_355	
	if(   lp_GNSSData -> GNSS_State != 0x01  
     && lp_GNSSData -> GNSS_State != 0x04
     && lp_GNSSData -> GNSS_State != 0x07
)  //	
#endif
#ifdef DEVICE_TYPE_370_25J_6089
//	if(   lp_GNSSData -> GNSS_State != 0x01  
//     && lp_GNSSData -> GNSS_State != 0x04
//     && lp_GNSSData -> GNSS_State != 0x07
//   )  //
	if(lp_GNSSData -> GNSS_State == 0x00)  //	
#endif	   
	{
		lp_GNSSData -> isVelEn = NO;
		lp_GNSSData -> isPosEn = NO;
		lp_GNSSData -> isHeightEn = NO;
		lp_GNSSData -> isHeadingEn = NO;
	}
#else //Chen Ke							//仅仅接入RTK有效
  // use RTK Fixed solution
	if(lp_GNSSData -> GNSS_State != 0x04) //0x00:无效，0x01单点，0x02伪距差分，0x04 RTK固定,0x05 RTK浮点 
	{
		lp_GNSSData -> isVelEn = NO;
		lp_GNSSData -> isPosEn = NO;
		lp_GNSSData -> isHeightEn = NO;
		lp_GNSSData -> isHeadingEn = NO;
	}  
#endif 
//--End Update 2024.6.21--------------------------------------  
//#endif

#ifndef 	code_test_gyc_gao_chen_20240708 	//按照 高给纯惯导	代码
	//定位状态无效
	if(lp_GNSSData -> GNSS_State == GNSS_FAIL) // 0x00:无效，0x01单点，0x02伪距差分，0x04 RTK固定,0x05 RTK浮点
	{
		lp_GNSSData -> isVelEn = NO;
		lp_GNSSData -> isPosEn = NO;
		lp_GNSSData -> isHeightEn = NO;
		lp_GNSSData -> isHeadingEn = NO;
	}
#endif	
	
	//定位无效
	if(lp_GNSSData -> GNSS_POS_State == 'V')
	{
		lp_GNSSData -> isVelEn = NO;
		lp_GNSSData -> isPosEn = NO;
		lp_GNSSData -> isHeightEn = NO;
		lp_GNSSData -> isHeadingEn = NO;	
	}
	
	//速度无效
	if(lp_GNSSData -> GNSS_V_State == 1)
	{
		lp_GNSSData -> isVelEn = NO;
		lp_GNSSData -> isPosEn = NO;
		lp_GNSSData -> isHeightEn = NO;
		lp_GNSSData -> isHeadingEn = NO;	
	}
 
	//Hdop值超过阈值
	if(lp_GNSSData -> Hdop >= THRESHOLD_HDOP)
	{
		lp_GNSSData -> isVelEn = NO;
		lp_GNSSData -> isPosEn = NO;
		lp_GNSSData -> isHeightEn = NO;
		lp_GNSSData -> isHeadingEn = NO;
	}
	
#ifdef 	code_test_gyc_old 	//按照 高给纯惯导	代码	
	//星数
	if(lp_GNSSData -> UseSatNum <= THRESHOLD_VIS_NUM)
	{
		lp_GNSSData -> isVelEn = NO;
		lp_GNSSData -> isPosEn = NO;
		lp_GNSSData -> isHeightEn = NO;
		lp_GNSSData -> isHeadingEn = NO;
	
	}
#endif	
	
	//GNSS速度野值判据
	if(fabs(lp_GNSSData -> GNSSVn[0]) >= THRESHOLD_VN)
	{
	  lp_GNSSData -> isVelEn = NO;
		lp_GNSSData -> isPosEn = NO;
		lp_GNSSData -> isHeightEn = NO;
		lp_GNSSData -> isHeadingEn = NO;
	}
	if(fabs(lp_GNSSData -> GNSSVn[1]) >= THRESHOLD_VU)
	{
		lp_GNSSData -> isVelEn = NO;
		lp_GNSSData -> isPosEn = NO;
		lp_GNSSData -> isHeightEn = NO;
		lp_GNSSData -> isHeadingEn = NO;
	}
	if(fabs(lp_GNSSData -> GNSSVn[2]) >= THRESHOLD_VE)
	{
		lp_GNSSData -> isVelEn = NO;
		lp_GNSSData -> isPosEn = NO;
		lp_GNSSData -> isHeightEn = NO;
		lp_GNSSData -> isHeadingEn = NO;
	}
	//航向无效
	if(lp_GNSSData ->  GNSS_Head_State == 0)
	{
		lp_GNSSData -> isHeadingEn = NO;
	}
	//其他判据根据应用场景更新
}


