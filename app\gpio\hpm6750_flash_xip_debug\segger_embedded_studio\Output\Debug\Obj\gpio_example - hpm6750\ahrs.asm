	.file	"ahrs.c"
	.option nopic
	.attribute arch, "rv32i2p1_m2p0_a2p1_c2p0_zicsr2p0_zifencei2p0"
	.attribute unaligned_access, 0
	.attribute stack_align, 16
	.text
.Ltext0:
	.cfi_sections	.debug_frame
	.file 1 "E:\\2014902\\HPM6750\\HPM6750_INVProject\\my_project\\app\\gpio\\src\\INAV\\ahrs.c"
	.globl	ahrs_k
	.section	.bss.ahrs_k,"aw",@nobits
	.align	3
	.type	ahrs_k, @object
	.size	ahrs_k, 88
ahrs_k:
	.zero	88
	.globl	ahrs_titl_alig
	.section	.sbss.ahrs_titl_alig,"aw",@nobits
	.type	ahrs_titl_alig, @object
	.size	ahrs_titl_alig, 1
ahrs_titl_alig:
	.zero	1
	.section	.sbss.twoant_mean,"aw",@nobits
	.align	3
	.type	twoant_mean, @object
	.size	twoant_mean, 8
twoant_mean:
	.zero	8
	.section	.sbss.imu_sample_cnt,"aw",@nobits
	.align	1
	.type	imu_sample_cnt, @object
	.size	imu_sample_cnt, 2
imu_sample_cnt:
	.zero	2
	.section	.sbss.twoant_sample_cnt,"aw",@nobits
	.align	1
	.type	twoant_sample_cnt, @object
	.size	twoant_sample_cnt, 2
twoant_sample_cnt:
	.zero	2
	.section	.sbss.mag_sample_cnt,"aw",@nobits
	.align	1
	.type	mag_sample_cnt, @object
	.size	mag_sample_cnt, 2
mag_sample_cnt:
	.zero	2
	.section	.bss.mag_mean,"aw",@nobits
	.align	3
	.type	mag_mean, @object
	.size	mag_mean, 24
mag_mean:
	.zero	24
	.section	.sbss.imu_sample_count,"aw",@nobits
	.align	1
	.type	imu_sample_count, @object
	.size	imu_sample_count, 2
imu_sample_count:
	.zero	2
	.section	.bss.wie_mean,"aw",@nobits
	.align	3
	.type	wie_mean, @object
	.size	wie_mean, 24
wie_mean:
	.zero	24
	.section	.bss.acc_mean,"aw",@nobits
	.align	3
	.type	acc_mean, @object
	.size	acc_mean, 24
acc_mean:
	.zero	24
	.globl	__gedf2
	.globl	__ledf2
	.globl	__adddf3
	.globl	__nedf2
	.globl	__subdf3
	.globl	__gtdf2
	.globl	__ltdf2
	.globl	__floatunsidf
	.globl	__divdf3
	.globl	__floatsidf
	.globl	__muldf3
	.section	.text.ahrs_init,"ax",@progbits
	.align	1
	.globl	ahrs_init
	.type	ahrs_init, @function
ahrs_init:
.LFB0:
	.loc 1 22 1
	.cfi_startproc
	addi	sp,sp,-368
	.cfi_def_cfa_offset 368
	sw	ra,364(sp)
	sw	s0,360(sp)
	sw	s1,356(sp)
	sw	s2,352(sp)
	sw	s3,348(sp)
	sw	s4,344(sp)
	sw	s5,340(sp)
	sw	s6,336(sp)
	sw	s7,332(sp)
	sw	s8,328(sp)
	sw	s9,324(sp)
	sw	s10,320(sp)
	sw	s11,316(sp)
	.cfi_offset 1, -4
	.cfi_offset 8, -8
	.cfi_offset 9, -12
	.cfi_offset 18, -16
	.cfi_offset 19, -20
	.cfi_offset 20, -24
	.cfi_offset 21, -28
	.cfi_offset 22, -32
	.cfi_offset 23, -36
	.cfi_offset 24, -40
	.cfi_offset 25, -44
	.cfi_offset 26, -48
	.cfi_offset 27, -52
	sw	a0,28(sp)
	sw	a1,24(sp)
	sw	a2,20(sp)
	mv	a5,a3
	sh	a5,18(sp)
	.loc 1 23 10
	sb	zero,303(sp)
	.loc 1 24 10
	sb	zero,302(sp)
	.loc 1 25 10
	sb	zero,301(sp)
	.loc 1 27 11
	li	a4,0
	li	a5,0
	sw	a4,264(sp)
	sw	a5,268(sp)
	.loc 1 28 11
	sw	zero,64(sp)
	sw	zero,68(sp)
	sw	zero,72(sp)
	sw	zero,76(sp)
	sw	zero,80(sp)
	sw	zero,84(sp)
	.loc 1 29 11
	sw	zero,40(sp)
	sw	zero,44(sp)
	sw	zero,48(sp)
	sw	zero,52(sp)
	sw	zero,56(sp)
	sw	zero,60(sp)
	.loc 1 31 11
	li	a4,0
	li	a5,0
	sw	a4,256(sp)
	sw	a5,260(sp)
	.loc 1 32 11
	li	a4,0
	li	a5,0
	sw	a4,248(sp)
	sw	a5,252(sp)
	.loc 1 34 11
	li	a4,0
	li	a5,0
	sw	a4,288(sp)
	sw	a5,292(sp)
	.loc 1 35 11
	li	a4,0
	li	a5,0
	sw	a4,240(sp)
	sw	a5,244(sp)
	.loc 1 37 11
	li	a4,0
	li	a5,0
	sw	a4,232(sp)
	sw	a5,236(sp)
	.loc 1 38 11
	li	a4,0
	li	a5,0
	sw	a4,224(sp)
	sw	a5,228(sp)
	.loc 1 38 21
	li	a4,0
	li	a5,0
	sw	a4,216(sp)
	sw	a5,220(sp)
	.loc 1 38 32
	li	a4,0
	li	a5,0
	sw	a4,280(sp)
	sw	a5,284(sp)
	.loc 1 39 11
	li	a4,0
	li	a5,0
	sw	a4,208(sp)
	sw	a5,212(sp)
	.loc 1 39 25
	li	a4,0
	li	a5,0
	sw	a4,200(sp)
	sw	a5,204(sp)
	.loc 1 39 39
	li	a4,0
	li	a5,0
	sw	a4,192(sp)
	sw	a5,196(sp)
	.loc 1 39 54
	li	a4,0
	li	a5,0
	sw	a4,184(sp)
	sw	a5,188(sp)
	.loc 1 40 11
	li	a4,0
	li	a5,0
	sw	a4,272(sp)
	sw	a5,276(sp)
	.loc 1 41 11
	li	a4,0
	li	a5,0
	sw	a4,176(sp)
	sw	a5,180(sp)
	.loc 1 43 11
	li	a4,0
	li	a5,0
	sw	a4,168(sp)
	sw	a5,172(sp)
	.loc 1 43 20
	li	a4,0
	li	a5,0
	sw	a4,160(sp)
	sw	a5,164(sp)
	.loc 1 43 29
	li	a4,0
	li	a5,0
	sw	a4,152(sp)
	sw	a5,156(sp)
	.loc 1 43 38
	li	a4,0
	li	a5,0
	sw	a4,144(sp)
	sw	a5,148(sp)
	.loc 1 43 47
	li	a4,0
	li	a5,0
	sw	a4,136(sp)
	sw	a5,140(sp)
	.loc 1 43 56
	li	a4,0
	li	a5,0
	sw	a4,128(sp)
	sw	a5,132(sp)
	.loc 1 44 11
	li	a4,0
	li	a5,0
	sw	a4,120(sp)
	sw	a5,124(sp)
	.loc 1 44 20
	li	a4,0
	li	a5,0
	sw	a4,112(sp)
	sw	a5,116(sp)
	.loc 1 44 29
	li	a4,0
	li	a5,0
	sw	a4,104(sp)
	sw	a5,108(sp)
	.loc 1 44 38
	li	a4,0
	li	a5,0
	sw	a4,96(sp)
	sw	a5,100(sp)
	.loc 1 47 16
	lw	a5,20(sp)
	lbu	a4,99(a5)
	.loc 1 47 6
	li	a5,4
	bne	a4,a5,.L2
	.loc 1 49 26
	li	a5,1
	sb	a5,303(sp)
	j	.L3
.L2:
	.loc 1 53 26
	sb	zero,303(sp)
.L3:
	.loc 1 56 17
	lw	a5,20(sp)
	lbu	a5,138(a5)
	.loc 1 56 36
	xori	a5,a5,1
	andi	a5,a5,0xff
	.loc 1 56 7
	beq	a5,zero,.L4
	.loc 1 58 26
	sb	zero,303(sp)
.L4:
	.loc 1 61 23
	lui	a5,%hi(imu_sample_cnt)
	lhu	a5,%lo(imu_sample_cnt)(a5)
	.loc 1 61 7
	lhu	a4,18(sp)
	bleu	a4,a5,.L5
	.loc 1 64 12
	lbu	a5,303(sp)
	beq	a5,zero,.L6
	.loc 1 66 29
	lw	a5,20(sp)
	lw	a4,104(a5)
	lw	a5,108(a5)
	sw	a4,264(sp)
	sw	a5,268(sp)
	.loc 1 67 15
	li	a2,0
	li	a3,0
	lw	a0,264(sp)
	lw	a1,268(sp)
	call	__gedf2
	mv	a5,a0
	blt	a5,zero,.L6
	.loc 1 67 37 discriminator 1
	lui	a5,%hi(.LC0)
	lw	a2,%lo(.LC0)(a5)
	lw	a3,%lo(.LC0+4)(a5)
	lw	a0,264(sp)
	lw	a1,268(sp)
	call	__ledf2
	mv	a5,a0
	bgt	a5,zero,.L6
	.loc 1 69 38
	lui	a5,%hi(twoant_sample_cnt)
	lhu	a5,%lo(twoant_sample_cnt)(a5)
	.loc 1 69 19
	bne	a5,zero,.L9
	.loc 1 69 54 discriminator 1
	lui	a3,%hi(twoant_mean)
	li	a4,0
	li	a5,0
	sw	a4,%lo(twoant_mean)(a3)
	sw	a5,%lo(twoant_mean+4)(a3)
.L9:
	.loc 1 71 49
	lui	a5,%hi(twoant_mean)
	lw	a4,%lo(twoant_mean)(a5)
	lw	a5,%lo(twoant_mean+4)(a5)
	lw	a2,264(sp)
	lw	a3,268(sp)
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 71 35
	lui	a3,%hi(twoant_mean)
	sw	a4,%lo(twoant_mean)(a3)
	sw	a5,%lo(twoant_mean+4)(a3)
	.loc 1 72 55
	lui	a5,%hi(twoant_sample_cnt)
	lhu	a5,%lo(twoant_sample_cnt)(a5)
	addi	a5,a5,1
	slli	a4,a5,16
	srli	a4,a4,16
	.loc 1 72 35
	lui	a5,%hi(twoant_sample_cnt)
	sh	a4,%lo(twoant_sample_cnt)(a5)
	.loc 1 74 19
	li	a2,0
	li	a3,0
	lw	a0,240(sp)
	lw	a1,244(sp)
	call	__nedf2
	mv	a5,a0
	beq	a5,zero,.L10
	.loc 1 74 48 discriminator 1
	lw	a2,240(sp)
	lw	a3,244(sp)
	lw	a0,264(sp)
	lw	a1,268(sp)
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,288(sp)
	sw	a5,292(sp)
.L10:
	.loc 1 76 20
	lw	s2,288(sp)
	lw	a4,292(sp)
	li	a5,-2147483648
	addi	a5,a5,-1
	and	s3,a4,a5
	.loc 1 76 19
	lui	a5,%hi(.LC1)
	lw	a2,%lo(.LC1)(a5)
	lw	a3,%lo(.LC1+4)(a5)
	mv	a0,s2
	mv	a1,s3
	call	__gtdf2
	mv	a5,a0
	ble	a5,zero,.L12
	.loc 1 78 37
	lui	a3,%hi(twoant_mean)
	lw	a4,264(sp)
	lw	a5,268(sp)
	sw	a4,%lo(twoant_mean)(a3)
	sw	a5,%lo(twoant_mean+4)(a3)
	.loc 1 79 44
	lui	a5,%hi(twoant_sample_cnt)
	li	a4,1
	sh	a4,%lo(twoant_sample_cnt)(a5)
.L12:
	.loc 1 82 35
	lw	a4,264(sp)
	lw	a5,268(sp)
	sw	a4,240(sp)
	sw	a5,244(sp)
.L6:
	.loc 1 96 27
	lui	a5,%hi(mag_sample_cnt)
	lhu	a5,%lo(mag_sample_cnt)(a5)
	.loc 1 96 11
	bne	a5,zero,.L14
	.loc 1 98 24
	lui	a5,%hi(mag_mean)
	addi	a3,a5,%lo(mag_mean)
	li	a4,0
	li	a5,0
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 99 24
	lui	a5,%hi(mag_mean)
	addi	a3,a5,%lo(mag_mean)
	li	a4,0
	li	a5,0
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 100 24
	lui	a5,%hi(mag_mean)
	addi	a3,a5,%lo(mag_mean)
	li	a4,0
	li	a5,0
	sw	a4,16(a3)
	sw	a5,20(a3)
.L14:
	.loc 1 111 44
	lui	a5,%hi(mag_sample_cnt)
	lhu	a5,%lo(mag_sample_cnt)(a5)
	addi	a5,a5,1
	slli	a4,a5,16
	srli	a4,a4,16
	.loc 1 111 28
	lui	a5,%hi(mag_sample_cnt)
	sh	a4,%lo(mag_sample_cnt)(a5)
	.loc 1 116 29
	lui	a5,%hi(imu_sample_count)
	lhu	a5,%lo(imu_sample_count)(a5)
	.loc 1 116 12
	bne	a5,zero,.L15
	.loc 1 118 25
	lui	a5,%hi(wie_mean)
	addi	a3,a5,%lo(wie_mean)
	li	a4,0
	li	a5,0
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 119 25
	lui	a5,%hi(wie_mean)
	addi	a3,a5,%lo(wie_mean)
	li	a4,0
	li	a5,0
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 120 25
	lui	a5,%hi(wie_mean)
	addi	a3,a5,%lo(wie_mean)
	li	a4,0
	li	a5,0
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 122 24
	lui	a5,%hi(acc_mean)
	addi	a3,a5,%lo(acc_mean)
	li	a4,0
	li	a5,0
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 123 24
	lui	a5,%hi(acc_mean)
	addi	a3,a5,%lo(acc_mean)
	li	a4,0
	li	a5,0
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 124 24
	lui	a5,%hi(acc_mean)
	addi	a3,a5,%lo(acc_mean)
	li	a4,0
	li	a5,0
	sw	a4,16(a3)
	sw	a5,20(a3)
.L15:
	.loc 1 128 24
	lui	a5,%hi(wie_mean)
	addi	a5,a5,%lo(wie_mean)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 128 44
	lw	a3,28(sp)
	lw	a2,0(a3)
	lw	a3,4(a3)
	.loc 1 128 27
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 128 14
	lui	a3,%hi(wie_mean)
	addi	a3,a3,%lo(wie_mean)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 129 24
	lui	a5,%hi(wie_mean)
	addi	a5,a5,%lo(wie_mean)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 129 44
	lw	a3,28(sp)
	lw	a2,8(a3)
	lw	a3,12(a3)
	.loc 1 129 27
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 129 14
	lui	a3,%hi(wie_mean)
	addi	a3,a3,%lo(wie_mean)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 130 24
	lui	a5,%hi(wie_mean)
	addi	a5,a5,%lo(wie_mean)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 130 44
	lw	a3,28(sp)
	lw	a2,16(a3)
	lw	a3,20(a3)
	.loc 1 130 27
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 130 14
	lui	a3,%hi(wie_mean)
	addi	a3,a3,%lo(wie_mean)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 133 24
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 133 43
	lw	a3,28(sp)
	lw	a2,48(a3)
	lw	a3,52(a3)
	.loc 1 133 27
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 133 14
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 134 24
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 134 43
	lw	a3,28(sp)
	lw	a2,56(a3)
	lw	a3,60(a3)
	.loc 1 134 27
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 134 14
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 135 24
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 135 43
	lw	a3,28(sp)
	lw	a2,64(a3)
	lw	a3,68(a3)
	.loc 1 135 27
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 135 14
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 138 45
	lui	a5,%hi(imu_sample_count)
	lhu	a5,%lo(imu_sample_count)(a5)
	addi	a5,a5,1
	slli	a4,a5,16
	srli	a4,a4,16
	.loc 1 138 26
	lui	a5,%hi(imu_sample_count)
	sh	a4,%lo(imu_sample_count)(a5)
	.loc 1 141 25
	lw	a5,28(sp)
	lw	a4,48(a5)
	lw	a5,52(a5)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	s0,a0
	mv	s1,a1
	.loc 1 141 49 discriminator 1
	lw	a5,28(sp)
	lw	a4,56(a5)
	lw	a5,60(a5)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 141 47 discriminator 2
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 141 73 discriminator 2
	lw	a5,28(sp)
	lw	a4,64(a5)
	lw	a5,68(a5)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 141 20 discriminator 3
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sqrt
	sw	a0,256(sp)
	sw	a1,260(sp)
	.loc 1 142 22
	lw	a5,28(sp)
	lw	a4,0(a5)
	lw	a5,4(a5)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	s0,a0
	mv	s1,a1
	.loc 1 142 47 discriminator 1
	lw	a5,28(sp)
	lw	a4,8(a5)
	lw	a5,12(a5)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 142 45 discriminator 2
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 142 72 discriminator 2
	lw	a5,28(sp)
	lw	a4,16(a5)
	lw	a5,20(a5)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 142 17 discriminator 3
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sqrt
	sw	a0,248(sp)
	sw	a1,252(sp)
	.loc 1 143 11
	lui	a5,%hi(.LC2)
	lw	a2,%lo(.LC2)(a5)
	lw	a3,%lo(.LC2+4)(a5)
	lw	a0,256(sp)
	lw	a1,260(sp)
	call	__ltdf2
	mv	a5,a0
	blt	a5,zero,.L16
	.loc 1 143 27 discriminator 1
	lui	a5,%hi(.LC3)
	lw	a2,%lo(.LC3)(a5)
	lw	a3,%lo(.LC3+4)(a5)
	lw	a0,256(sp)
	lw	a1,260(sp)
	call	__gtdf2
	mv	a5,a0
	bgt	a5,zero,.L16
	.loc 1 143 45 discriminator 2
	lui	a5,%hi(.LC4)
	lw	a2,%lo(.LC4)(a5)
	lw	a3,%lo(.LC4+4)(a5)
	lw	a0,248(sp)
	lw	a1,252(sp)
	call	__gtdf2
	mv	a5,a0
	ble	a5,zero,.L17
.L16:
	.loc 1 145 41
	lw	a5,28(sp)
	lw	a4,48(a5)
	lw	a5,52(a5)
	.loc 1 145 24
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 146 38
	lw	a5,28(sp)
	lw	a4,56(a5)
	lw	a5,60(a5)
	.loc 1 146 21
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 147 38
	lw	a5,28(sp)
	lw	a4,64(a5)
	lw	a5,68(a5)
	.loc 1 147 21
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 149 42
	lw	a5,28(sp)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 149 24
	lui	a3,%hi(wie_mean)
	addi	a3,a3,%lo(wie_mean)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 150 36
	lw	a5,28(sp)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 150 18
	lui	a3,%hi(wie_mean)
	addi	a3,a3,%lo(wie_mean)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 151 36
	lw	a5,28(sp)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 151 18
	lui	a3,%hi(wie_mean)
	addi	a3,a3,%lo(wie_mean)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 153 30
	lui	a5,%hi(imu_sample_count)
	li	a4,1
	sh	a4,%lo(imu_sample_count)(a5)
.L17:
	.loc 1 156 40
	lui	a5,%hi(imu_sample_cnt)
	lhu	a5,%lo(imu_sample_cnt)(a5)
	addi	a5,a5,1
	slli	a4,a5,16
	srli	a4,a4,16
	.loc 1 156 24
	lui	a5,%hi(imu_sample_cnt)
	sh	a4,%lo(imu_sample_cnt)(a5)
	.loc 1 159 30
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	sb	zero,84(a5)
	.loc 1 160 31
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	sb	zero,85(a5)
	j	.L19
.L5:
.LBB2:
	.loc 1 165 11
	lbu	a5,303(sp)
	beq	a5,zero,.L20
	.loc 1 167 23
	lw	a5,20(sp)
	lw	a4,104(a5)
	lw	a5,108(a5)
	sw	a4,264(sp)
	sw	a5,268(sp)
	.loc 1 169 15
	li	a2,0
	li	a3,0
	lw	a0,264(sp)
	lw	a1,268(sp)
	call	__gedf2
	mv	a5,a0
	blt	a5,zero,.L20
	.loc 1 169 37 discriminator 1
	lui	a5,%hi(.LC0)
	lw	a2,%lo(.LC0)(a5)
	lw	a3,%lo(.LC0+4)(a5)
	lw	a0,264(sp)
	lw	a1,268(sp)
	call	__ledf2
	mv	a5,a0
	bgt	a5,zero,.L20
	.loc 1 173 38
	lui	a5,%hi(twoant_sample_cnt)
	lhu	a5,%lo(twoant_sample_cnt)(a5)
	.loc 1 173 19
	bne	a5,zero,.L23
	.loc 1 173 55 discriminator 1
	lui	a3,%hi(twoant_mean)
	li	a4,0
	li	a5,0
	sw	a4,%lo(twoant_mean)(a3)
	sw	a5,%lo(twoant_mean+4)(a3)
.L23:
	.loc 1 175 49
	lui	a5,%hi(twoant_mean)
	lw	a4,%lo(twoant_mean)(a5)
	lw	a5,%lo(twoant_mean+4)(a5)
	lw	a2,264(sp)
	lw	a3,268(sp)
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 175 35
	lui	a3,%hi(twoant_mean)
	sw	a4,%lo(twoant_mean)(a3)
	sw	a5,%lo(twoant_mean+4)(a3)
	.loc 1 176 55
	lui	a5,%hi(twoant_sample_cnt)
	lhu	a5,%lo(twoant_sample_cnt)(a5)
	addi	a5,a5,1
	slli	a4,a5,16
	srli	a4,a4,16
	.loc 1 176 35
	lui	a5,%hi(twoant_sample_cnt)
	sh	a4,%lo(twoant_sample_cnt)(a5)
	.loc 1 178 22
	li	a4,0
	li	a5,0
	sw	a4,288(sp)
	sw	a5,292(sp)
	.loc 1 179 19
	li	a2,0
	li	a3,0
	lw	a0,240(sp)
	lw	a1,244(sp)
	call	__nedf2
	mv	a5,a0
	beq	a5,zero,.L24
	.loc 1 179 48 discriminator 1
	lw	a2,240(sp)
	lw	a3,244(sp)
	lw	a0,264(sp)
	lw	a1,268(sp)
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,288(sp)
	sw	a5,292(sp)
.L24:
	.loc 1 180 20
	lw	s0,288(sp)
	lw	a4,292(sp)
	li	a5,-2147483648
	addi	a5,a5,-1
	and	s1,a4,a5
	.loc 1 180 19
	lui	a5,%hi(.LC1)
	lw	a2,%lo(.LC1)(a5)
	lw	a3,%lo(.LC1+4)(a5)
	mv	a0,s0
	mv	a1,s1
	call	__gtdf2
	mv	a5,a0
	ble	a5,zero,.L26
	.loc 1 182 40
	lui	a3,%hi(twoant_mean)
	lw	a4,264(sp)
	lw	a5,268(sp)
	sw	a4,%lo(twoant_mean)(a3)
	sw	a5,%lo(twoant_mean+4)(a3)
	.loc 1 183 40
	lui	a5,%hi(twoant_sample_cnt)
	li	a4,1
	sh	a4,%lo(twoant_sample_cnt)(a5)
.L26:
	.loc 1 186 35
	lw	a4,264(sp)
	lw	a5,268(sp)
	sw	a4,240(sp)
	sw	a5,244(sp)
.L20:
	.loc 1 202 27
	lui	a5,%hi(mag_sample_cnt)
	lhu	a5,%lo(mag_sample_cnt)(a5)
	.loc 1 202 11
	bne	a5,zero,.L28
	.loc 1 204 24
	lui	a5,%hi(mag_mean)
	addi	a3,a5,%lo(mag_mean)
	li	a4,0
	li	a5,0
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 205 24
	lui	a5,%hi(mag_mean)
	addi	a3,a5,%lo(mag_mean)
	li	a4,0
	li	a5,0
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 206 24
	lui	a5,%hi(mag_mean)
	addi	a3,a5,%lo(mag_mean)
	li	a4,0
	li	a5,0
	sw	a4,16(a3)
	sw	a5,20(a3)
.L28:
	.loc 1 222 30
	lui	a5,%hi(wie_mean)
	addi	a5,a5,%lo(wie_mean)
	lw	s0,0(a5)
	lw	s1,4(a5)
	.loc 1 222 35
	lui	a5,%hi(imu_sample_count)
	lhu	a5,%lo(imu_sample_count)(a5)
	mv	a0,a5
	call	__floatunsidf
	mv	a4,a0
	mv	a5,a1
	.loc 1 222 33
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 222 20
	lui	a3,%hi(wie_mean)
	addi	a3,a3,%lo(wie_mean)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 223 24
	lui	a5,%hi(wie_mean)
	addi	a5,a5,%lo(wie_mean)
	lw	s0,8(a5)
	lw	s1,12(a5)
	.loc 1 223 29
	lui	a5,%hi(imu_sample_count)
	lhu	a5,%lo(imu_sample_count)(a5)
	mv	a0,a5
	call	__floatunsidf
	mv	a4,a0
	mv	a5,a1
	.loc 1 223 27
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 223 14
	lui	a3,%hi(wie_mean)
	addi	a3,a3,%lo(wie_mean)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 224 24
	lui	a5,%hi(wie_mean)
	addi	a5,a5,%lo(wie_mean)
	lw	s0,16(a5)
	lw	s1,20(a5)
	.loc 1 224 29
	lui	a5,%hi(imu_sample_count)
	lhu	a5,%lo(imu_sample_count)(a5)
	mv	a0,a5
	call	__floatunsidf
	mv	a4,a0
	mv	a5,a1
	.loc 1 224 27
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 224 14
	lui	a3,%hi(wie_mean)
	addi	a3,a3,%lo(wie_mean)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 226 30
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	s0,0(a5)
	lw	s1,4(a5)
	.loc 1 226 35
	lui	a5,%hi(imu_sample_count)
	lhu	a5,%lo(imu_sample_count)(a5)
	mv	a0,a5
	call	__floatunsidf
	mv	a4,a0
	mv	a5,a1
	.loc 1 226 33
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 226 20
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 227 24
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	s0,8(a5)
	lw	s1,12(a5)
	.loc 1 227 29
	lui	a5,%hi(imu_sample_count)
	lhu	a5,%lo(imu_sample_count)(a5)
	mv	a0,a5
	call	__floatunsidf
	mv	a4,a0
	mv	a5,a1
	.loc 1 227 27
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 227 14
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 228 24
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	s0,16(a5)
	lw	s1,20(a5)
	.loc 1 228 29
	lui	a5,%hi(imu_sample_count)
	lhu	a5,%lo(imu_sample_count)(a5)
	mv	a0,a5
	call	__floatunsidf
	mv	a4,a0
	mv	a5,a1
	.loc 1 228 27
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 228 14
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 230 14
	li	a5,1
	sb	a5,95(sp)
	.loc 1 232 29
	lui	a5,%hi(twoant_sample_cnt)
	lhu	a5,%lo(twoant_sample_cnt)(a5)
	.loc 1 232 11
	beq	a5,zero,.L29
	.loc 1 234 38
	lui	a5,%hi(twoant_mean)
	lw	s0,%lo(twoant_mean)(a5)
	lw	s1,%lo(twoant_mean+4)(a5)
	.loc 1 234 39
	lui	a5,%hi(twoant_sample_cnt)
	lhu	a5,%lo(twoant_sample_cnt)(a5)
	mv	a0,a5
	call	__floatunsidf
	mv	a4,a0
	mv	a5,a1
	.loc 1 234 38
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 234 25
	lui	a3,%hi(twoant_mean)
	sw	a4,%lo(twoant_mean)(a3)
	sw	a5,%lo(twoant_mean+4)(a3)
	.loc 1 235 33
	li	a5,1
	sb	a5,302(sp)
	j	.L30
.L29:
	.loc 1 239 25
	lui	a3,%hi(twoant_mean)
	li	a4,0
	li	a5,0
	sw	a4,%lo(twoant_mean)(a3)
	sw	a5,%lo(twoant_mean+4)(a3)
	.loc 1 240 33
	sb	zero,302(sp)
.L30:
	.loc 1 244 27
	lui	a5,%hi(mag_sample_cnt)
	lhu	a5,%lo(mag_sample_cnt)(a5)
	.loc 1 244 11
	beq	a5,zero,.L31
	.loc 1 246 28
	lui	a5,%hi(mag_mean)
	addi	a5,a5,%lo(mag_mean)
	lw	s0,0(a5)
	lw	s1,4(a5)
	.loc 1 246 31
	lui	a5,%hi(mag_sample_cnt)
	lhu	a5,%lo(mag_sample_cnt)(a5)
	mv	a0,a5
	call	__floatsidf
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 246 18
	lui	a3,%hi(mag_mean)
	addi	a3,a3,%lo(mag_mean)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 247 28
	lui	a5,%hi(mag_mean)
	addi	a5,a5,%lo(mag_mean)
	lw	s0,8(a5)
	lw	s1,12(a5)
	.loc 1 247 31
	lui	a5,%hi(mag_sample_cnt)
	lhu	a5,%lo(mag_sample_cnt)(a5)
	mv	a0,a5
	call	__floatsidf
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 247 18
	lui	a3,%hi(mag_mean)
	addi	a3,a3,%lo(mag_mean)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 248 28
	lui	a5,%hi(mag_mean)
	addi	a5,a5,%lo(mag_mean)
	lw	s0,16(a5)
	lw	s1,20(a5)
	.loc 1 248 31
	lui	a5,%hi(mag_sample_cnt)
	lhu	a5,%lo(mag_sample_cnt)(a5)
	mv	a0,a5
	call	__floatsidf
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 248 18
	lui	a3,%hi(mag_mean)
	addi	a3,a3,%lo(mag_mean)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 250 30
	li	a5,1
	sb	a5,301(sp)
	j	.L32
.L31:
	.loc 1 254 30
	sb	zero,301(sp)
.L32:
	.loc 1 257 34
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 257 45
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	lw	a2,0(a3)
	lw	a3,4(a3)
	.loc 1 257 36
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 257 58
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 257 69
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	lw	a2,8(a3)
	lw	a3,12(a3)
	.loc 1 257 60
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 257 48
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 257 82
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 257 93
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	lw	a2,16(a3)
	lw	a3,20(a3)
	.loc 1 257 84
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 257 21
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sqrt
	sw	a0,232(sp)
	sw	a1,236(sp)
	.loc 1 258 32
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 258 17
	sw	a4,0(sp)
	li	a3,-2147483648
	xor	a5,a5,a3
	sw	a5,4(sp)
	.loc 1 258 44
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 258 17
	sw	a4,8(sp)
	li	a3,-2147483648
	xor	a5,a5,a3
	sw	a5,12(sp)
	lw	a2,8(sp)
	lw	a3,12(sp)
	lw	a0,0(sp)
	lw	a1,4(sp)
	call	atan2
	sw	a0,224(sp)
	sw	a1,228(sp)
	.loc 1 259 24
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 259 11
	lw	a2,232(sp)
	lw	a3,236(sp)
	mv	a0,a4
	mv	a1,a5
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	asin
	sw	a0,216(sp)
	sw	a1,220(sp)
	.loc 1 261 11
	lbu	a5,302(sp)
	beq	a5,zero,.L33
	.loc 1 263 30
	lui	a5,%hi(twoant_mean)
	lw	a4,%lo(twoant_mean)(a5)
	lw	a5,%lo(twoant_mean+4)(a5)
	.loc 1 263 17
	lui	a3,%hi(.LC5)
	lw	a2,%lo(.LC5)(a3)
	lw	a3,%lo(.LC5+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,280(sp)
	sw	a5,284(sp)
	.loc 1 264 34
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	li	a4,1
	sb	a4,85(a5)
	j	.L34
.L33:
	.loc 1 266 16
	lbu	a5,95(sp)
	beq	a5,zero,.L35
	.loc 1 268 26
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	sin
	sw	a0,208(sp)
	sw	a1,212(sp)
	.loc 1 268 50 discriminator 1
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	cos
	sw	a0,200(sp)
	sw	a1,204(sp)
	.loc 1 269 20
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	sin
	sw	a0,192(sp)
	sw	a1,196(sp)
	.loc 1 269 44 discriminator 1
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	cos
	sw	a0,184(sp)
	sw	a1,188(sp)
	.loc 1 272 37
	lui	a5,%hi(wie_mean)
	addi	a5,a5,%lo(wie_mean)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 272 28
	lw	a2,184(sp)
	lw	a3,188(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 272 51
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,192(sp)
	lw	a1,196(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 272 69
	lui	a5,%hi(wie_mean)
	addi	a5,a5,%lo(wie_mean)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 272 60
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 272 40
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 272 82
	lw	a2,192(sp)
	lw	a3,196(sp)
	lw	a0,200(sp)
	lw	a1,204(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 272 101
	lui	a5,%hi(wie_mean)
	addi	a5,a5,%lo(wie_mean)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 272 92
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 272 72
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 272 17
	sw	a4,64(sp)
	sw	a5,68(sp)
	.loc 1 273 36
	lui	a5,%hi(wie_mean)
	addi	a5,a5,%lo(wie_mean)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 273 27
	lw	a2,200(sp)
	lw	a3,204(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 273 59
	lui	a5,%hi(wie_mean)
	addi	a5,a5,%lo(wie_mean)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 273 50
	lw	a2,208(sp)
	lw	a3,212(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 273 40
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 273 17
	sw	a4,72(sp)
	sw	a5,76(sp)
	.loc 1 275 28
	lw	a4,64(sp)
	lw	a5,68(sp)
	.loc 1 275 16
	mv	s10,a4
	li	a3,-2147483648
	addi	a3,a3,-1
	and	s11,a5,a3
	.loc 1 275 15
	lui	a5,%hi(.LC6)
	lw	a2,%lo(.LC6)(a5)
	lw	a3,%lo(.LC6+4)(a5)
	mv	a0,s10
	mv	a1,s11
	call	__ltdf2
	mv	a5,a0
	bge	a5,zero,.L55
	.loc 1 277 43
	lw	a0,64(sp)
	lw	a1,68(sp)
	.loc 1 277 69
	lw	a4,72(sp)
	lw	a5,76(sp)
	.loc 1 277 43
	mv	s8,a4
	li	a3,-2147483648
	xor	s9,a5,a3
	mv	a2,s8
	mv	a3,s9
	call	atan2
	mv	a4,a0
	mv	a5,a1
	.loc 1 277 31 discriminator 1
	lui	a1,%hi(.LC7)
	mv	a2,a4
	mv	a3,a5
	lw	a0,%lo(.LC7)(a1)
	lw	a1,%lo(.LC7+4)(a1)
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,272(sp)
	sw	a5,276(sp)
	j	.L38
.L55:
	.loc 1 281 48
	lw	a4,72(sp)
	lw	a5,76(sp)
	.loc 1 281 34
	mv	s6,a4
	li	a3,-2147483648
	xor	s7,a5,a3
	lw	a4,64(sp)
	lw	a5,68(sp)
	mv	a2,a4
	mv	a3,a5
	mv	a0,s6
	mv	a1,s7
	call	atan2
	sw	a0,272(sp)
	sw	a1,276(sp)
.L38:
	.loc 1 284 17
	lw	a4,272(sp)
	lw	a5,276(sp)
	sw	a4,280(sp)
	sw	a5,284(sp)
	.loc 1 285 34
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	li	a4,1
	sb	a4,85(a5)
	j	.L34
.L35:
	.loc 1 287 16
	lbu	a5,301(sp)
	beq	a5,zero,.L39
	.loc 1 289 26
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	sin
	sw	a0,208(sp)
	sw	a1,212(sp)
	.loc 1 289 50 discriminator 1
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	cos
	sw	a0,200(sp)
	sw	a1,204(sp)
	.loc 1 290 20
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	sin
	sw	a0,192(sp)
	sw	a1,196(sp)
	.loc 1 290 44 discriminator 1
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	cos
	sw	a0,184(sp)
	sw	a1,188(sp)
	.loc 1 293 43
	lui	a5,%hi(mag_mean)
	addi	a5,a5,%lo(mag_mean)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 293 34
	lw	a2,184(sp)
	lw	a3,188(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 293 57
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,192(sp)
	lw	a1,196(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 293 75
	lui	a5,%hi(mag_mean)
	addi	a5,a5,%lo(mag_mean)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 293 66
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 293 46
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 293 88
	lw	a2,192(sp)
	lw	a3,196(sp)
	lw	a0,200(sp)
	lw	a1,204(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 293 107
	lui	a5,%hi(mag_mean)
	addi	a5,a5,%lo(mag_mean)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 293 98
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 293 78
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 293 23
	sw	a4,40(sp)
	sw	a5,44(sp)
	.loc 1 294 42
	lui	a5,%hi(mag_mean)
	addi	a5,a5,%lo(mag_mean)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 294 33
	lw	a2,200(sp)
	lw	a3,204(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 294 65
	lui	a5,%hi(mag_mean)
	addi	a5,a5,%lo(mag_mean)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 294 56
	lw	a2,208(sp)
	lw	a3,212(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 294 46
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 294 23
	sw	a4,48(sp)
	sw	a5,52(sp)
	.loc 1 298 43
	lw	a4,48(sp)
	lw	a5,52(sp)
	.loc 1 298 29
	mv	s4,a4
	li	a3,-2147483648
	xor	s5,a5,a3
	lw	a4,40(sp)
	lw	a5,44(sp)
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	atan2
	sw	a0,176(sp)
	sw	a1,180(sp)
	.loc 1 300 17
	lw	a4,176(sp)
	lw	a5,180(sp)
	sw	a4,280(sp)
	sw	a5,284(sp)
	.loc 1 301 34
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	li	a4,1
	sb	a4,85(a5)
	j	.L34
.L39:
	.loc 1 305 17
	li	a4,0
	li	a5,0
	sw	a4,280(sp)
	sw	a5,284(sp)
	.loc 1 306 34
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	sb	zero,85(a5)
.L34:
	.loc 1 309 11
	lui	a5,%hi(.LC8)
	lw	a2,%lo(.LC8)(a5)
	lw	a3,%lo(.LC8+4)(a5)
	lw	a0,280(sp)
	lw	a1,284(sp)
	call	__gtdf2
	mv	a5,a0
	ble	a5,zero,.L56
	.loc 1 311 17
	lui	a5,%hi(.LC9)
	lw	a2,%lo(.LC9)(a5)
	lw	a3,%lo(.LC9+4)(a5)
	lw	a0,280(sp)
	lw	a1,284(sp)
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,280(sp)
	sw	a5,284(sp)
	j	.L42
.L56:
	.loc 1 313 16
	lui	a5,%hi(.LC10)
	lw	a2,%lo(.LC10)(a5)
	lw	a3,%lo(.LC10+4)(a5)
	lw	a0,280(sp)
	lw	a1,284(sp)
	call	__ltdf2
	mv	a5,a0
	bge	a5,zero,.L42
	.loc 1 315 17
	lui	a5,%hi(.LC9)
	lw	a2,%lo(.LC9)(a5)
	lw	a3,%lo(.LC9+4)(a5)
	lw	a0,280(sp)
	lw	a1,284(sp)
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,280(sp)
	sw	a5,284(sp)
.L42:
	.loc 1 320 14
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	cos
	sw	a0,168(sp)
	sw	a1,172(sp)
	.loc 1 321 14
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	cos
	sw	a0,160(sp)
	sw	a1,164(sp)
	.loc 1 322 14
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,280(sp)
	lw	a1,284(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	cos
	sw	a0,152(sp)
	sw	a1,156(sp)
	.loc 1 324 14
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sin
	sw	a0,144(sp)
	sw	a1,148(sp)
	.loc 1 325 14
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sin
	sw	a0,136(sp)
	sw	a1,140(sp)
	.loc 1 326 14
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,280(sp)
	lw	a1,284(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sin
	sw	a0,128(sp)
	sw	a1,132(sp)
	.loc 1 328 17
	lw	a2,160(sp)
	lw	a3,164(sp)
	lw	a0,168(sp)
	lw	a1,172(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 328 20
	lw	a2,152(sp)
	lw	a3,156(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 328 30
	lw	a2,136(sp)
	lw	a3,140(sp)
	lw	a0,144(sp)
	lw	a1,148(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 328 33
	lw	a2,128(sp)
	lw	a3,132(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 328 25
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 328 13
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 329 17
	lw	a2,160(sp)
	lw	a3,164(sp)
	lw	a0,144(sp)
	lw	a1,148(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 329 20
	lw	a2,152(sp)
	lw	a3,156(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 329 30
	lw	a2,136(sp)
	lw	a3,140(sp)
	lw	a0,168(sp)
	lw	a1,172(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 329 33
	lw	a2,128(sp)
	lw	a3,132(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 329 25
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 329 13
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 330 17
	lw	a2,136(sp)
	lw	a3,140(sp)
	lw	a0,168(sp)
	lw	a1,172(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 330 20
	lw	a2,152(sp)
	lw	a3,156(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 330 30
	lw	a2,160(sp)
	lw	a3,164(sp)
	lw	a0,144(sp)
	lw	a1,148(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 330 33
	lw	a2,128(sp)
	lw	a3,132(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 330 25
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 330 13
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 331 17
	lw	a2,160(sp)
	lw	a3,164(sp)
	lw	a0,168(sp)
	lw	a1,172(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 331 20
	lw	a2,128(sp)
	lw	a3,132(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 331 30
	lw	a2,136(sp)
	lw	a3,140(sp)
	lw	a0,144(sp)
	lw	a1,148(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 331 33
	lw	a2,152(sp)
	lw	a3,156(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 331 25
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 331 13
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,24(a3)
	sw	a5,28(a3)
	.loc 1 333 30
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 333 32
	lw	a2,232(sp)
	lw	a3,236(sp)
	mv	a0,a4
	mv	a1,a5
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 333 20
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 334 30
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 334 32
	lw	a2,232(sp)
	lw	a3,236(sp)
	mv	a0,a4
	mv	a1,a5
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 334 20
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 335 30
	lui	a5,%hi(acc_mean)
	addi	a5,a5,%lo(acc_mean)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 335 32
	lw	a2,232(sp)
	lw	a3,236(sp)
	mv	a0,a4
	mv	a1,a5
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 335 20
	lui	a3,%hi(acc_mean)
	addi	a3,a3,%lo(acc_mean)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 339 24
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	li	a4,1
	sb	a4,84(a5)
	.loc 1 342 12
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	sw	a4,120(sp)
	sw	a5,124(sp)
	.loc 1 342 28
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	sw	a4,112(sp)
	sw	a5,116(sp)
	.loc 1 342 44
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	sw	a4,104(sp)
	sw	a5,108(sp)
	.loc 1 342 60
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	sw	a4,96(sp)
	sw	a5,100(sp)
	.loc 1 344 43
	lw	a2,96(sp)
	lw	a3,100(sp)
	lw	a0,112(sp)
	lw	a1,116(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 344 51
	lw	a2,104(sp)
	lw	a3,108(sp)
	lw	a0,120(sp)
	lw	a1,124(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 344 47
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 344 32
	lui	a5,%hi(.LC12)
	lw	a2,%lo(.LC12)(a5)
	lw	a3,%lo(.LC12+4)(a5)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	asin
	mv	a4,a0
	mv	a5,a1
	.loc 1 344 31 discriminator 1
	lui	a3,%hi(.LC13)
	lw	a2,%lo(.LC13)(a3)
	lw	a3,%lo(.LC13+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 344 22 discriminator 1
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,40(a3)
	sw	a5,44(a3)
	.loc 1 345 43
	lw	a2,96(sp)
	lw	a3,100(sp)
	lw	a0,104(sp)
	lw	a1,108(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 345 51
	lw	a2,112(sp)
	lw	a3,116(sp)
	lw	a0,120(sp)
	lw	a1,124(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 345 47
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 345 32
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 345 59
	lw	a2,120(sp)
	lw	a3,124(sp)
	lw	a0,120(sp)
	lw	a1,124(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 345 67
	lw	a2,112(sp)
	lw	a3,116(sp)
	lw	a0,112(sp)
	lw	a1,116(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 345 63
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 345 75
	lw	a2,104(sp)
	lw	a3,108(sp)
	lw	a0,104(sp)
	lw	a1,108(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 345 71
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 345 83
	lw	a2,96(sp)
	lw	a3,100(sp)
	lw	a0,96(sp)
	lw	a1,100(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 345 32
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	atan2
	mv	a4,a0
	mv	a5,a1
	.loc 1 345 31 discriminator 1
	lui	a3,%hi(.LC13)
	lw	a2,%lo(.LC13)(a3)
	lw	a3,%lo(.LC13+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 345 22 discriminator 1
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,32(a3)
	sw	a5,36(a3)
	.loc 1 346 43
	lw	a2,104(sp)
	lw	a3,108(sp)
	lw	a0,112(sp)
	lw	a1,116(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 346 51
	lw	a2,96(sp)
	lw	a3,100(sp)
	lw	a0,120(sp)
	lw	a1,124(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 346 47
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 346 32
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 346 58
	lw	a2,120(sp)
	lw	a3,124(sp)
	lw	a0,120(sp)
	lw	a1,124(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 346 66
	lw	a2,112(sp)
	lw	a3,116(sp)
	lw	a0,112(sp)
	lw	a1,116(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 346 62
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 346 74
	lw	a2,104(sp)
	lw	a3,108(sp)
	lw	a0,104(sp)
	lw	a1,108(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 346 70
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 346 82
	lw	a2,96(sp)
	lw	a3,100(sp)
	lw	a0,96(sp)
	lw	a1,100(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 346 32
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	atan2
	mv	a4,a0
	mv	a5,a1
	.loc 1 346 31 discriminator 1
	lui	a3,%hi(.LC13)
	lw	a2,%lo(.LC13)(a3)
	lw	a3,%lo(.LC13+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 346 22 discriminator 1
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,48(a3)
	sw	a5,52(a3)
	.loc 1 348 35
	lui	a5,%hi(.LC13)
	lw	a2,%lo(.LC13)(a5)
	lw	a3,%lo(.LC13+4)(a5)
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 348 26
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,64(a3)
	sw	a5,68(a3)
	.loc 1 349 35
	lui	a5,%hi(.LC13)
	lw	a2,%lo(.LC13)(a5)
	lw	a3,%lo(.LC13+4)(a5)
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 349 26
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,56(a3)
	sw	a5,60(a3)
	.loc 1 350 35
	lui	a5,%hi(.LC13)
	lw	a2,%lo(.LC13)(a5)
	lw	a3,%lo(.LC13+4)(a5)
	lw	a0,280(sp)
	lw	a1,284(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 350 26
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,72(a3)
	sw	a5,76(a3)
.L19:
.LBE2:
	.loc 1 353 16
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	sw	zero,80(a5)
	.loc 1 354 1
	nop
	lw	ra,364(sp)
	.cfi_restore 1
	lw	s0,360(sp)
	.cfi_restore 8
	lw	s1,356(sp)
	.cfi_restore 9
	lw	s2,352(sp)
	.cfi_restore 18
	lw	s3,348(sp)
	.cfi_restore 19
	lw	s4,344(sp)
	.cfi_restore 20
	lw	s5,340(sp)
	.cfi_restore 21
	lw	s6,336(sp)
	.cfi_restore 22
	lw	s7,332(sp)
	.cfi_restore 23
	lw	s8,328(sp)
	.cfi_restore 24
	lw	s9,324(sp)
	.cfi_restore 25
	lw	s10,320(sp)
	.cfi_restore 26
	lw	s11,316(sp)
	.cfi_restore 27
	addi	sp,sp,368
	.cfi_def_cfa_offset 0
	jr	ra
	.cfi_endproc
.LFE0:
	.size	ahrs_init, .-ahrs_init
	.section	.bss.dAng,"aw",@nobits
	.align	3
	.type	dAng, @object
	.size	dAng, 24
dAng:
	.zero	24
	.section	.text.ahrs_imu_update,"ax",@progbits
	.align	1
	.globl	ahrs_imu_update
	.type	ahrs_imu_update, @function
ahrs_imu_update:
.LFB1:
	.loc 1 360 1
	.cfi_startproc
	addi	sp,sp,-672
	.cfi_def_cfa_offset 672
	sw	ra,668(sp)
	sw	s0,664(sp)
	sw	s1,660(sp)
	sw	s2,656(sp)
	sw	s3,652(sp)
	sw	s4,648(sp)
	sw	s5,644(sp)
	sw	s6,640(sp)
	sw	s7,636(sp)
	.cfi_offset 1, -4
	.cfi_offset 8, -8
	.cfi_offset 9, -12
	.cfi_offset 18, -16
	.cfi_offset 19, -20
	.cfi_offset 20, -24
	.cfi_offset 21, -28
	.cfi_offset 22, -32
	.cfi_offset 23, -36
	sw	a0,76(sp)
	sw	a1,72(sp)
	.loc 1 361 11
	lui	a5,%hi(.LC14)
	lw	a4,%lo(.LC14)(a5)
	lw	a5,%lo(.LC14+4)(a5)
	sw	a4,584(sp)
	sw	a5,588(sp)
	.loc 1 362 11
	lw	a5,76(sp)
	lw	a4,176(a5)
	lw	a5,180(a5)
	sw	a4,576(sp)
	sw	a5,580(sp)
	.loc 1 363 11
	sw	zero,376(sp)
	sw	zero,380(sp)
	sw	zero,384(sp)
	sw	zero,388(sp)
	sw	zero,392(sp)
	sw	zero,396(sp)
	.loc 1 365 13
	sw	zero,344(sp)
	sw	zero,348(sp)
	sw	zero,352(sp)
	sw	zero,356(sp)
	sw	zero,360(sp)
	sw	zero,364(sp)
	sw	zero,368(sp)
	sw	zero,372(sp)
	.loc 1 366 11
	sw	zero,320(sp)
	sw	zero,324(sp)
	sw	zero,328(sp)
	sw	zero,332(sp)
	sw	zero,336(sp)
	sw	zero,340(sp)
	.loc 1 368 11
	li	a4,0
	li	a5,0
	sw	a4,568(sp)
	sw	a5,572(sp)
	.loc 1 368 20
	li	a4,0
	li	a5,0
	sw	a4,560(sp)
	sw	a5,564(sp)
	.loc 1 368 29
	li	a4,0
	li	a5,0
	sw	a4,552(sp)
	sw	a5,556(sp)
	.loc 1 368 38
	li	a4,0
	li	a5,0
	sw	a4,544(sp)
	sw	a5,548(sp)
	.loc 1 369 11
	addi	a5,sp,248
	li	a4,72
	mv	a2,a4
	li	a1,0
	mv	a0,a5
	call	memset
	.loc 1 371 11
	sw	zero,224(sp)
	sw	zero,228(sp)
	sw	zero,232(sp)
	sw	zero,236(sp)
	sw	zero,240(sp)
	sw	zero,244(sp)
	.loc 1 372 11
	sw	zero,200(sp)
	sw	zero,204(sp)
	sw	zero,208(sp)
	sw	zero,212(sp)
	sw	zero,216(sp)
	sw	zero,220(sp)
	.loc 1 373 11
	sw	zero,176(sp)
	sw	zero,180(sp)
	sw	zero,184(sp)
	sw	zero,188(sp)
	sw	zero,192(sp)
	sw	zero,196(sp)
	.loc 1 374 11
	sw	zero,144(sp)
	sw	zero,148(sp)
	sw	zero,152(sp)
	sw	zero,156(sp)
	sw	zero,160(sp)
	sw	zero,164(sp)
	sw	zero,168(sp)
	sw	zero,172(sp)
	.loc 1 375 11
	sw	zero,112(sp)
	sw	zero,116(sp)
	sw	zero,120(sp)
	sw	zero,124(sp)
	sw	zero,128(sp)
	sw	zero,132(sp)
	sw	zero,136(sp)
	sw	zero,140(sp)
	.loc 1 377 11
	li	a4,0
	li	a5,0
	sw	a4,536(sp)
	sw	a5,540(sp)
	.loc 1 379 11
	sw	zero,88(sp)
	sw	zero,92(sp)
	sw	zero,96(sp)
	sw	zero,100(sp)
	sw	zero,104(sp)
	sw	zero,108(sp)
	.loc 1 381 11
	li	a4,0
	li	a5,0
	sw	a4,616(sp)
	sw	a5,620(sp)
	.loc 1 381 20
	li	a4,0
	li	a5,0
	sw	a4,608(sp)
	sw	a5,612(sp)
	.loc 1 381 29
	li	a4,0
	li	a5,0
	sw	a4,600(sp)
	sw	a5,604(sp)
	.loc 1 381 38
	li	a4,0
	li	a5,0
	sw	a4,592(sp)
	sw	a5,596(sp)
	.loc 1 382 11
	li	a4,0
	li	a5,0
	sw	a4,528(sp)
	sw	a5,532(sp)
	.loc 1 382 20
	li	a4,0
	li	a5,0
	sw	a4,520(sp)
	sw	a5,524(sp)
	.loc 1 382 29
	li	a4,0
	li	a5,0
	sw	a4,512(sp)
	sw	a5,516(sp)
	.loc 1 384 22
	lui	a5,%hi(dAng)
	addi	a5,a5,%lo(dAng)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 384 16
	sw	a4,376(sp)
	sw	a5,380(sp)
	.loc 1 385 22
	lui	a5,%hi(dAng)
	addi	a5,a5,%lo(dAng)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 385 16
	sw	a4,384(sp)
	sw	a5,388(sp)
	.loc 1 386 22
	lui	a5,%hi(dAng)
	addi	a5,a5,%lo(dAng)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 386 16
	sw	a4,392(sp)
	sw	a5,396(sp)
	.loc 1 390 34
	lw	a3,76(sp)
	li	a4,0
	li	a5,0
	sw	a4,24(a3)
	sw	a5,28(a3)
	.loc 1 391 34
	lw	a3,76(sp)
	li	a4,0
	li	a5,0
	sw	a4,32(a3)
	sw	a5,36(a3)
	.loc 1 392 34
	lw	a3,76(sp)
	li	a4,0
	li	a5,0
	sw	a4,40(a3)
	sw	a5,44(a3)
	.loc 1 395 17
	lw	a5,72(sp)
	lw	a4,72(a5)
	lw	a5,76(a5)
	.loc 1 395 7
	li	a2,0
	li	a3,0
	mv	a0,a4
	mv	a1,a5
	call	__nedf2
	mv	a5,a0
	beq	a5,zero,.L71
	.loc 1 397 39
	lw	a5,72(sp)
	lw	a4,72(a5)
	lw	a5,76(a5)
	.loc 1 397 26
	lui	a3,%hi(.LC5)
	lw	a2,%lo(.LC5)(a3)
	lw	a3,%lo(.LC5+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	cos
	mv	a4,a0
	mv	a5,a1
	.loc 1 397 25 discriminator 1
	lui	a3,%hi(.LC15)
	lw	a2,%lo(.LC15)(a3)
	lw	a3,%lo(.LC15+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 397 19 discriminator 1
	sw	a4,344(sp)
	sw	a5,348(sp)
	.loc 1 398 19
	li	a4,0
	li	a5,0
	sw	a4,352(sp)
	sw	a5,356(sp)
	.loc 1 399 39
	lw	a5,72(sp)
	lw	a4,72(a5)
	lw	a5,76(a5)
	.loc 1 399 26
	lui	a3,%hi(.LC5)
	lw	a2,%lo(.LC5)(a3)
	lw	a3,%lo(.LC5+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sin
	mv	a4,a0
	mv	a5,a1
	.loc 1 399 25 discriminator 1
	lui	a3,%hi(.LC16)
	lw	a2,%lo(.LC16)(a3)
	lw	a3,%lo(.LC16+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 399 19 discriminator 1
	sw	a4,360(sp)
	sw	a5,364(sp)
	j	.L60
.L71:
	.loc 1 403 19
	li	a4,0
	li	a5,0
	sw	a4,344(sp)
	sw	a5,348(sp)
	.loc 1 404 19
	li	a4,0
	li	a5,0
	sw	a4,352(sp)
	sw	a5,356(sp)
	.loc 1 405 19
	li	a4,0
	li	a5,0
	sw	a4,360(sp)
	sw	a5,364(sp)
.L60:
	.loc 1 408 8
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	sw	a4,568(sp)
	sw	a5,572(sp)
	.loc 1 409 8
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	sw	a4,560(sp)
	sw	a5,564(sp)
	.loc 1 410 8
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	sw	a4,552(sp)
	sw	a5,556(sp)
	.loc 1 411 8
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	sw	a4,544(sp)
	sw	a5,548(sp)
	.loc 1 413 22
	lw	a2,568(sp)
	lw	a3,572(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 413 30
	lw	a2,560(sp)
	lw	a3,564(sp)
	lw	a0,560(sp)
	lw	a1,564(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 413 26
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 413 38
	lw	a2,552(sp)
	lw	a3,556(sp)
	lw	a0,552(sp)
	lw	a1,556(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 413 34
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 413 46
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,544(sp)
	lw	a1,548(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 413 42
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 413 18
	sw	a4,248(sp)
	sw	a5,252(sp)
	.loc 1 414 25
	lw	a2,552(sp)
	lw	a3,556(sp)
	lw	a0,560(sp)
	lw	a1,564(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 414 33
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 414 29
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 414 21
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 414 18
	sw	a4,256(sp)
	sw	a5,260(sp)
	.loc 1 415 25
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,560(sp)
	lw	a1,564(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 415 33
	lw	a2,552(sp)
	lw	a3,556(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 415 29
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 415 21
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 415 18
	sw	a4,264(sp)
	sw	a5,268(sp)
	.loc 1 417 25
	lw	a2,552(sp)
	lw	a3,556(sp)
	lw	a0,560(sp)
	lw	a1,564(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 417 33
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 417 29
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 417 21
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 417 18
	sw	a4,272(sp)
	sw	a5,276(sp)
	.loc 1 418 22
	lw	a2,568(sp)
	lw	a3,572(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 418 30
	lw	a2,560(sp)
	lw	a3,564(sp)
	lw	a0,560(sp)
	lw	a1,564(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 418 26
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 418 38
	lw	a2,552(sp)
	lw	a3,556(sp)
	lw	a0,552(sp)
	lw	a1,556(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 418 34
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 418 46
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,544(sp)
	lw	a1,548(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 418 42
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 418 18
	sw	a4,280(sp)
	sw	a5,284(sp)
	.loc 1 419 25
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,552(sp)
	lw	a1,556(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 419 33
	lw	a2,560(sp)
	lw	a3,564(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 419 29
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 419 21
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 419 18
	sw	a4,288(sp)
	sw	a5,292(sp)
	.loc 1 421 25
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,560(sp)
	lw	a1,564(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 421 33
	lw	a2,552(sp)
	lw	a3,556(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 421 29
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 421 21
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 421 18
	sw	a4,296(sp)
	sw	a5,300(sp)
	.loc 1 422 25
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,552(sp)
	lw	a1,556(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 422 33
	lw	a2,560(sp)
	lw	a3,564(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 422 29
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 422 21
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 422 18
	sw	a4,304(sp)
	sw	a5,308(sp)
	.loc 1 423 22
	lw	a2,568(sp)
	lw	a3,572(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 423 30
	lw	a2,560(sp)
	lw	a3,564(sp)
	lw	a0,560(sp)
	lw	a1,564(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 423 26
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 423 38
	lw	a2,552(sp)
	lw	a3,556(sp)
	lw	a0,552(sp)
	lw	a1,556(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 423 34
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 423 46
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,544(sp)
	lw	a1,548(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 423 42
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 423 18
	sw	a4,312(sp)
	sw	a5,316(sp)
	.loc 1 425 23
	lw	a4,248(sp)
	lw	a5,252(sp)
	.loc 1 425 34
	lw	a2,344(sp)
	lw	a3,348(sp)
	.loc 1 425 26
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 425 49
	lw	a4,256(sp)
	lw	a5,260(sp)
	.loc 1 425 60
	lw	a2,352(sp)
	lw	a3,356(sp)
	.loc 1 425 52
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 425 38
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 425 74
	lw	a4,264(sp)
	lw	a5,268(sp)
	.loc 1 425 85
	lw	a2,360(sp)
	lw	a3,364(sp)
	.loc 1 425 77
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 425 63
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 425 12
	sw	a4,320(sp)
	sw	a5,324(sp)
	.loc 1 426 20
	lw	a4,272(sp)
	lw	a5,276(sp)
	.loc 1 426 31
	lw	a2,344(sp)
	lw	a3,348(sp)
	.loc 1 426 23
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 426 46
	lw	a4,280(sp)
	lw	a5,284(sp)
	.loc 1 426 57
	lw	a2,352(sp)
	lw	a3,356(sp)
	.loc 1 426 49
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 426 35
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 426 71
	lw	a4,288(sp)
	lw	a5,292(sp)
	.loc 1 426 82
	lw	a2,360(sp)
	lw	a3,364(sp)
	.loc 1 426 74
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 426 60
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 426 9
	sw	a4,328(sp)
	sw	a5,332(sp)
	.loc 1 427 20
	lw	a4,296(sp)
	lw	a5,300(sp)
	.loc 1 427 31
	lw	a2,344(sp)
	lw	a3,348(sp)
	.loc 1 427 23
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 427 46
	lw	a4,304(sp)
	lw	a5,308(sp)
	.loc 1 427 57
	lw	a2,352(sp)
	lw	a3,356(sp)
	.loc 1 427 49
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 427 35
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 427 71
	lw	a4,312(sp)
	lw	a5,316(sp)
	.loc 1 427 82
	lw	a2,360(sp)
	lw	a3,364(sp)
	.loc 1 427 74
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 427 60
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 427 9
	sw	a4,336(sp)
	sw	a5,340(sp)
	.loc 1 433 30
	lw	a5,76(sp)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 433 61
	lw	a3,76(sp)
	lw	a2,24(a3)
	lw	a3,28(a3)
	.loc 1 433 33
	mv	a0,a4
	mv	a1,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 433 70
	lw	a4,320(sp)
	lw	a5,324(sp)
	.loc 1 433 64
	mv	a2,a4
	mv	a3,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 433 73
	lui	a5,%hi(.LC5)
	lw	a2,%lo(.LC5)(a5)
	lw	a3,%lo(.LC5+4)(a5)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 433 12
	sw	a4,224(sp)
	sw	a5,228(sp)
	.loc 1 434 30
	lw	a5,76(sp)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 434 61
	lw	a3,76(sp)
	lw	a2,32(a3)
	lw	a3,36(a3)
	.loc 1 434 33
	mv	a0,a4
	mv	a1,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 434 70
	lw	a4,328(sp)
	lw	a5,332(sp)
	.loc 1 434 64
	mv	a2,a4
	mv	a3,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 434 73
	lui	a5,%hi(.LC5)
	lw	a2,%lo(.LC5)(a5)
	lw	a3,%lo(.LC5+4)(a5)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 434 12
	sw	a4,232(sp)
	sw	a5,236(sp)
	.loc 1 435 30
	lw	a5,76(sp)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 435 61
	lw	a3,76(sp)
	lw	a2,40(a3)
	lw	a3,44(a3)
	.loc 1 435 33
	mv	a0,a4
	mv	a1,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 435 70
	lw	a4,336(sp)
	lw	a5,340(sp)
	.loc 1 435 64
	mv	a2,a4
	mv	a3,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 435 73
	lui	a5,%hi(.LC5)
	lw	a2,%lo(.LC5)(a5)
	lw	a3,%lo(.LC5+4)(a5)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 435 12
	sw	a4,240(sp)
	sw	a5,244(sp)
	.loc 1 437 19
	lw	a4,224(sp)
	lw	a5,228(sp)
	.loc 1 437 21
	lw	a2,576(sp)
	lw	a3,580(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 437 12
	lui	a3,%hi(dAng)
	addi	a3,a3,%lo(dAng)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 438 19
	lw	a4,232(sp)
	lw	a5,236(sp)
	.loc 1 438 21
	lw	a2,576(sp)
	lw	a3,580(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 438 12
	lui	a3,%hi(dAng)
	addi	a3,a3,%lo(dAng)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 439 19
	lw	a4,240(sp)
	lw	a5,244(sp)
	.loc 1 439 21
	lw	a2,576(sp)
	lw	a3,580(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 439 12
	lui	a3,%hi(dAng)
	addi	a3,a3,%lo(dAng)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 443 20
	addi	a6,sp,200
	lw	a0,376(sp)
	lw	a1,380(sp)
	lw	a2,384(sp)
	lw	a3,388(sp)
	lw	a4,392(sp)
	lw	a5,396(sp)
	sw	a0,32(sp)
	sw	a1,36(sp)
	sw	a2,40(sp)
	sw	a3,44(sp)
	sw	a4,48(sp)
	sw	a5,52(sp)
	lui	a5,%hi(dAng)
	addi	a5,a5,%lo(dAng)
	lw	a0,0(a5)
	lw	a1,4(a5)
	lw	a2,8(a5)
	lw	a3,12(a5)
	lw	a4,16(a5)
	lw	a5,20(a5)
	sw	a0,0(sp)
	sw	a1,4(sp)
	sw	a2,8(sp)
	sw	a3,12(sp)
	sw	a4,16(sp)
	sw	a5,20(sp)
	mv	a4,sp
	addi	a5,sp,32
	mv	a2,a4
	mv	a1,a5
	mv	a0,a6
	call	v3_cross
	.loc 1 445 17
	lui	a5,%hi(dAng)
	addi	a5,a5,%lo(dAng)
	lw	s4,0(a5)
	lw	s5,4(a5)
	.loc 1 445 37
	lw	a4,200(sp)
	lw	a5,204(sp)
	.loc 1 445 25
	lui	a3,%hi(.LC17)
	lw	a2,%lo(.LC17)(a3)
	lw	a3,%lo(.LC17+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 445 20
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 445 11
	sw	a4,176(sp)
	sw	a5,180(sp)
	.loc 1 446 17
	lui	a5,%hi(dAng)
	addi	a5,a5,%lo(dAng)
	lw	s4,8(a5)
	lw	s5,12(a5)
	.loc 1 446 37
	lw	a4,208(sp)
	lw	a5,212(sp)
	.loc 1 446 25
	lui	a3,%hi(.LC17)
	lw	a2,%lo(.LC17)(a3)
	lw	a3,%lo(.LC17+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 446 20
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 446 11
	sw	a4,184(sp)
	sw	a5,188(sp)
	.loc 1 447 17
	lui	a5,%hi(dAng)
	addi	a5,a5,%lo(dAng)
	lw	s4,16(a5)
	lw	s5,20(a5)
	.loc 1 447 37
	lw	a4,216(sp)
	lw	a5,220(sp)
	.loc 1 447 25
	lui	a3,%hi(.LC17)
	lw	a2,%lo(.LC17)(a3)
	lw	a3,%lo(.LC17+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 447 20
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 447 11
	sw	a4,192(sp)
	sw	a5,196(sp)
	.loc 1 451 28
	lw	a4,176(sp)
	lw	a5,180(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	s4,a0
	mv	s5,a1
	.loc 1 451 41 discriminator 1
	lw	a4,184(sp)
	lw	a5,188(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 451 39 discriminator 2
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 451 54 discriminator 2
	lw	a4,192(sp)
	lw	a5,196(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 451 23 discriminator 3
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sqrt
	sw	a0,504(sp)
	sw	a1,508(sp)
	.loc 1 452 7
	lui	a5,%hi(.LC6)
	lw	a2,%lo(.LC6)(a5)
	lw	a3,%lo(.LC6+4)(a5)
	lw	a0,504(sp)
	lw	a1,508(sp)
	call	__ltdf2
	mv	a5,a0
	bge	a5,zero,.L72
	.loc 1 454 15
	lui	a5,%hi(.LC18)
	lw	a4,%lo(.LC18)(a5)
	lw	a5,%lo(.LC18+4)(a5)
	sw	a4,144(sp)
	sw	a5,148(sp)
	.loc 1 455 15
	li	a4,0
	li	a5,0
	sw	a4,152(sp)
	sw	a5,156(sp)
	.loc 1 456 15
	li	a4,0
	li	a5,0
	sw	a4,160(sp)
	sw	a5,164(sp)
	.loc 1 457 15
	li	a4,0
	li	a5,0
	sw	a4,168(sp)
	sw	a5,172(sp)
	j	.L63
.L72:
	.loc 1 464 17
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,504(sp)
	lw	a1,508(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	cos
	mv	a4,a0
	mv	a5,a1
	.loc 1 464 15 discriminator 1
	sw	a4,144(sp)
	sw	a5,148(sp)
	.loc 1 465 17
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,504(sp)
	lw	a1,508(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sin
	.loc 1 465 39 discriminator 1
	lw	a4,176(sp)
	lw	a5,180(sp)
	.loc 1 465 35 discriminator 1
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 465 41 discriminator 1
	lw	a2,504(sp)
	lw	a3,508(sp)
	mv	a0,a4
	mv	a1,a5
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 465 15 discriminator 1
	sw	a4,152(sp)
	sw	a5,156(sp)
	.loc 1 466 17
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,504(sp)
	lw	a1,508(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sin
	.loc 1 466 39 discriminator 1
	lw	a4,184(sp)
	lw	a5,188(sp)
	.loc 1 466 35 discriminator 1
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 466 41 discriminator 1
	lw	a2,504(sp)
	lw	a3,508(sp)
	mv	a0,a4
	mv	a1,a5
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 466 15 discriminator 1
	sw	a4,160(sp)
	sw	a5,164(sp)
	.loc 1 467 17
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,504(sp)
	lw	a1,508(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sin
	.loc 1 467 39 discriminator 1
	lw	a4,192(sp)
	lw	a5,196(sp)
	.loc 1 467 35 discriminator 1
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 467 41 discriminator 1
	lw	a2,504(sp)
	lw	a3,508(sp)
	mv	a0,a4
	mv	a1,a5
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 467 15 discriminator 1
	sw	a4,168(sp)
	sw	a5,172(sp)
.L63:
	.loc 1 471 25
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 471 31
	lw	a2,144(sp)
	lw	a3,148(sp)
	.loc 1 471 28
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 471 43
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 471 49
	lw	a2,152(sp)
	lw	a3,156(sp)
	.loc 1 471 46
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 471 35
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 471 61
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 471 67
	lw	a2,160(sp)
	lw	a3,164(sp)
	.loc 1 471 64
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 471 53
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 471 79
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 471 85
	lw	a2,168(sp)
	lw	a3,172(sp)
	.loc 1 471 82
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 471 71
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 471 17
	sw	a4,112(sp)
	sw	a5,116(sp)
	.loc 1 472 25
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 472 31
	lw	a2,152(sp)
	lw	a3,156(sp)
	.loc 1 472 28
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 472 43
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 472 49
	lw	a2,144(sp)
	lw	a3,148(sp)
	.loc 1 472 46
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 472 35
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 472 61
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 472 67
	lw	a2,168(sp)
	lw	a3,172(sp)
	.loc 1 472 64
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 472 53
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 472 79
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 472 85
	lw	a2,160(sp)
	lw	a3,164(sp)
	.loc 1 472 82
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 472 71
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 472 17
	sw	a4,120(sp)
	sw	a5,124(sp)
	.loc 1 473 25
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 473 31
	lw	a2,160(sp)
	lw	a3,164(sp)
	.loc 1 473 28
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 473 43
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 473 49
	lw	a2,168(sp)
	lw	a3,172(sp)
	.loc 1 473 46
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 473 35
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 473 61
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 473 67
	lw	a2,144(sp)
	lw	a3,148(sp)
	.loc 1 473 64
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 473 53
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 473 79
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 473 85
	lw	a2,152(sp)
	lw	a3,156(sp)
	.loc 1 473 82
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 473 71
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 473 17
	sw	a4,128(sp)
	sw	a5,132(sp)
	.loc 1 474 25
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 474 31
	lw	a2,168(sp)
	lw	a3,172(sp)
	.loc 1 474 28
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 474 43
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 474 49
	lw	a2,160(sp)
	lw	a3,164(sp)
	.loc 1 474 46
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 474 35
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 474 61
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 474 67
	lw	a2,152(sp)
	lw	a3,156(sp)
	.loc 1 474 64
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 474 53
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 474 79
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 474 85
	lw	a2,144(sp)
	lw	a3,148(sp)
	.loc 1 474 82
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 474 71
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 474 17
	sw	a4,136(sp)
	sw	a5,140(sp)
	.loc 1 476 26
	lw	a4,112(sp)
	lw	a5,116(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	s4,a0
	mv	s5,a1
	.loc 1 476 45 discriminator 1
	lw	a4,120(sp)
	lw	a5,124(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 476 43 discriminator 2
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 476 64 discriminator 2
	lw	a4,128(sp)
	lw	a5,132(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 476 62 discriminator 3
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 476 83 discriminator 3
	lw	a4,136(sp)
	lw	a5,140(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 476 21 discriminator 4
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sqrt
	mv	a4,a0
	mv	a5,a1
	.loc 1 476 15 discriminator 5
	lui	a1,%hi(.LC18)
	mv	a2,a4
	mv	a3,a5
	lw	a0,%lo(.LC18)(a1)
	lw	a1,%lo(.LC18+4)(a1)
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,536(sp)
	sw	a5,540(sp)
	.loc 1 477 27
	lw	a4,112(sp)
	lw	a5,116(sp)
	.loc 1 477 30
	lw	a2,536(sp)
	lw	a3,540(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 477 17
	sw	a4,112(sp)
	sw	a5,116(sp)
	.loc 1 478 27
	lw	a4,120(sp)
	lw	a5,124(sp)
	.loc 1 478 30
	lw	a2,536(sp)
	lw	a3,540(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 478 17
	sw	a4,120(sp)
	sw	a5,124(sp)
	.loc 1 479 27
	lw	a4,128(sp)
	lw	a5,132(sp)
	.loc 1 479 30
	lw	a2,536(sp)
	lw	a3,540(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 479 17
	sw	a4,128(sp)
	sw	a5,132(sp)
	.loc 1 480 27
	lw	a4,136(sp)
	lw	a5,140(sp)
	.loc 1 480 30
	lw	a2,536(sp)
	lw	a3,540(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 480 17
	sw	a4,136(sp)
	sw	a5,140(sp)
	.loc 1 484 28
	lw	a5,76(sp)
	lw	a4,48(a5)
	lw	a5,52(a5)
	.loc 1 484 58
	lw	a3,76(sp)
	lw	a2,72(a3)
	lw	a3,76(a3)
	.loc 1 484 31
	mv	a0,a4
	mv	a1,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 484 12
	sw	a4,88(sp)
	sw	a5,92(sp)
	.loc 1 485 28
	lw	a5,76(sp)
	lw	a4,56(a5)
	lw	a5,60(a5)
	.loc 1 485 58
	lw	a3,76(sp)
	lw	a2,80(a3)
	lw	a3,84(a3)
	.loc 1 485 31
	mv	a0,a4
	mv	a1,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 485 12
	sw	a4,96(sp)
	sw	a5,100(sp)
	.loc 1 486 28
	lw	a5,76(sp)
	lw	a4,64(a5)
	lw	a5,68(a5)
	.loc 1 486 58
	lw	a3,76(sp)
	lw	a2,88(a3)
	lw	a3,92(a3)
	.loc 1 486 31
	mv	a0,a4
	mv	a1,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 486 12
	sw	a4,104(sp)
	sw	a5,108(sp)
	.loc 1 488 8
	li	a4,0
	li	a5,0
	sw	a4,616(sp)
	sw	a5,620(sp)
	.loc 1 488 17
	li	a4,0
	li	a5,0
	sw	a4,608(sp)
	sw	a5,612(sp)
	.loc 1 488 26
	li	a4,0
	li	a5,0
	sw	a4,600(sp)
	sw	a5,604(sp)
	.loc 1 488 35
	li	a4,0
	li	a5,0
	sw	a4,592(sp)
	sw	a5,596(sp)
	.loc 1 490 22
	lw	a4,88(sp)
	lw	a5,92(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	s4,a0
	mv	s5,a1
	.loc 1 490 33 discriminator 1
	lw	a4,96(sp)
	lw	a5,100(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 490 32 discriminator 2
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 490 44 discriminator 2
	lw	a4,104(sp)
	lw	a5,108(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 490 17 discriminator 3
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sqrt
	sw	a0,536(sp)
	sw	a1,540(sp)
	.loc 1 491 7
	lui	a5,%hi(.LC19)
	lw	a2,%lo(.LC19)(a5)
	lw	a3,%lo(.LC19+4)(a5)
	lw	a0,536(sp)
	lw	a1,540(sp)
	call	__gtdf2
	mv	a5,a0
	ble	a5,zero,.L64
	.loc 1 491 23 discriminator 1
	lui	a5,%hi(.LC20)
	lw	a2,%lo(.LC20)(a5)
	lw	a3,%lo(.LC20+4)(a5)
	lw	a0,536(sp)
	lw	a1,540(sp)
	call	__ltdf2
	mv	a5,a0
	bge	a5,zero,.L64
.LBB3:
	.loc 1 494 30
	lw	a4,88(sp)
	lw	a5,92(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	s4,a0
	mv	s5,a1
	.loc 1 494 41 discriminator 1
	lw	a4,96(sp)
	lw	a5,100(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 494 40 discriminator 2
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 494 52 discriminator 2
	lw	a4,104(sp)
	lw	a5,108(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 494 25 discriminator 3
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sqrt
	mv	a4,a0
	mv	a5,a1
	.loc 1 494 19 discriminator 4
	lui	a1,%hi(.LC18)
	mv	a2,a4
	mv	a3,a5
	lw	a0,%lo(.LC18)(a1)
	lw	a1,%lo(.LC18+4)(a1)
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,536(sp)
	sw	a5,540(sp)
	.loc 1 495 17
	lw	a4,88(sp)
	lw	a5,92(sp)
	.loc 1 495 12
	mv	a2,a4
	mv	a3,a5
	lw	a0,536(sp)
	lw	a1,540(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,528(sp)
	sw	a5,532(sp)
	.loc 1 496 17
	lw	a4,96(sp)
	lw	a5,100(sp)
	.loc 1 496 12
	mv	a2,a4
	mv	a3,a5
	lw	a0,536(sp)
	lw	a1,540(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,520(sp)
	sw	a5,524(sp)
	.loc 1 497 17
	lw	a4,104(sp)
	lw	a5,108(sp)
	.loc 1 497 12
	mv	a2,a4
	mv	a3,a5
	lw	a0,536(sp)
	lw	a1,540(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,512(sp)
	sw	a5,516(sp)
	.loc 1 501 33
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 501 15
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,496(sp)
	sw	a5,500(sp)
	.loc 1 502 33
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 502 15
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,488(sp)
	sw	a5,492(sp)
	.loc 1 503 33
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 503 15
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,480(sp)
	sw	a5,484(sp)
	.loc 1 504 33
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 504 15
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,472(sp)
	sw	a5,476(sp)
	.loc 1 505 33
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 505 15
	lui	a3,%hi(.LC21)
	lw	a2,%lo(.LC21)(a3)
	lw	a3,%lo(.LC21+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,464(sp)
	sw	a5,468(sp)
	.loc 1 506 33
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 506 15
	lui	a3,%hi(.LC21)
	lw	a2,%lo(.LC21)(a3)
	lw	a3,%lo(.LC21+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,456(sp)
	sw	a5,460(sp)
	.loc 1 507 33
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 507 15
	lui	a3,%hi(.LC21)
	lw	a2,%lo(.LC21)(a3)
	lw	a3,%lo(.LC21+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,448(sp)
	sw	a5,452(sp)
	.loc 1 508 33
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 508 15
	lui	a3,%hi(.LC22)
	lw	a2,%lo(.LC22)(a3)
	lw	a3,%lo(.LC22+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,440(sp)
	sw	a5,444(sp)
	.loc 1 509 33
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 509 15
	lui	a3,%hi(.LC22)
	lw	a2,%lo(.LC22)(a3)
	lw	a3,%lo(.LC22+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,432(sp)
	sw	a5,436(sp)
	.loc 1 511 28
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 511 40
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	lw	a2,0(a3)
	lw	a3,4(a3)
	.loc 1 511 15
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,424(sp)
	sw	a5,428(sp)
	.loc 1 512 28
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 512 40
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	lw	a2,8(a3)
	lw	a3,12(a3)
	.loc 1 512 15
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,416(sp)
	sw	a5,420(sp)
	.loc 1 513 28
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 513 40
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	lw	a2,16(a3)
	lw	a3,20(a3)
	.loc 1 513 15
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,408(sp)
	sw	a5,412(sp)
	.loc 1 514 28
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 514 40
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	lw	a2,24(a3)
	lw	a3,28(a3)
	.loc 1 514 15
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,400(sp)
	sw	a5,404(sp)
	.loc 1 517 18
	lw	a2,408(sp)
	lw	a3,412(sp)
	lw	a0,464(sp)
	lw	a1,468(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 517 31
	lw	a2,528(sp)
	lw	a3,532(sp)
	lw	a0,480(sp)
	lw	a1,484(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 517 25
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 517 42
	lw	a2,416(sp)
	lw	a3,420(sp)
	lw	a0,464(sp)
	lw	a1,468(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 517 36
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 517 55
	lw	a2,520(sp)
	lw	a3,524(sp)
	lw	a0,488(sp)
	lw	a1,492(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 517 12
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,616(sp)
	sw	a5,620(sp)
	.loc 1 518 18
	lw	a2,400(sp)
	lw	a3,404(sp)
	lw	a0,456(sp)
	lw	a1,460(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 518 31
	lw	a2,528(sp)
	lw	a3,532(sp)
	lw	a0,472(sp)
	lw	a1,476(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 518 25
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 518 42
	lui	a5,%hi(.LC21)
	lw	a2,%lo(.LC21)(a5)
	lw	a3,%lo(.LC21+4)(a5)
	lw	a0,424(sp)
	lw	a1,428(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 518 57
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 518 49
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 518 36
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 518 67
	lw	a2,520(sp)
	lw	a3,524(sp)
	lw	a0,496(sp)
	lw	a1,500(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 518 61
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 518 72
	lw	a2,456(sp)
	lw	a3,460(sp)
	mv	a0,a4
	mv	a1,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 518 84
	lw	a2,416(sp)
	lw	a3,420(sp)
	lw	a0,440(sp)
	lw	a1,444(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 518 78
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 518 97
	lw	a2,408(sp)
	lw	a3,412(sp)
	lw	a0,440(sp)
	lw	a1,444(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 518 91
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 518 110
	lw	a2,512(sp)
	lw	a3,516(sp)
	lw	a0,456(sp)
	lw	a1,460(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 518 12
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,608(sp)
	sw	a5,612(sp)
	.loc 1 519 18
	lui	a5,%hi(.LC21)
	lw	a2,%lo(.LC21)(a5)
	lw	a3,%lo(.LC21+4)(a5)
	lw	a0,424(sp)
	lw	a1,428(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 519 33
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 519 25
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 519 43
	lw	a2,528(sp)
	lw	a3,532(sp)
	lw	a0,496(sp)
	lw	a1,500(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 519 37
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 519 54
	lw	a2,400(sp)
	lw	a3,404(sp)
	lw	a0,448(sp)
	lw	a1,452(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 519 48
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 519 67
	lw	a2,520(sp)
	lw	a3,524(sp)
	lw	a0,472(sp)
	lw	a1,476(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 519 61
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 519 72
	lw	a2,448(sp)
	lw	a3,452(sp)
	mv	a0,a4
	mv	a1,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 519 84
	lw	a2,416(sp)
	lw	a3,420(sp)
	lw	a0,432(sp)
	lw	a1,436(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 519 78
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 519 97
	lw	a2,408(sp)
	lw	a3,412(sp)
	lw	a0,432(sp)
	lw	a1,436(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 519 91
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 519 110
	lw	a2,512(sp)
	lw	a3,516(sp)
	lw	a0,448(sp)
	lw	a1,452(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 519 12
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,600(sp)
	sw	a5,604(sp)
	.loc 1 520 18
	lui	a5,%hi(.LC21)
	lw	a2,%lo(.LC21)(a5)
	lw	a3,%lo(.LC21+4)(a5)
	lw	a0,416(sp)
	lw	a1,420(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 520 33
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 520 25
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 520 43
	lw	a2,528(sp)
	lw	a3,532(sp)
	lw	a0,488(sp)
	lw	a1,492(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 520 37
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 520 54
	lui	a5,%hi(.LC21)
	lw	a2,%lo(.LC21)(a5)
	lw	a3,%lo(.LC21+4)(a5)
	lw	a0,408(sp)
	lw	a1,412(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 520 69
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 520 61
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 520 48
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 520 79
	lw	a2,520(sp)
	lw	a3,524(sp)
	lw	a0,480(sp)
	lw	a1,484(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 520 12
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,592(sp)
	sw	a5,596(sp)
	.loc 1 523 30
	lw	a0,616(sp)
	lw	a1,620(sp)
	call	SQR
	mv	s4,a0
	mv	s5,a1
	.loc 1 523 41 discriminator 1
	lw	a0,608(sp)
	lw	a1,612(sp)
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 523 39 discriminator 2
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 523 52 discriminator 2
	lw	a0,600(sp)
	lw	a1,604(sp)
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 523 50 discriminator 3
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 523 63 discriminator 3
	lw	a0,592(sp)
	lw	a1,596(sp)
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 523 25 discriminator 4
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sqrt
	mv	a4,a0
	mv	a5,a1
	.loc 1 523 19 discriminator 5
	lui	a1,%hi(.LC18)
	mv	a2,a4
	mv	a3,a5
	lw	a0,%lo(.LC18)(a1)
	lw	a1,%lo(.LC18+4)(a1)
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,536(sp)
	sw	a5,540(sp)
	.loc 1 524 12
	lw	a2,536(sp)
	lw	a3,540(sp)
	lw	a0,616(sp)
	lw	a1,620(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,616(sp)
	sw	a5,620(sp)
	.loc 1 525 12
	lw	a2,536(sp)
	lw	a3,540(sp)
	lw	a0,608(sp)
	lw	a1,612(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,608(sp)
	sw	a5,612(sp)
	.loc 1 526 12
	lw	a2,536(sp)
	lw	a3,540(sp)
	lw	a0,600(sp)
	lw	a1,604(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,600(sp)
	sw	a5,604(sp)
	.loc 1 527 12
	lw	a2,536(sp)
	lw	a3,540(sp)
	lw	a0,592(sp)
	lw	a1,596(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,592(sp)
	sw	a5,596(sp)
.L64:
.LBE3:
	.loc 1 532 25
	lw	s4,112(sp)
	lw	s5,116(sp)
	.loc 1 532 36
	lw	a2,616(sp)
	lw	a3,620(sp)
	lw	a0,584(sp)
	lw	a1,588(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 532 40
	lw	a2,576(sp)
	lw	a3,580(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 532 29
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 532 15
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 533 25
	lw	s4,120(sp)
	lw	s5,124(sp)
	.loc 1 533 36
	lw	a2,608(sp)
	lw	a3,612(sp)
	lw	a0,584(sp)
	lw	a1,588(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 533 40
	lw	a2,576(sp)
	lw	a3,580(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 533 29
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 533 15
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 534 25
	lw	s4,128(sp)
	lw	s5,132(sp)
	.loc 1 534 36
	lw	a2,600(sp)
	lw	a3,604(sp)
	lw	a0,584(sp)
	lw	a1,588(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 534 40
	lw	a2,576(sp)
	lw	a3,580(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 534 29
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 534 15
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 535 25
	lw	s4,136(sp)
	lw	s5,140(sp)
	.loc 1 535 36
	lw	a2,592(sp)
	lw	a3,596(sp)
	lw	a0,584(sp)
	lw	a1,588(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 535 40
	lw	a2,576(sp)
	lw	a3,580(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 535 29
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 535 15
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,24(a3)
	sw	a5,28(a3)
	.loc 1 537 26
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	s4,a0
	mv	s5,a1
	.loc 1 537 43 discriminator 1
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 537 41 discriminator 2
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 537 60 discriminator 2
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 537 58 discriminator 3
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 537 77 discriminator 3
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 537 21 discriminator 4
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sqrt
	mv	a4,a0
	mv	a5,a1
	.loc 1 537 15 discriminator 5
	lui	a1,%hi(.LC18)
	mv	a2,a4
	mv	a3,a5
	lw	a0,%lo(.LC18)(a1)
	lw	a1,%lo(.LC18+4)(a1)
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,536(sp)
	sw	a5,540(sp)
	.loc 1 538 23
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 538 26
	lw	a2,536(sp)
	lw	a3,540(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 538 15
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 539 23
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 539 26
	lw	a2,536(sp)
	lw	a3,540(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 539 15
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 540 23
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 540 26
	lw	a2,536(sp)
	lw	a3,540(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 540 15
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 541 23
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 541 26
	lw	a2,536(sp)
	lw	a3,540(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 541 15
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,24(a3)
	sw	a5,28(a3)
	.loc 1 557 8
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	sw	a4,568(sp)
	sw	a5,572(sp)
	.loc 1 557 24
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	sw	a4,560(sp)
	sw	a5,564(sp)
	.loc 1 557 40
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	sw	a4,552(sp)
	sw	a5,556(sp)
	.loc 1 557 56
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	sw	a4,544(sp)
	sw	a5,548(sp)
	.loc 1 559 39
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,560(sp)
	lw	a1,564(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 559 47
	lw	a2,552(sp)
	lw	a3,556(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 559 43
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 559 28
	lui	a5,%hi(.LC12)
	lw	a2,%lo(.LC12)(a5)
	lw	a3,%lo(.LC12+4)(a5)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	asin
	mv	a4,a0
	mv	a5,a1
	.loc 1 559 27 discriminator 1
	lui	a3,%hi(.LC13)
	lw	a2,%lo(.LC13)(a3)
	lw	a3,%lo(.LC13+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 559 18 discriminator 1
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,40(a3)
	sw	a5,44(a3)
	.loc 1 560 39
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,552(sp)
	lw	a1,556(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 560 47
	lw	a2,560(sp)
	lw	a3,564(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 560 43
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 560 28
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 560 66
	lw	a2,560(sp)
	lw	a3,564(sp)
	lw	a0,560(sp)
	lw	a1,564(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s6,a4
	mv	s7,a5
	.loc 1 560 74
	lw	a2,552(sp)
	lw	a3,556(sp)
	lw	a0,552(sp)
	lw	a1,556(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 560 70
	mv	a2,a4
	mv	a3,a5
	mv	a0,s6
	mv	a1,s7
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 560 62
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	.loc 1 560 28
	lui	a5,%hi(.LC18)
	lw	a0,%lo(.LC18)(a5)
	lw	a1,%lo(.LC18+4)(a5)
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	atan2
	mv	a4,a0
	mv	a5,a1
	.loc 1 560 27 discriminator 1
	lui	a3,%hi(.LC13)
	lw	a2,%lo(.LC13)(a3)
	lw	a3,%lo(.LC13+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 560 18 discriminator 1
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,32(a3)
	sw	a5,36(a3)
	.loc 1 561 39
	lw	a2,552(sp)
	lw	a3,556(sp)
	lw	a0,560(sp)
	lw	a1,564(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 561 47
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,568(sp)
	lw	a1,572(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 561 43
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 561 28
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 561 66
	lw	a2,552(sp)
	lw	a3,556(sp)
	lw	a0,552(sp)
	lw	a1,556(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s6,a4
	mv	s7,a5
	.loc 1 561 74
	lw	a2,544(sp)
	lw	a3,548(sp)
	lw	a0,544(sp)
	lw	a1,548(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 561 70
	mv	a2,a4
	mv	a3,a5
	mv	a0,s6
	mv	a1,s7
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 561 62
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	.loc 1 561 28
	lui	a5,%hi(.LC18)
	lw	a0,%lo(.LC18)(a5)
	lw	a1,%lo(.LC18+4)(a5)
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	atan2
	mv	a4,a0
	mv	a5,a1
	.loc 1 561 27 discriminator 1
	lui	a3,%hi(.LC13)
	lw	a2,%lo(.LC13)(a3)
	lw	a3,%lo(.LC13+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 561 18 discriminator 1
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,48(a3)
	sw	a5,52(a3)
	.loc 1 563 42
	lw	a4,96(sp)
	lw	a5,100(sp)
	.loc 1 563 32
	mv	s0,a4
	li	a3,-2147483648
	xor	s1,a5,a3
	.loc 1 563 49
	lw	a4,104(sp)
	lw	a5,108(sp)
	.loc 1 563 32
	mv	s2,a4
	li	a3,-2147483648
	xor	s3,a5,a3
	mv	a2,s2
	mv	a3,s3
	mv	a0,s0
	mv	a1,s1
	call	atan2
	mv	a4,a0
	mv	a5,a1
	.loc 1 563 31 discriminator 1
	lui	a3,%hi(.LC13)
	lw	a2,%lo(.LC13)(a3)
	lw	a3,%lo(.LC13+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 563 22 discriminator 1
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,56(a3)
	sw	a5,60(a3)
	.loc 1 564 32
	lw	a4,88(sp)
	lw	a5,92(sp)
	mv	a0,a4
	mv	a1,a5
	call	asin
	mv	a4,a0
	mv	a5,a1
	.loc 1 564 31 discriminator 1
	lui	a3,%hi(.LC13)
	lw	a2,%lo(.LC13)(a3)
	lw	a3,%lo(.LC13+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 564 22 discriminator 1
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,64(a3)
	sw	a5,68(a3)
	.loc 1 565 1
	nop
	lw	ra,668(sp)
	.cfi_restore 1
	lw	s0,664(sp)
	.cfi_restore 8
	lw	s1,660(sp)
	.cfi_restore 9
	lw	s2,656(sp)
	.cfi_restore 18
	lw	s3,652(sp)
	.cfi_restore 19
	lw	s4,648(sp)
	.cfi_restore 20
	lw	s5,644(sp)
	.cfi_restore 21
	lw	s6,640(sp)
	.cfi_restore 22
	lw	s7,636(sp)
	.cfi_restore 23
	addi	sp,sp,672
	.cfi_def_cfa_offset 0
	jr	ra
	.cfi_endproc
.LFE1:
	.size	ahrs_imu_update, .-ahrs_imu_update
	.section	.sbss.yaw_unfusion_cnt,"aw",@nobits
	.align	2
	.type	yaw_unfusion_cnt, @object
	.size	yaw_unfusion_cnt, 4
yaw_unfusion_cnt:
	.zero	4
	.section	.text.ahrs_mag_update,"ax",@progbits
	.align	1
	.globl	ahrs_mag_update
	.type	ahrs_mag_update, @function
ahrs_mag_update:
.LFB2:
	.loc 1 569 1
	.cfi_startproc
	addi	sp,sp,-336
	.cfi_def_cfa_offset 336
	sw	ra,332(sp)
	sw	s0,328(sp)
	sw	s1,324(sp)
	sw	s2,320(sp)
	sw	s3,316(sp)
	sw	s4,312(sp)
	sw	s5,308(sp)
	sw	s6,304(sp)
	sw	s7,300(sp)
	sw	s8,296(sp)
	sw	s9,292(sp)
	.cfi_offset 1, -4
	.cfi_offset 8, -8
	.cfi_offset 9, -12
	.cfi_offset 18, -16
	.cfi_offset 19, -20
	.cfi_offset 20, -24
	.cfi_offset 21, -28
	.cfi_offset 22, -32
	.cfi_offset 23, -36
	.cfi_offset 24, -40
	.cfi_offset 25, -44
	sw	a0,12(sp)
	sw	a1,8(sp)
	sw	a2,4(sp)
	.loc 1 570 14
	sh	zero,286(sp)
	.loc 1 571 17
	lui	a5,%hi(.LC23)
	lw	a4,%lo(.LC23)(a5)
	lw	a5,%lo(.LC23+4)(a5)
	sw	a4,240(sp)
	sw	a5,244(sp)
	.loc 1 572 11
	li	a4,0
	li	a5,0
	sw	a4,232(sp)
	sw	a5,236(sp)
	.loc 1 572 20
	li	a4,0
	li	a5,0
	sw	a4,224(sp)
	sw	a5,228(sp)
	.loc 1 572 29
	li	a4,0
	li	a5,0
	sw	a4,216(sp)
	sw	a5,220(sp)
	.loc 1 572 38
	li	a4,0
	li	a5,0
	sw	a4,208(sp)
	sw	a5,212(sp)
	.loc 1 573 11
	li	a4,0
	li	a5,0
	sw	a4,200(sp)
	sw	a5,204(sp)
	.loc 1 573 27
	li	a4,0
	li	a5,0
	sw	a4,192(sp)
	sw	a5,196(sp)
	.loc 1 574 11
	li	a4,0
	li	a5,0
	sw	a4,184(sp)
	sw	a5,188(sp)
	.loc 1 575 11
	li	a4,0
	li	a5,0
	sw	a4,176(sp)
	sw	a5,180(sp)
	.loc 1 575 24
	li	a4,0
	li	a5,0
	sw	a4,168(sp)
	sw	a5,172(sp)
	.loc 1 576 11
	li	a4,0
	li	a5,0
	sw	a4,160(sp)
	sw	a5,164(sp)
	.loc 1 576 24
	li	a4,0
	li	a5,0
	sw	a4,152(sp)
	sw	a5,156(sp)
	.loc 1 577 10
	sb	zero,285(sp)
	.loc 1 579 11
	li	a4,0
	li	a5,0
	sw	a4,272(sp)
	sw	a5,276(sp)
	.loc 1 580 11
	li	a4,0
	li	a5,0
	sw	a4,264(sp)
	sw	a5,268(sp)
	.loc 1 581 11
	li	a4,0
	li	a5,0
	sw	a4,256(sp)
	sw	a5,260(sp)
	.loc 1 583 11
	sw	zero,112(sp)
	sw	zero,116(sp)
	sw	zero,120(sp)
	sw	zero,124(sp)
	sw	zero,128(sp)
	sw	zero,132(sp)
	.loc 1 584 13
	sw	zero,80(sp)
	sw	zero,84(sp)
	sw	zero,88(sp)
	sw	zero,92(sp)
	sw	zero,96(sp)
	sw	zero,100(sp)
	sw	zero,104(sp)
	sw	zero,108(sp)
	.loc 1 586 11
	li	a4,0
	li	a5,0
	sw	a4,248(sp)
	sw	a5,252(sp)
	.loc 1 587 11
	li	a4,0
	li	a5,0
	sw	a4,144(sp)
	sw	a5,148(sp)
	.loc 1 589 11
	sw	zero,48(sp)
	sw	zero,52(sp)
	sw	zero,56(sp)
	sw	zero,60(sp)
	sw	zero,64(sp)
	sw	zero,68(sp)
	sw	zero,72(sp)
	sw	zero,76(sp)
	.loc 1 590 11
	sw	zero,16(sp)
	sw	zero,20(sp)
	sw	zero,24(sp)
	sw	zero,28(sp)
	sw	zero,32(sp)
	sw	zero,36(sp)
	sw	zero,40(sp)
	sw	zero,44(sp)
	.loc 1 592 11
	li	a4,0
	li	a5,0
	sw	a4,136(sp)
	sw	a5,140(sp)
	.loc 1 596 8
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	sw	a4,232(sp)
	sw	a5,236(sp)
	.loc 1 596 23
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	sw	a4,224(sp)
	sw	a5,228(sp)
	.loc 1 596 38
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	sw	a4,216(sp)
	sw	a5,220(sp)
	.loc 1 596 53
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	sw	a4,208(sp)
	sw	a5,212(sp)
	.loc 1 598 41
	lui	a5,%hi(yaw_unfusion_cnt)
	lw	a5,%lo(yaw_unfusion_cnt)(a5)
	addi	a4,a5,1
	.loc 1 598 22
	lui	a5,%hi(yaw_unfusion_cnt)
	sw	a4,%lo(yaw_unfusion_cnt)(a5)
	.loc 1 601 28
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s6,a4
	mv	s7,a5
	.loc 1 601 36
	lw	a2,216(sp)
	lw	a3,220(sp)
	lw	a0,232(sp)
	lw	a1,236(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 601 32
	mv	a2,a4
	mv	a3,a5
	mv	a0,s6
	mv	a1,s7
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 601 17
	lui	a5,%hi(.LC12)
	lw	a2,%lo(.LC12)(a5)
	lw	a3,%lo(.LC12+4)(a5)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	asin
	sw	a0,200(sp)
	sw	a1,204(sp)
	.loc 1 602 28
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s6,a4
	mv	s7,a5
	.loc 1 602 36
	lw	a2,224(sp)
	lw	a3,228(sp)
	lw	a0,232(sp)
	lw	a1,236(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 602 32
	mv	a2,a4
	mv	a3,a5
	mv	a0,s6
	mv	a1,s7
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 602 17
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s6,a4
	mv	s7,a5
	.loc 1 602 43
	lw	a2,232(sp)
	lw	a3,236(sp)
	lw	a0,232(sp)
	lw	a1,236(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s8,a4
	mv	s9,a5
	.loc 1 602 51
	lw	a2,224(sp)
	lw	a3,228(sp)
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 602 47
	mv	a2,a4
	mv	a3,a5
	mv	a0,s8
	mv	a1,s9
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s8,a4
	mv	s9,a5
	.loc 1 602 59
	lw	a2,216(sp)
	lw	a3,220(sp)
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 602 55
	mv	a2,a4
	mv	a3,a5
	mv	a0,s8
	mv	a1,s9
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s8,a4
	mv	s9,a5
	.loc 1 602 67
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,208(sp)
	lw	a1,212(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 602 17
	mv	a2,a4
	mv	a3,a5
	mv	a0,s8
	mv	a1,s9
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s6
	mv	a1,s7
	call	atan2
	sw	a0,192(sp)
	sw	a1,196(sp)
	.loc 1 605 37
	lw	a2,216(sp)
	lw	a3,220(sp)
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s6,a4
	mv	s7,a5
	.loc 1 605 45
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,232(sp)
	lw	a1,236(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 605 41
	mv	a2,a4
	mv	a3,a5
	mv	a0,s6
	mv	a1,s7
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 605 26
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s6,a4
	mv	s7,a5
	.loc 1 605 52
	lw	a2,232(sp)
	lw	a3,236(sp)
	lw	a0,232(sp)
	lw	a1,236(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s8,a4
	mv	s9,a5
	.loc 1 605 60
	lw	a2,224(sp)
	lw	a3,228(sp)
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 605 56
	mv	a2,a4
	mv	a3,a5
	mv	a0,s8
	mv	a1,s9
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s8,a4
	mv	s9,a5
	.loc 1 605 68
	lw	a2,216(sp)
	lw	a3,220(sp)
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 605 64
	mv	a2,a4
	mv	a3,a5
	mv	a0,s8
	mv	a1,s9
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s8,a4
	mv	s9,a5
	.loc 1 605 76
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,208(sp)
	lw	a1,212(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 605 26
	mv	a2,a4
	mv	a3,a5
	mv	a0,s8
	mv	a1,s9
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s6
	mv	a1,s7
	call	atan2
	sw	a0,184(sp)
	sw	a1,188(sp)
	.loc 1 607 18
	lw	a0,192(sp)
	lw	a1,196(sp)
	call	sin
	sw	a0,176(sp)
	sw	a1,180(sp)
	.loc 1 607 46 discriminator 1
	lw	a0,192(sp)
	lw	a1,196(sp)
	call	cos
	sw	a0,160(sp)
	sw	a1,164(sp)
	.loc 1 608 18
	lw	a0,200(sp)
	lw	a1,204(sp)
	call	sin
	sw	a0,168(sp)
	sw	a1,172(sp)
	.loc 1 608 46 discriminator 1
	lw	a0,200(sp)
	lw	a1,204(sp)
	call	cos
	sw	a0,152(sp)
	sw	a1,156(sp)
	.loc 1 611 17
	lw	a5,4(sp)
	lbu	a4,99(a5)
	.loc 1 611 7
	li	a5,4
	bne	a4,a5,.L74
	.loc 1 613 36
	lw	a5,4(sp)
	lw	a4,104(a5)
	lw	a5,108(a5)
	.loc 1 613 25
	lui	a3,%hi(.LC5)
	lw	a2,%lo(.LC5)(a3)
	lw	a3,%lo(.LC5+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,272(sp)
	sw	a5,276(sp)
	.loc 1 614 26
	li	a5,1
	sb	a5,285(sp)
	j	.L75
.L74:
	.loc 1 618 26
	sb	zero,285(sp)
.L75:
	.loc 1 621 17
	lw	a5,4(sp)
	lbu	a5,138(a5)
	.loc 1 621 36
	xori	a5,a5,1
	andi	a5,a5,0xff
	.loc 1 621 7
	beq	a5,zero,.L76
	.loc 1 623 26
	sb	zero,285(sp)
.L76:
	.loc 1 626 22
	lw	a5,4(sp)
	lw	a4,120(a5)
	lw	a5,124(a5)
	.loc 1 626 8
	lui	a3,%hi(TWOANTBL)
	lw	a2,%lo(TWOANTBL)(a3)
	lw	a3,%lo(TWOANTBL+4)(a3)
	.loc 1 626 27
	mv	a0,a4
	mv	a1,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 626 8
	mv	s4,a4
	li	a3,-2147483648
	addi	a3,a3,-1
	and	s5,a5,a3
	.loc 1 626 7
	lui	a5,%hi(.LC14)
	lw	a2,%lo(.LC14)(a5)
	lw	a3,%lo(.LC14+4)(a5)
	mv	a0,s4
	mv	a1,s5
	call	__ledf2
	mv	a5,a0
	bgt	a5,zero,.L77
	.loc 1 626 58 discriminator 1
	lw	a5,4(sp)
	lbu	a4,99(a5)
	.loc 1 626 46 discriminator 1
	li	a5,4
	bne	a4,a5,.L77
	.loc 1 628 41
	lw	a5,4(sp)
	lw	a4,104(a5)
	lw	a5,108(a5)
	.loc 1 628 30
	lui	a3,%hi(.LC5)
	lw	a2,%lo(.LC5)(a3)
	lw	a3,%lo(.LC5+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,272(sp)
	sw	a5,276(sp)
	.loc 1 629 30
	li	a5,1
	sb	a5,285(sp)
	j	.L79
.L77:
	.loc 1 633 30
	sb	zero,285(sp)
.L79:
	.loc 1 636 17
	lw	a5,4(sp)
	lbu	a5,138(a5)
	.loc 1 636 36
	xori	a5,a5,1
	andi	a5,a5,0xff
	.loc 1 636 7
	beq	a5,zero,.L80
	.loc 1 638 26
	sb	zero,285(sp)
.L80:
	.loc 1 642 25
	lui	a5,%hi(ins_state)
	addi	a4,a5,%lo(ins_state)
	li	a5,4096
	add	a5,a4,a5
	lbu	a5,-1872(a5)
	.loc 1 642 7
	beq	a5,zero,.L81
	.loc 1 642 63 discriminator 1
	lui	a5,%hi(ins_state)
	addi	a4,a5,%lo(ins_state)
	li	a5,4096
	add	a5,a4,a5
	lbu	a5,-1868(a5)
	.loc 1 642 52 discriminator 1
	xori	a5,a5,1
	andi	a5,a5,0xff
	.loc 1 642 44 discriminator 1
	beq	a5,zero,.L81
	.loc 1 644 12
	lui	a5,%hi(ins_state)
	addi	a5,a5,%lo(ins_state)
	lw	a4,152(a5)
	lw	a5,156(a5)
	sw	a4,232(sp)
	sw	a5,236(sp)
	.loc 1 644 30
	lui	a5,%hi(ins_state)
	addi	a5,a5,%lo(ins_state)
	lw	a4,160(a5)
	lw	a5,164(a5)
	sw	a4,224(sp)
	sw	a5,228(sp)
	.loc 1 644 48
	lui	a5,%hi(ins_state)
	addi	a5,a5,%lo(ins_state)
	lw	a4,168(a5)
	lw	a5,172(a5)
	sw	a4,216(sp)
	sw	a5,220(sp)
	.loc 1 644 66
	lui	a5,%hi(ins_state)
	addi	a5,a5,%lo(ins_state)
	lw	a4,176(a5)
	lw	a5,180(a5)
	sw	a4,208(sp)
	sw	a5,212(sp)
	.loc 1 645 33
	lw	a4,224(sp)
	lw	a5,228(sp)
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 645 36
	lw	a2,216(sp)
	lw	a3,220(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 645 43
	lw	a4,232(sp)
	lw	a5,236(sp)
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 645 46
	lw	a2,208(sp)
	lw	a3,212(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 645 26
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 645 52
	lui	a5,%hi(.LC12)
	lw	a2,%lo(.LC12)(a5)
	lw	a3,%lo(.LC12+4)(a5)
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 645 55
	lw	a2,216(sp)
	lw	a3,220(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 645 60
	lw	a4,208(sp)
	lw	a5,212(sp)
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 645 63
	lw	a2,208(sp)
	lw	a3,212(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 645 58
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 645 26
	lui	a5,%hi(.LC18)
	lw	a2,%lo(.LC18)(a5)
	lw	a3,%lo(.LC18+4)(a5)
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	atan2
	sw	a0,256(sp)
	sw	a1,260(sp)
	.loc 1 646 26
	lui	a5,%hi(yaw_unfusion_cnt)
	sw	zero,%lo(yaw_unfusion_cnt)(a5)
	j	.L82
.L81:
	.loc 1 649 12
	lbu	a5,285(sp)
	beq	a5,zero,.L83
	.loc 1 652 28
	li	a4,0
	li	a5,0
	sw	a4,264(sp)
	sw	a5,268(sp)
	.loc 1 653 14
	sh	zero,286(sp)
	.loc 1 653 9
	j	.L84
.L88:
	.loc 1 655 35
	lhu	a4,286(sp)
	lui	a5,%hi(gnss_ang_delayed)
	addi	a3,a5,%lo(gnss_ang_delayed)
	mv	a5,a4
	slli	a5,a5,2
	add	a5,a5,a4
	slli	a5,a5,3
	add	a5,a3,a5
	lw	s2,32(a5)
	lw	s3,36(a5)
	.loc 1 655 57
	lw	a5,12(sp)
	lw	a4,184(a5)
	lw	a5,188(a5)
	.loc 1 655 77
	lw	a3,4(sp)
	lw	a2,24(a3)
	lw	a3,28(a3)
	.loc 1 655 67
	mv	a0,a4
	mv	a1,a5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 655 15
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__gedf2
	mv	a5,a0
	blt	a5,zero,.L85
	.loc 1 656 36
	lhu	a4,286(sp)
	lui	a5,%hi(gnss_ang_delayed)
	addi	a3,a5,%lo(gnss_ang_delayed)
	mv	a5,a4
	slli	a5,a5,2
	add	a5,a5,a4
	slli	a5,a5,3
	add	a5,a3,a5
	lw	a4,32(a5)
	lw	a5,36(a5)
	.loc 1 656 58
	lw	a3,12(sp)
	lw	a2,184(a3)
	lw	a3,188(a3)
	.loc 1 655 89 discriminator 1
	mv	a0,a4
	mv	a1,a5
	call	__ledf2
	mv	a5,a0
	bgt	a5,zero,.L85
	.loc 1 658 78
	lhu	a4,286(sp)
	lui	a5,%hi(gnss_ang_delayed)
	addi	a3,a5,%lo(gnss_ang_delayed)
	mv	a5,a4
	slli	a5,a5,2
	add	a5,a5,a4
	slli	a5,a5,3
	add	a5,a3,a5
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 658 36
	mv	a2,a4
	mv	a3,a5
	lw	a0,264(sp)
	lw	a1,268(sp)
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,264(sp)
	sw	a5,268(sp)
.L85:
	.loc 1 653 36 discriminator 2
	lhu	a5,286(sp)
	addi	a5,a5,1
	sh	a5,286(sp)
.L84:
	.loc 1 653 18 discriminator 1
	lhu	a4,286(sp)
	li	a5,29
	bleu	a4,a5,.L88
	.loc 1 662 24
	lw	a2,264(sp)
	lw	a3,268(sp)
	lw	a0,272(sp)
	lw	a1,276(sp)
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,256(sp)
	sw	a5,260(sp)
	.loc 1 663 26
	lui	a5,%hi(yaw_unfusion_cnt)
	sw	zero,%lo(yaw_unfusion_cnt)(a5)
	j	.L82
.L83:
	.loc 1 666 29
	lui	a5,%hi(yaw_unfusion_cnt)
	lw	a4,%lo(yaw_unfusion_cnt)(a5)
	.loc 1 666 12
	li	a5,30
	bleu	a4,a5,.L82
	.loc 1 670 48
	lw	a5,8(sp)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 670 34
	lw	a2,152(sp)
	lw	a3,156(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 670 62
	lw	a2,176(sp)
	lw	a3,180(sp)
	lw	a0,168(sp)
	lw	a1,172(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 670 85
	lw	a5,8(sp)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 670 71
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 670 51
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 670 98
	lw	a2,168(sp)
	lw	a3,172(sp)
	lw	a0,160(sp)
	lw	a1,164(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 670 122
	lw	a5,8(sp)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 670 108
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 670 88
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 670 23
	sw	a4,112(sp)
	sw	a5,116(sp)
	.loc 1 671 47
	lw	a5,8(sp)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 671 33
	lw	a2,160(sp)
	lw	a3,164(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s4,a4
	mv	s5,a5
	.loc 1 671 75
	lw	a5,8(sp)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 671 61
	lw	a2,176(sp)
	lw	a3,180(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 671 51
	mv	a2,a4
	mv	a3,a5
	mv	a0,s4
	mv	a1,s5
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 671 23
	sw	a4,120(sp)
	sw	a5,124(sp)
	.loc 1 674 44
	lw	a4,120(sp)
	lw	a5,124(sp)
	.loc 1 674 30
	mv	s2,a4
	li	a3,-2147483648
	xor	s3,a5,a3
	lw	a4,112(sp)
	lw	a5,116(sp)
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	atan2
	sw	a0,256(sp)
	sw	a1,260(sp)
	.loc 1 676 30
	lui	a5,%hi(yaw_unfusion_cnt)
	sw	zero,%lo(yaw_unfusion_cnt)(a5)
.L82:
	.loc 1 681 7
	li	a2,0
	li	a3,0
	lw	a0,256(sp)
	lw	a1,260(sp)
	call	__nedf2
	mv	a5,a0
	bne	a5,zero,.L109
	.loc 1 769 1
	j	.L120
.L109:
	.loc 1 683 11
	lui	a5,%hi(.LC8)
	lw	a2,%lo(.LC8)(a5)
	lw	a3,%lo(.LC8+4)(a5)
	lw	a0,256(sp)
	lw	a1,260(sp)
	call	__gtdf2
	mv	a5,a0
	ble	a5,zero,.L116
	.loc 1 685 28
	lui	a5,%hi(.LC9)
	lw	a2,%lo(.LC9)(a5)
	lw	a3,%lo(.LC9+4)(a5)
	lw	a0,256(sp)
	lw	a1,260(sp)
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,256(sp)
	sw	a5,260(sp)
	j	.L93
.L116:
	.loc 1 687 16
	lui	a5,%hi(.LC10)
	lw	a2,%lo(.LC10)(a5)
	lw	a3,%lo(.LC10+4)(a5)
	lw	a0,256(sp)
	lw	a1,260(sp)
	call	__ltdf2
	mv	a5,a0
	bge	a5,zero,.L93
	.loc 1 689 28
	lui	a5,%hi(.LC9)
	lw	a2,%lo(.LC9)(a5)
	lw	a3,%lo(.LC9+4)(a5)
	lw	a0,256(sp)
	lw	a1,260(sp)
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,256(sp)
	sw	a5,260(sp)
.L93:
	.loc 1 692 18
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lbu	a5,85(a5)
	.loc 1 692 33
	xori	a5,a5,1
	andi	a5,a5,0xff
	.loc 1 692 11
	beq	a5,zero,.L95
	.loc 1 694 34
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	li	a4,1
	sb	a4,85(a5)
.L95:
	.loc 1 699 19
	lw	a2,184(sp)
	lw	a3,188(sp)
	lw	a0,256(sp)
	lw	a1,260(sp)
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,248(sp)
	sw	a5,252(sp)
	.loc 1 704 11
	lui	a5,%hi(.LC8)
	lw	a2,%lo(.LC8)(a5)
	lw	a3,%lo(.LC8+4)(a5)
	lw	a0,248(sp)
	lw	a1,252(sp)
	call	__gtdf2
	mv	a5,a0
	ble	a5,zero,.L117
	.loc 1 706 23
	lui	a5,%hi(.LC9)
	lw	a2,%lo(.LC9)(a5)
	lw	a3,%lo(.LC9+4)(a5)
	lw	a0,248(sp)
	lw	a1,252(sp)
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,248(sp)
	sw	a5,252(sp)
	j	.L98
.L117:
	.loc 1 708 16
	lui	a5,%hi(.LC10)
	lw	a2,%lo(.LC10)(a5)
	lw	a3,%lo(.LC10+4)(a5)
	lw	a0,248(sp)
	lw	a1,252(sp)
	call	__ltdf2
	mv	a5,a0
	bge	a5,zero,.L98
	.loc 1 710 23
	lui	a5,%hi(.LC9)
	lw	a2,%lo(.LC9)(a5)
	lw	a3,%lo(.LC9+4)(a5)
	lw	a0,248(sp)
	lw	a1,252(sp)
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,248(sp)
	sw	a5,252(sp)
.L98:
	.loc 1 715 23
	li	a4,0
	li	a5,0
	sw	a4,80(sp)
	sw	a5,84(sp)
	.loc 1 716 23
	li	a4,0
	li	a5,0
	sw	a4,88(sp)
	sw	a5,92(sp)
	.loc 1 718 12
	lw	s0,248(sp)
	lw	a4,252(sp)
	li	a5,-2147483648
	addi	a5,a5,-1
	and	s1,a4,a5
	.loc 1 718 11
	lui	a5,%hi(.LC24)
	lw	a2,%lo(.LC24)(a5)
	lw	a3,%lo(.LC24+4)(a5)
	mv	a0,s0
	mv	a1,s1
	call	__gtdf2
	mv	a5,a0
	ble	a5,zero,.L118
	.loc 1 720 27
	lw	a4,248(sp)
	lw	a5,252(sp)
	sw	a4,96(sp)
	sw	a5,100(sp)
	j	.L102
.L118:
	.loc 1 724 34
	lw	a2,248(sp)
	lw	a3,252(sp)
	lw	a0,240(sp)
	lw	a1,244(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 724 27
	sw	a4,96(sp)
	sw	a5,100(sp)
.L102:
	.loc 1 728 41
	lw	a4,96(sp)
	lw	a5,100(sp)
	.loc 1 728 23
	sw	a4,144(sp)
	li	a3,-2147483648
	addi	a3,a3,-1
	and	a5,a5,a3
	sw	a5,148(sp)
	.loc 1 729 11
	lui	a5,%hi(.LC25)
	lw	a2,%lo(.LC25)(a5)
	lw	a3,%lo(.LC25+4)(a5)
	lw	a0,144(sp)
	lw	a1,148(sp)
	call	__ltdf2
	mv	a5,a0
	bge	a5,zero,.L119
	.loc 1 731 19
	lui	a5,%hi(.LC18)
	lw	a4,%lo(.LC18)(a5)
	lw	a5,%lo(.LC18+4)(a5)
	sw	a4,48(sp)
	sw	a5,52(sp)
	.loc 1 732 19
	li	a4,0
	li	a5,0
	sw	a4,56(sp)
	sw	a5,60(sp)
	.loc 1 733 19
	li	a4,0
	li	a5,0
	sw	a4,64(sp)
	sw	a5,68(sp)
	.loc 1 734 19
	li	a4,0
	li	a5,0
	sw	a4,72(sp)
	sw	a5,76(sp)
	j	.L105
.L119:
	.loc 1 740 21
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,144(sp)
	lw	a1,148(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	cos
	mv	a4,a0
	mv	a5,a1
	.loc 1 740 19 discriminator 1
	sw	a4,48(sp)
	sw	a5,52(sp)
	.loc 1 741 21
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,144(sp)
	lw	a1,148(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sin
	.loc 1 741 54 discriminator 1
	lw	a4,80(sp)
	lw	a5,84(sp)
	.loc 1 741 42 discriminator 1
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 741 56 discriminator 1
	lw	a2,144(sp)
	lw	a3,148(sp)
	mv	a0,a4
	mv	a1,a5
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 741 19 discriminator 1
	sw	a4,56(sp)
	sw	a5,60(sp)
	.loc 1 742 21
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,144(sp)
	lw	a1,148(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sin
	.loc 1 742 54 discriminator 1
	lw	a4,88(sp)
	lw	a5,92(sp)
	.loc 1 742 42 discriminator 1
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 742 56 discriminator 1
	lw	a2,144(sp)
	lw	a3,148(sp)
	mv	a0,a4
	mv	a1,a5
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 742 19 discriminator 1
	sw	a4,64(sp)
	sw	a5,68(sp)
	.loc 1 743 21
	lui	a5,%hi(.LC11)
	lw	a2,%lo(.LC11)(a5)
	lw	a3,%lo(.LC11+4)(a5)
	lw	a0,144(sp)
	lw	a1,148(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sin
	.loc 1 743 54 discriminator 1
	lw	a4,96(sp)
	lw	a5,100(sp)
	.loc 1 743 42 discriminator 1
	mv	a2,a4
	mv	a3,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 743 56 discriminator 1
	lw	a2,144(sp)
	lw	a3,148(sp)
	mv	a0,a4
	mv	a1,a5
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 743 19 discriminator 1
	sw	a4,72(sp)
	sw	a5,76(sp)
.L105:
	.loc 1 748 29
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 748 35
	lw	a2,48(sp)
	lw	a3,52(sp)
	.loc 1 748 32
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 748 47
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 748 53
	lw	a2,56(sp)
	lw	a3,60(sp)
	.loc 1 748 50
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 748 39
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 748 65
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 748 71
	lw	a2,64(sp)
	lw	a3,68(sp)
	.loc 1 748 68
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 748 57
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 748 83
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 748 89
	lw	a2,72(sp)
	lw	a3,76(sp)
	.loc 1 748 86
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 748 75
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 748 21
	sw	a4,16(sp)
	sw	a5,20(sp)
	.loc 1 749 29
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 749 35
	lw	a2,48(sp)
	lw	a3,52(sp)
	.loc 1 749 32
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 749 47
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 749 53
	lw	a2,56(sp)
	lw	a3,60(sp)
	.loc 1 749 50
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 749 39
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 749 65
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 749 71
	lw	a2,64(sp)
	lw	a3,68(sp)
	.loc 1 749 68
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 749 57
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 749 83
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 749 89
	lw	a2,72(sp)
	lw	a3,76(sp)
	.loc 1 749 86
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 749 75
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 749 21
	sw	a4,24(sp)
	sw	a5,28(sp)
	.loc 1 750 29
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 750 35
	lw	a2,48(sp)
	lw	a3,52(sp)
	.loc 1 750 32
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 750 47
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 750 53
	lw	a2,56(sp)
	lw	a3,60(sp)
	.loc 1 750 50
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 750 39
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 750 65
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 750 71
	lw	a2,64(sp)
	lw	a3,68(sp)
	.loc 1 750 68
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 750 57
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 750 83
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 750 89
	lw	a2,72(sp)
	lw	a3,76(sp)
	.loc 1 750 86
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 750 75
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 750 21
	sw	a4,32(sp)
	sw	a5,36(sp)
	.loc 1 751 29
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	.loc 1 751 35
	lw	a2,48(sp)
	lw	a3,52(sp)
	.loc 1 751 32
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 751 47
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	.loc 1 751 53
	lw	a2,56(sp)
	lw	a3,60(sp)
	.loc 1 751 50
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 751 39
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 751 65
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	.loc 1 751 71
	lw	a2,64(sp)
	lw	a3,68(sp)
	.loc 1 751 68
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 751 57
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 751 83
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	.loc 1 751 89
	lw	a2,72(sp)
	lw	a3,76(sp)
	.loc 1 751 86
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 751 75
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 751 21
	sw	a4,40(sp)
	sw	a5,44(sp)
	.loc 1 754 30
	lw	a4,16(sp)
	lw	a5,20(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	s0,a0
	mv	s1,a1
	.loc 1 754 49 discriminator 1
	lw	a4,24(sp)
	lw	a5,28(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 754 47 discriminator 2
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 754 68 discriminator 2
	lw	a4,32(sp)
	lw	a5,36(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 754 66 discriminator 3
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 754 87 discriminator 3
	lw	a4,40(sp)
	lw	a5,44(sp)
	mv	a0,a4
	mv	a1,a5
	call	SQR
	mv	a4,a0
	mv	a5,a1
	.loc 1 754 25 discriminator 4
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	sqrt
	mv	a4,a0
	mv	a5,a1
	.loc 1 754 19 discriminator 5
	lui	a1,%hi(.LC18)
	mv	a2,a4
	mv	a3,a5
	lw	a0,%lo(.LC18)(a1)
	lw	a1,%lo(.LC18+4)(a1)
	call	__divdf3
	mv	a4,a0
	mv	a5,a1
	sw	a4,136(sp)
	sw	a5,140(sp)
	.loc 1 756 29
	lw	a4,16(sp)
	lw	a5,20(sp)
	.loc 1 756 32
	lw	a2,136(sp)
	lw	a3,140(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 756 19
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,0(a3)
	sw	a5,4(a3)
	.loc 1 757 29
	lw	a4,24(sp)
	lw	a5,28(sp)
	.loc 1 757 32
	lw	a2,136(sp)
	lw	a3,140(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 757 19
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,8(a3)
	sw	a5,12(a3)
	.loc 1 758 29
	lw	a4,32(sp)
	lw	a5,36(sp)
	.loc 1 758 32
	lw	a2,136(sp)
	lw	a3,140(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 758 19
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,16(a3)
	sw	a5,20(a3)
	.loc 1 759 29
	lw	a4,40(sp)
	lw	a5,44(sp)
	.loc 1 759 32
	lw	a2,136(sp)
	lw	a3,140(sp)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 759 19
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,24(a3)
	sw	a5,28(a3)
	.loc 1 761 12
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,0(a5)
	lw	a5,4(a5)
	sw	a4,232(sp)
	sw	a5,236(sp)
	.loc 1 761 27
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,8(a5)
	lw	a5,12(a5)
	sw	a4,224(sp)
	sw	a5,228(sp)
	.loc 1 761 42
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,16(a5)
	lw	a5,20(a5)
	sw	a4,216(sp)
	sw	a5,220(sp)
	.loc 1 761 57
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a4,24(a5)
	lw	a5,28(a5)
	sw	a4,208(sp)
	sw	a5,212(sp)
	.loc 1 762 43
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 762 51
	lw	a2,216(sp)
	lw	a3,220(sp)
	lw	a0,232(sp)
	lw	a1,236(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 762 47
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	.loc 1 762 32
	lui	a5,%hi(.LC12)
	lw	a2,%lo(.LC12)(a5)
	lw	a3,%lo(.LC12+4)(a5)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	a0,a4
	mv	a1,a5
	call	asin
	mv	a4,a0
	mv	a5,a1
	.loc 1 762 31 discriminator 1
	lui	a3,%hi(.LC13)
	lw	a2,%lo(.LC13)(a3)
	lw	a3,%lo(.LC13+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 762 22 discriminator 1
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,40(a3)
	sw	a5,44(a3)
	.loc 1 763 43
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 763 51
	lw	a2,224(sp)
	lw	a3,228(sp)
	lw	a0,232(sp)
	lw	a1,236(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 763 47
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 763 32
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 763 59
	lw	a2,232(sp)
	lw	a3,236(sp)
	lw	a0,232(sp)
	lw	a1,236(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 763 67
	lw	a2,224(sp)
	lw	a3,228(sp)
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 763 63
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 763 75
	lw	a2,216(sp)
	lw	a3,220(sp)
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 763 71
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 763 83
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,208(sp)
	lw	a1,212(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 763 32
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	atan2
	mv	a4,a0
	mv	a5,a1
	.loc 1 763 31 discriminator 1
	lui	a3,%hi(.LC13)
	lw	a2,%lo(.LC13)(a3)
	lw	a3,%lo(.LC13+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 763 22 discriminator 1
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,32(a3)
	sw	a5,36(a3)
	.loc 1 764 43
	lw	a2,216(sp)
	lw	a3,220(sp)
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 764 51
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,232(sp)
	lw	a1,236(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 764 47
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 764 32
	mv	a2,a4
	mv	a3,a5
	mv	a0,a4
	mv	a1,a5
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s0,a4
	mv	s1,a5
	.loc 1 764 58
	lw	a2,232(sp)
	lw	a3,236(sp)
	lw	a0,232(sp)
	lw	a1,236(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 764 66
	lw	a2,224(sp)
	lw	a3,228(sp)
	lw	a0,224(sp)
	lw	a1,228(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 764 62
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__adddf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 764 74
	lw	a2,216(sp)
	lw	a3,220(sp)
	lw	a0,216(sp)
	lw	a1,220(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 764 70
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	s2,a4
	mv	s3,a5
	.loc 1 764 82
	lw	a2,208(sp)
	lw	a3,212(sp)
	lw	a0,208(sp)
	lw	a1,212(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 764 32
	mv	a2,a4
	mv	a3,a5
	mv	a0,s2
	mv	a1,s3
	call	__subdf3
	mv	a4,a0
	mv	a5,a1
	mv	a2,a4
	mv	a3,a5
	mv	a0,s0
	mv	a1,s1
	call	atan2
	mv	a4,a0
	mv	a5,a1
	.loc 1 764 31 discriminator 1
	lui	a3,%hi(.LC13)
	lw	a2,%lo(.LC13)(a3)
	lw	a3,%lo(.LC13+4)(a3)
	mv	a0,a4
	mv	a1,a5
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 764 22 discriminator 1
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,48(a3)
	sw	a5,52(a3)
	.loc 1 766 33
	lui	a5,%hi(.LC13)
	lw	a2,%lo(.LC13)(a5)
	lw	a3,%lo(.LC13+4)(a5)
	lw	a0,256(sp)
	lw	a1,260(sp)
	call	__muldf3
	mv	a4,a0
	mv	a5,a1
	.loc 1 766 24
	lui	a3,%hi(ahrs_k)
	addi	a3,a3,%lo(ahrs_k)
	sw	a4,72(a3)
	sw	a5,76(a3)
.L120:
	.loc 1 769 1
	nop
	lw	ra,332(sp)
	.cfi_restore 1
	lw	s0,328(sp)
	.cfi_restore 8
	lw	s1,324(sp)
	.cfi_restore 9
	lw	s2,320(sp)
	.cfi_restore 18
	lw	s3,316(sp)
	.cfi_restore 19
	lw	s4,312(sp)
	.cfi_restore 20
	lw	s5,308(sp)
	.cfi_restore 21
	lw	s6,304(sp)
	.cfi_restore 22
	lw	s7,300(sp)
	.cfi_restore 23
	lw	s8,296(sp)
	.cfi_restore 24
	lw	s9,292(sp)
	.cfi_restore 25
	addi	sp,sp,336
	.cfi_def_cfa_offset 0
	jr	ra
	.cfi_endproc
.LFE2:
	.size	ahrs_mag_update, .-ahrs_mag_update
	.section	.text.ahrs_update,"ax",@progbits
	.align	1
	.globl	ahrs_update
	.type	ahrs_update, @function
ahrs_update:
.LFB3:
	.loc 1 772 1
	.cfi_startproc
	addi	sp,sp,-32
	.cfi_def_cfa_offset 32
	sw	ra,28(sp)
	.cfi_offset 1, -4
	sw	a0,12(sp)
	sw	a1,8(sp)
	sw	a2,4(sp)
	.loc 1 773 5
	lw	a1,4(sp)
	lw	a0,12(sp)
	call	ahrs_imu_update
	.loc 1 776 24
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	lw	a5,80(a5)
	.loc 1 776 28
	addi	a4,a5,1
	.loc 1 776 16
	lui	a5,%hi(ahrs_k)
	addi	a5,a5,%lo(ahrs_k)
	sw	a4,80(a5)
	.loc 1 777 1
	nop
	lw	ra,28(sp)
	.cfi_restore 1
	addi	sp,sp,32
	.cfi_def_cfa_offset 0
	jr	ra
	.cfi_endproc
.LFE3:
	.size	ahrs_update, .-ahrs_update
	.section	.rodata
	.align	3
.LC0:
	.word	0
	.word	1081507840
	.align	3
.LC1:
	.word	-1717986918
	.word	1069128089
	.align	3
.LC2:
	.word	-858993459
	.word	1072483532
	.align	3
.LC3:
	.word	-1717986918
	.word	1072798105
	.align	3
.LC4:
	.word	-842880625
	.word	1062724881
	.align	3
.LC5:
	.word	-1571644103
	.word	1066524486
	.align	3
.LC6:
	.word	-2127697391
	.word	1030854553
	.align	3
.LC7:
	.word	1413754136
	.word	1073291771
	.align	3
.LC8:
	.word	1413754136
	.word	1074340347
	.align	3
.LC9:
	.word	1413754136
	.word	1075388923
	.align	3
.LC10:
	.word	1413754136
	.word	-1073143301
	.align	3
.LC11:
	.word	0
	.word	1071644672
	.align	3
.LC12:
	.word	0
	.word	-1073741824
	.align	3
.LC13:
	.word	442745336
	.word	1078765020
	.align	3
.LC14:
	.word	1202590843
	.word	1066695393
	.align	3
.LC15:
	.word	-674304500
	.word	1058217383
	.align	3
.LC16:
	.word	-674304500
	.word	-1089266265
	.align	3
.LC17:
	.word	1431655765
	.word	1068848469
	.align	3
.LC18:
	.word	0
	.word	1072693248
	.align	3
.LC19:
	.word	-171798692
	.word	1072651304
	.align	3
.LC20:
	.word	-2061584302
	.word	1072714219
	.align	3
.LC21:
	.word	0
	.word	1074790400
	.align	3
.LC22:
	.word	0
	.word	1075838976
	.align	3
.LC23:
	.word	-1717986918
	.word	1070176665
	.align	3
.LC24:
	.word	1256670343
	.word	1069963032
	.align	3
.LC25:
	.word	-500134854
	.word	1044740494
	.text
.Letext0:
	.file 2 "D:/Program Files/SEGGER/SEGGER Embedded Studio 8.12a/include/stdint.h"
	.file 3 "E:\\2014902\\HPM6750\\HPM6750_INVProject\\my_project\\app\\gpio\\src\\INAV\\ins.h"
	.file 4 "D:/Program Files/SEGGER/SEGGER Embedded Studio 8.12a/include/math.h"
	.section	.debug_info,"",@progbits
.Ldebug_info0:
	.4byte	0x117b
	.2byte	0x4
	.4byte	.Ldebug_abbrev0
	.byte	0x4
	.uleb128 0x1
	.4byte	.LASF1397
	.byte	0xc
	.4byte	.LASF1398
	.4byte	.LASF1399
	.4byte	.Ldebug_ranges0+0
	.4byte	0
	.4byte	.Ldebug_line0
	.4byte	.Ldebug_macro0
	.uleb128 0x2
	.byte	0x1
	.byte	0x8
	.4byte	.LASF1252
	.uleb128 0x2
	.byte	0x4
	.byte	0x7
	.4byte	.LASF1253
	.uleb128 0x2
	.byte	0x4
	.byte	0x5
	.4byte	.LASF1254
	.uleb128 0x2
	.byte	0x1
	.byte	0x6
	.4byte	.LASF1255
	.uleb128 0x3
	.4byte	.LASF1258
	.byte	0x2
	.2byte	0x111
	.byte	0x29
	.4byte	0x52
	.uleb128 0x2
	.byte	0x1
	.byte	0x8
	.4byte	.LASF1256
	.uleb128 0x2
	.byte	0x2
	.byte	0x5
	.4byte	.LASF1257
	.uleb128 0x3
	.4byte	.LASF1259
	.byte	0x2
	.2byte	0x113
	.byte	0x29
	.4byte	0x6d
	.uleb128 0x2
	.byte	0x2
	.byte	0x7
	.4byte	.LASF1260
	.uleb128 0x3
	.4byte	.LASF1261
	.byte	0x2
	.2byte	0x115
	.byte	0x29
	.4byte	0x81
	.uleb128 0x2
	.byte	0x4
	.byte	0x7
	.4byte	.LASF1262
	.uleb128 0x2
	.byte	0x8
	.byte	0x5
	.4byte	.LASF1263
	.uleb128 0x3
	.4byte	.LASF1264
	.byte	0x2
	.2byte	0x117
	.byte	0x29
	.4byte	0x9c
	.uleb128 0x2
	.byte	0x8
	.byte	0x7
	.4byte	.LASF1265
	.uleb128 0x4
	.byte	0x4
	.byte	0x5
	.string	"int"
	.uleb128 0x2
	.byte	0x4
	.byte	0x4
	.4byte	.LASF1266
	.uleb128 0x2
	.byte	0x8
	.byte	0x3
	.4byte	.LASF1267
	.uleb128 0x2
	.byte	0x8
	.byte	0x4
	.4byte	.LASF1268
	.uleb128 0x2
	.byte	0x10
	.byte	0x3
	.4byte	.LASF1269
	.uleb128 0x2
	.byte	0x10
	.byte	0x4
	.4byte	.LASF1270
	.uleb128 0x2
	.byte	0x20
	.byte	0x3
	.4byte	.LASF1271
	.uleb128 0x5
	.4byte	.LASF1272
	.byte	0x3
	.byte	0x1a
	.byte	0x14
	.4byte	0xb8
	.uleb128 0x6
	.4byte	0xd4
	.uleb128 0x7
	.4byte	.LASF1299
	.byte	0x3
	.byte	0x6a
	.byte	0xe
	.4byte	0xd4
	.uleb128 0x8
	.byte	0x18
	.byte	0x3
	.2byte	0x14f
	.byte	0x9
	.4byte	0x120
	.uleb128 0x9
	.string	"x"
	.byte	0x3
	.2byte	0x151
	.byte	0xb
	.4byte	0xd4
	.byte	0
	.uleb128 0x9
	.string	"y"
	.byte	0x3
	.2byte	0x152
	.byte	0xb
	.4byte	0xd4
	.byte	0x8
	.uleb128 0x9
	.string	"z"
	.byte	0x3
	.2byte	0x153
	.byte	0xb
	.4byte	0xd4
	.byte	0x10
	.byte	0
	.uleb128 0x3
	.4byte	.LASF1273
	.byte	0x3
	.2byte	0x155
	.byte	0x2
	.4byte	0xf1
	.uleb128 0x8
	.byte	0x20
	.byte	0x3
	.2byte	0x160
	.byte	0x9
	.4byte	0x16a
	.uleb128 0x9
	.string	"n"
	.byte	0x3
	.2byte	0x162
	.byte	0x8
	.4byte	0xd4
	.byte	0
	.uleb128 0x9
	.string	"e"
	.byte	0x3
	.2byte	0x163
	.byte	0x8
	.4byte	0xd4
	.byte	0x8
	.uleb128 0x9
	.string	"d"
	.byte	0x3
	.2byte	0x164
	.byte	0x8
	.4byte	0xd4
	.byte	0x10
	.uleb128 0xa
	.4byte	.LASF1274
	.byte	0x3
	.2byte	0x166
	.byte	0xc
	.4byte	0xb8
	.byte	0x18
	.byte	0
	.uleb128 0x3
	.4byte	.LASF1275
	.byte	0x3
	.2byte	0x168
	.byte	0x2
	.4byte	0x12d
	.uleb128 0x8
	.byte	0x28
	.byte	0x3
	.2byte	0x190
	.byte	0x9
	.4byte	0x1c8
	.uleb128 0xa
	.4byte	.LASF1276
	.byte	0x3
	.2byte	0x192
	.byte	0x8
	.4byte	0xd4
	.byte	0
	.uleb128 0xa
	.4byte	.LASF1277
	.byte	0x3
	.2byte	0x193
	.byte	0x8
	.4byte	0xd4
	.byte	0x8
	.uleb128 0x9
	.string	"yaw"
	.byte	0x3
	.2byte	0x194
	.byte	0x8
	.4byte	0xd4
	.byte	0x10
	.uleb128 0xa
	.4byte	.LASF1278
	.byte	0x3
	.2byte	0x196
	.byte	0x8
	.4byte	0xd4
	.byte	0x18
	.uleb128 0xa
	.4byte	.LASF1274
	.byte	0x3
	.2byte	0x197
	.byte	0x9
	.4byte	0xb8
	.byte	0x20
	.byte	0
	.uleb128 0x3
	.4byte	.LASF1279
	.byte	0x3
	.2byte	0x199
	.byte	0x2
	.4byte	0x177
	.uleb128 0x8
	.byte	0x20
	.byte	0x3
	.2byte	0x19b
	.byte	0x9
	.4byte	0x214
	.uleb128 0x9
	.string	"q0"
	.byte	0x3
	.2byte	0x19d
	.byte	0x8
	.4byte	0xd4
	.byte	0
	.uleb128 0x9
	.string	"q1"
	.byte	0x3
	.2byte	0x19e
	.byte	0x8
	.4byte	0xd4
	.byte	0x8
	.uleb128 0x9
	.string	"q2"
	.byte	0x3
	.2byte	0x19f
	.byte	0x8
	.4byte	0xd4
	.byte	0x10
	.uleb128 0x9
	.string	"q3"
	.byte	0x3
	.2byte	0x1a0
	.byte	0x8
	.4byte	0xd4
	.byte	0x18
	.byte	0
	.uleb128 0x3
	.4byte	.LASF1280
	.byte	0x3
	.2byte	0x1a1
	.byte	0x2
	.4byte	0x1d5
	.uleb128 0x8
	.byte	0xc8
	.byte	0x3
	.2byte	0x1de
	.byte	0x9
	.4byte	0x2e1
	.uleb128 0xa
	.4byte	.LASF1281
	.byte	0x3
	.2byte	0x1e0
	.byte	0x8
	.4byte	0x120
	.byte	0
	.uleb128 0xa
	.4byte	.LASF1282
	.byte	0x3
	.2byte	0x1e1
	.byte	0x8
	.4byte	0x120
	.byte	0x18
	.uleb128 0x9
	.string	"acc"
	.byte	0x3
	.2byte	0x1e3
	.byte	0xb
	.4byte	0x120
	.byte	0x30
	.uleb128 0xa
	.4byte	.LASF1283
	.byte	0x3
	.2byte	0x1e4
	.byte	0xb
	.4byte	0x120
	.byte	0x48
	.uleb128 0xa
	.4byte	.LASF1284
	.byte	0x3
	.2byte	0x1e5
	.byte	0x8
	.4byte	0xd4
	.byte	0x60
	.uleb128 0x9
	.string	"mag"
	.byte	0x3
	.2byte	0x1e7
	.byte	0xb
	.4byte	0x120
	.byte	0x68
	.uleb128 0xa
	.4byte	.LASF1285
	.byte	0x3
	.2byte	0x1e8
	.byte	0xb
	.4byte	0x120
	.byte	0x80
	.uleb128 0xa
	.4byte	.LASF1286
	.byte	0x3
	.2byte	0x1ea
	.byte	0xb
	.4byte	0xd4
	.byte	0x98
	.uleb128 0x9
	.string	"alt"
	.byte	0x3
	.2byte	0x1eb
	.byte	0xb
	.4byte	0xd4
	.byte	0xa0
	.uleb128 0xa
	.4byte	.LASF1287
	.byte	0x3
	.2byte	0x1ec
	.byte	0xb
	.4byte	0xd4
	.byte	0xa8
	.uleb128 0x9
	.string	"dt"
	.byte	0x3
	.2byte	0x1ee
	.byte	0x8
	.4byte	0xd4
	.byte	0xb0
	.uleb128 0xa
	.4byte	.LASF1274
	.byte	0x3
	.2byte	0x1f0
	.byte	0xc
	.4byte	0xb8
	.byte	0xb8
	.uleb128 0xa
	.4byte	.LASF1288
	.byte	0x3
	.2byte	0x1f1
	.byte	0x9
	.4byte	0xb8
	.byte	0xc0
	.byte	0
	.uleb128 0x3
	.4byte	.LASF1289
	.byte	0x3
	.2byte	0x1f2
	.byte	0x2
	.4byte	0x221
	.uleb128 0x8
	.byte	0x20
	.byte	0x3
	.2byte	0x1f4
	.byte	0x9
	.4byte	0x315
	.uleb128 0x9
	.string	"mag"
	.byte	0x3
	.2byte	0x1f6
	.byte	0x8
	.4byte	0x120
	.byte	0
	.uleb128 0xa
	.4byte	.LASF1290
	.byte	0x3
	.2byte	0x1f7
	.byte	0x7
	.4byte	0x315
	.byte	0x18
	.byte	0
	.uleb128 0x2
	.byte	0x1
	.byte	0x2
	.4byte	.LASF1291
	.uleb128 0x3
	.4byte	.LASF1292
	.byte	0x3
	.2byte	0x1f8
	.byte	0x2
	.4byte	0x2ee
	.uleb128 0x8
	.byte	0x58
	.byte	0x3
	.2byte	0x237
	.byte	0x9
	.4byte	0x3e6
	.uleb128 0x9
	.string	"q0"
	.byte	0x3
	.2byte	0x239
	.byte	0x8
	.4byte	0xd4
	.byte	0
	.uleb128 0x9
	.string	"q1"
	.byte	0x3
	.2byte	0x23a
	.byte	0x8
	.4byte	0xd4
	.byte	0x8
	.uleb128 0x9
	.string	"q2"
	.byte	0x3
	.2byte	0x23b
	.byte	0x8
	.4byte	0xd4
	.byte	0x10
	.uleb128 0x9
	.string	"q3"
	.byte	0x3
	.2byte	0x23c
	.byte	0x8
	.4byte	0xd4
	.byte	0x18
	.uleb128 0xa
	.4byte	.LASF1276
	.byte	0x3
	.2byte	0x23e
	.byte	0x8
	.4byte	0xd4
	.byte	0x20
	.uleb128 0xa
	.4byte	.LASF1277
	.byte	0x3
	.2byte	0x23f
	.byte	0x8
	.4byte	0xd4
	.byte	0x28
	.uleb128 0x9
	.string	"yaw"
	.byte	0x3
	.2byte	0x240
	.byte	0x8
	.4byte	0xd4
	.byte	0x30
	.uleb128 0xa
	.4byte	.LASF1293
	.byte	0x3
	.2byte	0x242
	.byte	0x8
	.4byte	0xd4
	.byte	0x38
	.uleb128 0xa
	.4byte	.LASF1294
	.byte	0x3
	.2byte	0x243
	.byte	0x8
	.4byte	0xd4
	.byte	0x40
	.uleb128 0xa
	.4byte	.LASF1295
	.byte	0x3
	.2byte	0x244
	.byte	0x8
	.4byte	0xd4
	.byte	0x48
	.uleb128 0x9
	.string	"cnt"
	.byte	0x3
	.2byte	0x246
	.byte	0xb
	.4byte	0x74
	.byte	0x50
	.uleb128 0xa
	.4byte	.LASF1296
	.byte	0x3
	.2byte	0x248
	.byte	0x7
	.4byte	0x315
	.byte	0x54
	.uleb128 0xa
	.4byte	.LASF1297
	.byte	0x3
	.2byte	0x249
	.byte	0x7
	.4byte	0x315
	.byte	0x55
	.byte	0
	.uleb128 0x3
	.4byte	.LASF1298
	.byte	0x3
	.2byte	0x24b
	.byte	0x2
	.4byte	0x329
	.uleb128 0xb
	.4byte	.LASF1300
	.byte	0x3
	.2byte	0x24e
	.byte	0x10
	.4byte	0x3e6
	.uleb128 0x8
	.byte	0x68
	.byte	0x3
	.2byte	0x25e
	.byte	0x9
	.4byte	0x4d7
	.uleb128 0x9
	.string	"lat"
	.byte	0x3
	.2byte	0x260
	.byte	0x9
	.4byte	0xb8
	.byte	0
	.uleb128 0x9
	.string	"lon"
	.byte	0x3
	.2byte	0x261
	.byte	0x9
	.4byte	0xb8
	.byte	0x8
	.uleb128 0x9
	.string	"hgt"
	.byte	0x3
	.2byte	0x262
	.byte	0x9
	.4byte	0xd4
	.byte	0x10
	.uleb128 0xa
	.4byte	.LASF1301
	.byte	0x3
	.2byte	0x264
	.byte	0x8
	.4byte	0xd4
	.byte	0x18
	.uleb128 0xa
	.4byte	.LASF1302
	.byte	0x3
	.2byte	0x265
	.byte	0xb
	.4byte	0x60
	.byte	0x20
	.uleb128 0x9
	.string	"vn"
	.byte	0x3
	.2byte	0x267
	.byte	0x8
	.4byte	0xd4
	.byte	0x28
	.uleb128 0x9
	.string	"ve"
	.byte	0x3
	.2byte	0x268
	.byte	0x8
	.4byte	0xd4
	.byte	0x30
	.uleb128 0x9
	.string	"vd"
	.byte	0x3
	.2byte	0x269
	.byte	0x8
	.4byte	0xd4
	.byte	0x38
	.uleb128 0x9
	.string	"wn"
	.byte	0x3
	.2byte	0x26b
	.byte	0x8
	.4byte	0xd4
	.byte	0x40
	.uleb128 0x9
	.string	"we"
	.byte	0x3
	.2byte	0x26c
	.byte	0x8
	.4byte	0xd4
	.byte	0x48
	.uleb128 0x9
	.string	"wd"
	.byte	0x3
	.2byte	0x26d
	.byte	0x8
	.4byte	0xd4
	.byte	0x50
	.uleb128 0x9
	.string	"wie"
	.byte	0x3
	.2byte	0x26f
	.byte	0x8
	.4byte	0xd4
	.byte	0x58
	.uleb128 0xa
	.4byte	.LASF1303
	.byte	0x3
	.2byte	0x271
	.byte	0x7
	.4byte	0x315
	.byte	0x60
	.uleb128 0xa
	.4byte	.LASF1304
	.byte	0x3
	.2byte	0x273
	.byte	0xb
	.4byte	0x60
	.byte	0x62
	.uleb128 0xa
	.4byte	.LASF1305
	.byte	0x3
	.2byte	0x274
	.byte	0xb
	.4byte	0x60
	.byte	0x64
	.byte	0
	.uleb128 0x3
	.4byte	.LASF1306
	.byte	0x3
	.2byte	0x275
	.byte	0x2
	.4byte	0x400
	.uleb128 0x8
	.byte	0x90
	.byte	0x3
	.2byte	0x277
	.byte	0x9
	.4byte	0x62b
	.uleb128 0xa
	.4byte	.LASF1307
	.byte	0x3
	.2byte	0x279
	.byte	0xb
	.4byte	0x60
	.byte	0
	.uleb128 0xa
	.4byte	.LASF1308
	.byte	0x3
	.2byte	0x27a
	.byte	0x8
	.4byte	0xd4
	.byte	0x8
	.uleb128 0x9
	.string	"tow"
	.byte	0x3
	.2byte	0x27b
	.byte	0x8
	.4byte	0xd4
	.byte	0x10
	.uleb128 0xa
	.4byte	.LASF1309
	.byte	0x3
	.2byte	0x27c
	.byte	0x8
	.4byte	0xd4
	.byte	0x18
	.uleb128 0xa
	.4byte	.LASF1310
	.byte	0x3
	.2byte	0x27e
	.byte	0xb
	.4byte	0x60
	.byte	0x20
	.uleb128 0x9
	.string	"vn"
	.byte	0x3
	.2byte	0x27f
	.byte	0xb
	.4byte	0xd4
	.byte	0x28
	.uleb128 0x9
	.string	"ve"
	.byte	0x3
	.2byte	0x280
	.byte	0x8
	.4byte	0xd4
	.byte	0x30
	.uleb128 0x9
	.string	"vd"
	.byte	0x3
	.2byte	0x281
	.byte	0x8
	.4byte	0xd4
	.byte	0x38
	.uleb128 0xa
	.4byte	.LASF1311
	.byte	0x3
	.2byte	0x283
	.byte	0x7
	.4byte	0x29
	.byte	0x40
	.uleb128 0x9
	.string	"lat"
	.byte	0x3
	.2byte	0x284
	.byte	0x9
	.4byte	0xb8
	.byte	0x48
	.uleb128 0x9
	.string	"lon"
	.byte	0x3
	.2byte	0x285
	.byte	0x9
	.4byte	0xb8
	.byte	0x50
	.uleb128 0x9
	.string	"hgt"
	.byte	0x3
	.2byte	0x286
	.byte	0x9
	.4byte	0xd4
	.byte	0x58
	.uleb128 0xa
	.4byte	.LASF1312
	.byte	0x3
	.2byte	0x289
	.byte	0xa
	.4byte	0x45
	.byte	0x60
	.uleb128 0x9
	.string	"NS"
	.byte	0x3
	.2byte	0x28b
	.byte	0x7
	.4byte	0x29
	.byte	0x61
	.uleb128 0x9
	.string	"EW"
	.byte	0x3
	.2byte	0x28c
	.byte	0x7
	.4byte	0x29
	.byte	0x62
	.uleb128 0xa
	.4byte	.LASF1313
	.byte	0x3
	.2byte	0x28e
	.byte	0xa
	.4byte	0x45
	.byte	0x63
	.uleb128 0xa
	.4byte	.LASF1301
	.byte	0x3
	.2byte	0x28f
	.byte	0x8
	.4byte	0xd4
	.byte	0x68
	.uleb128 0xa
	.4byte	.LASF1277
	.byte	0x3
	.2byte	0x290
	.byte	0x8
	.4byte	0xd4
	.byte	0x70
	.uleb128 0x9
	.string	"bl"
	.byte	0x3
	.2byte	0x291
	.byte	0x8
	.4byte	0xd4
	.byte	0x78
	.uleb128 0xa
	.4byte	.LASF1314
	.byte	0x3
	.2byte	0x292
	.byte	0x8
	.4byte	0xd4
	.byte	0x80
	.uleb128 0xa
	.4byte	.LASF1315
	.byte	0x3
	.2byte	0x294
	.byte	0x7
	.4byte	0x315
	.byte	0x88
	.uleb128 0xa
	.4byte	.LASF1316
	.byte	0x3
	.2byte	0x295
	.byte	0x7
	.4byte	0x315
	.byte	0x89
	.uleb128 0xa
	.4byte	.LASF1317
	.byte	0x3
	.2byte	0x296
	.byte	0x7
	.4byte	0x315
	.byte	0x8a
	.byte	0
	.uleb128 0x3
	.4byte	.LASF1318
	.byte	0x3
	.2byte	0x297
	.byte	0x2
	.4byte	0x4e4
	.uleb128 0xc
	.2byte	0x8f0
	.byte	0x3
	.2byte	0x2a6
	.byte	0x9
	.4byte	0x86d
	.uleb128 0xa
	.4byte	.LASF1319
	.byte	0x3
	.2byte	0x2a8
	.byte	0xa
	.4byte	0x4d7
	.byte	0
	.uleb128 0xa
	.4byte	.LASF1320
	.byte	0x3
	.2byte	0x2aa
	.byte	0x8
	.4byte	0x120
	.byte	0x68
	.uleb128 0xa
	.4byte	.LASF1276
	.byte	0x3
	.2byte	0x2ad
	.byte	0x8
	.4byte	0xd4
	.byte	0x80
	.uleb128 0xa
	.4byte	.LASF1277
	.byte	0x3
	.2byte	0x2ad
	.byte	0xd
	.4byte	0xd4
	.byte	0x88
	.uleb128 0x9
	.string	"yaw"
	.byte	0x3
	.2byte	0x2ad
	.byte	0x13
	.4byte	0xd4
	.byte	0x90
	.uleb128 0x9
	.string	"q0"
	.byte	0x3
	.2byte	0x2af
	.byte	0x8
	.4byte	0xd4
	.byte	0x98
	.uleb128 0x9
	.string	"q1"
	.byte	0x3
	.2byte	0x2af
	.byte	0xb
	.4byte	0xd4
	.byte	0xa0
	.uleb128 0x9
	.string	"q2"
	.byte	0x3
	.2byte	0x2af
	.byte	0xe
	.4byte	0xd4
	.byte	0xa8
	.uleb128 0x9
	.string	"q3"
	.byte	0x3
	.2byte	0x2af
	.byte	0x11
	.4byte	0xd4
	.byte	0xb0
	.uleb128 0xa
	.4byte	.LASF1321
	.byte	0x3
	.2byte	0x2b2
	.byte	0x8
	.4byte	0xd4
	.byte	0xb8
	.uleb128 0xa
	.4byte	.LASF1322
	.byte	0x3
	.2byte	0x2b2
	.byte	0x10
	.4byte	0xd4
	.byte	0xc0
	.uleb128 0xa
	.4byte	.LASF1323
	.byte	0x3
	.2byte	0x2b2
	.byte	0x18
	.4byte	0xd4
	.byte	0xc8
	.uleb128 0x9
	.string	"vn"
	.byte	0x3
	.2byte	0x2b3
	.byte	0x8
	.4byte	0xd4
	.byte	0xd0
	.uleb128 0x9
	.string	"ve"
	.byte	0x3
	.2byte	0x2b3
	.byte	0xb
	.4byte	0xd4
	.byte	0xd8
	.uleb128 0x9
	.string	"vd"
	.byte	0x3
	.2byte	0x2b3
	.byte	0xe
	.4byte	0xd4
	.byte	0xe0
	.uleb128 0x9
	.string	"pn"
	.byte	0x3
	.2byte	0x2b4
	.byte	0x8
	.4byte	0xd4
	.byte	0xe8
	.uleb128 0x9
	.string	"pe"
	.byte	0x3
	.2byte	0x2b4
	.byte	0xb
	.4byte	0xd4
	.byte	0xf0
	.uleb128 0x9
	.string	"pd"
	.byte	0x3
	.2byte	0x2b4
	.byte	0xe
	.4byte	0xd4
	.byte	0xf8
	.uleb128 0xd
	.string	"lon"
	.byte	0x3
	.2byte	0x2b5
	.byte	0x9
	.4byte	0xb8
	.2byte	0x100
	.uleb128 0xd
	.string	"lat"
	.byte	0x3
	.2byte	0x2b5
	.byte	0xd
	.4byte	0xb8
	.2byte	0x108
	.uleb128 0xd
	.string	"hgt"
	.byte	0x3
	.2byte	0x2b5
	.byte	0x11
	.4byte	0xb8
	.2byte	0x110
	.uleb128 0xe
	.4byte	.LASF1324
	.byte	0x3
	.2byte	0x2ba
	.byte	0xa
	.4byte	0x16a
	.2byte	0x118
	.uleb128 0xe
	.4byte	.LASF1325
	.byte	0x3
	.2byte	0x2bb
	.byte	0xa
	.4byte	0x16a
	.2byte	0x138
	.uleb128 0xe
	.4byte	.LASF1326
	.byte	0x3
	.2byte	0x2bc
	.byte	0xa
	.4byte	0x16a
	.2byte	0x158
	.uleb128 0xe
	.4byte	.LASF1327
	.byte	0x3
	.2byte	0x2be
	.byte	0x8
	.4byte	0x120
	.2byte	0x178
	.uleb128 0xe
	.4byte	.LASF1328
	.byte	0x3
	.2byte	0x2bf
	.byte	0x8
	.4byte	0x120
	.2byte	0x190
	.uleb128 0xd
	.string	"Pk"
	.byte	0x3
	.2byte	0x2c3
	.byte	0x8
	.4byte	0x86d
	.2byte	0x1a8
	.uleb128 0xe
	.4byte	.LASF1329
	.byte	0x3
	.2byte	0x2c6
	.byte	0x7
	.4byte	0x315
	.2byte	0x8b0
	.uleb128 0xe
	.4byte	.LASF1330
	.byte	0x3
	.2byte	0x2c9
	.byte	0x7
	.4byte	0x315
	.2byte	0x8b1
	.uleb128 0xe
	.4byte	.LASF1331
	.byte	0x3
	.2byte	0x2cb
	.byte	0x7
	.4byte	0x315
	.2byte	0x8b2
	.uleb128 0xe
	.4byte	.LASF1332
	.byte	0x3
	.2byte	0x2cd
	.byte	0x7
	.4byte	0x315
	.2byte	0x8b3
	.uleb128 0xe
	.4byte	.LASF1333
	.byte	0x3
	.2byte	0x2ce
	.byte	0x7
	.4byte	0x315
	.2byte	0x8b4
	.uleb128 0xd
	.string	"dT"
	.byte	0x3
	.2byte	0x2d0
	.byte	0x8
	.4byte	0xd4
	.2byte	0x8b8
	.uleb128 0xd
	.string	"gn"
	.byte	0x3
	.2byte	0x2d1
	.byte	0x8
	.4byte	0xd4
	.2byte	0x8c0
	.uleb128 0xd
	.string	"pi"
	.byte	0x3
	.2byte	0x2d2
	.byte	0x8
	.4byte	0xd4
	.2byte	0x8c8
	.uleb128 0xe
	.4byte	.LASF1334
	.byte	0x3
	.2byte	0x2d4
	.byte	0x8
	.4byte	0xd4
	.2byte	0x8d0
	.uleb128 0xe
	.4byte	.LASF1335
	.byte	0x3
	.2byte	0x2d5
	.byte	0x8
	.4byte	0xd4
	.2byte	0x8d8
	.uleb128 0xd
	.string	"row"
	.byte	0x3
	.2byte	0x2d7
	.byte	0xb
	.4byte	0x8f
	.2byte	0x8e0
	.uleb128 0xe
	.4byte	.LASF1336
	.byte	0x3
	.2byte	0x2da
	.byte	0x7
	.4byte	0x315
	.2byte	0x8e8
	.byte	0
	.uleb128 0xf
	.4byte	0xd4
	.4byte	0x883
	.uleb128 0x10
	.4byte	0x30
	.byte	0xe
	.uleb128 0x10
	.4byte	0x30
	.byte	0xe
	.byte	0
	.uleb128 0x3
	.4byte	.LASF1337
	.byte	0x3
	.2byte	0x2dc
	.byte	0x2
	.4byte	0x638
	.uleb128 0xf
	.4byte	0x1c8
	.4byte	0x8a0
	.uleb128 0x10
	.4byte	0x30
	.byte	0x1d
	.byte	0
	.uleb128 0xb
	.4byte	.LASF1338
	.byte	0x3
	.2byte	0x30c
	.byte	0x15
	.4byte	0x890
	.uleb128 0xb
	.4byte	.LASF1339
	.byte	0x3
	.2byte	0x31d
	.byte	0x15
	.4byte	0x883
	.uleb128 0x11
	.4byte	0x3f3
	.byte	0x1
	.byte	0x4
	.byte	0x9
	.uleb128 0x5
	.byte	0x3
	.4byte	ahrs_k
	.uleb128 0x12
	.4byte	.LASF1340
	.byte	0x1
	.byte	0x6
	.byte	0x6
	.4byte	0x315
	.uleb128 0x5
	.byte	0x3
	.4byte	ahrs_titl_alig
	.uleb128 0x13
	.4byte	.LASF1341
	.byte	0x1
	.byte	0x9
	.byte	0xe
	.4byte	0xd4
	.uleb128 0x5
	.byte	0x3
	.4byte	twoant_mean
	.uleb128 0x13
	.4byte	.LASF1342
	.byte	0x1
	.byte	0xb
	.byte	0x11
	.4byte	0x60
	.uleb128 0x5
	.byte	0x3
	.4byte	imu_sample_cnt
	.uleb128 0x13
	.4byte	.LASF1343
	.byte	0x1
	.byte	0xc
	.byte	0x11
	.4byte	0x60
	.uleb128 0x5
	.byte	0x3
	.4byte	twoant_sample_cnt
	.uleb128 0x13
	.4byte	.LASF1344
	.byte	0x1
	.byte	0xe
	.byte	0x11
	.4byte	0x60
	.uleb128 0x5
	.byte	0x3
	.4byte	mag_sample_cnt
	.uleb128 0x13
	.4byte	.LASF1345
	.byte	0x1
	.byte	0xf
	.byte	0xe
	.4byte	0x120
	.uleb128 0x5
	.byte	0x3
	.4byte	mag_mean
	.uleb128 0x13
	.4byte	.LASF1346
	.byte	0x1
	.byte	0x11
	.byte	0x11
	.4byte	0x60
	.uleb128 0x5
	.byte	0x3
	.4byte	imu_sample_count
	.uleb128 0x13
	.4byte	.LASF1347
	.byte	0x1
	.byte	0x12
	.byte	0xe
	.4byte	0x120
	.uleb128 0x5
	.byte	0x3
	.4byte	wie_mean
	.uleb128 0x13
	.4byte	.LASF1348
	.byte	0x1
	.byte	0x13
	.byte	0xe
	.4byte	0x120
	.uleb128 0x5
	.byte	0x3
	.4byte	acc_mean
	.uleb128 0x14
	.4byte	.LASF1349
	.byte	0x1
	.2byte	0x166
	.byte	0xe
	.4byte	0x120
	.uleb128 0x5
	.byte	0x3
	.4byte	dAng
	.uleb128 0x14
	.4byte	.LASF1350
	.byte	0x1
	.2byte	0x237
	.byte	0x11
	.4byte	0x74
	.uleb128 0x5
	.byte	0x3
	.4byte	yaw_unfusion_cnt
	.uleb128 0x15
	.4byte	.LASF1351
	.byte	0x3
	.2byte	0x1ab
	.byte	0xe
	.4byte	0x120
	.4byte	0x9ac
	.uleb128 0x16
	.4byte	0x120
	.uleb128 0x16
	.4byte	0x120
	.byte	0
	.uleb128 0x17
	.string	"cos"
	.byte	0x4
	.byte	0xe0
	.byte	0xd
	.4byte	0xb8
	.4byte	0x9c2
	.uleb128 0x16
	.4byte	0xb8
	.byte	0
	.uleb128 0x17
	.string	"sin"
	.byte	0x4
	.byte	0xdf
	.byte	0xd
	.4byte	0xb8
	.4byte	0x9d8
	.uleb128 0x16
	.4byte	0xb8
	.byte	0
	.uleb128 0x18
	.4byte	.LASF1352
	.byte	0x4
	.byte	0xe2
	.byte	0xd
	.4byte	0xb8
	.4byte	0x9ee
	.uleb128 0x16
	.4byte	0xb8
	.byte	0
	.uleb128 0x18
	.4byte	.LASF1353
	.byte	0x4
	.byte	0xe5
	.byte	0xd
	.4byte	0xb8
	.4byte	0xa09
	.uleb128 0x16
	.4byte	0xb8
	.uleb128 0x16
	.4byte	0xb8
	.byte	0
	.uleb128 0x15
	.4byte	.LASF1354
	.byte	0x4
	.2byte	0x101
	.byte	0xd
	.4byte	0xb8
	.4byte	0xa20
	.uleb128 0x16
	.4byte	0xb8
	.byte	0
	.uleb128 0x19
	.string	"SQR"
	.byte	0x3
	.2byte	0x1aa
	.byte	0xe
	.4byte	0xd4
	.4byte	0xa37
	.uleb128 0x16
	.4byte	0xd4
	.byte	0
	.uleb128 0x1a
	.4byte	.LASF1358
	.byte	0x1
	.2byte	0x303
	.byte	0x6
	.4byte	.LFB3
	.4byte	.LFE3-.LFB3
	.uleb128 0x1
	.byte	0x9c
	.4byte	0xa7f
	.uleb128 0x1b
	.4byte	.LASF1355
	.byte	0x1
	.2byte	0x303
	.byte	0x1f
	.4byte	0xa7f
	.uleb128 0x2
	.byte	0x91
	.sleb128 -20
	.uleb128 0x1b
	.4byte	.LASF1356
	.byte	0x1
	.2byte	0x303
	.byte	0x35
	.4byte	0xa85
	.uleb128 0x2
	.byte	0x91
	.sleb128 -24
	.uleb128 0x1b
	.4byte	.LASF1357
	.byte	0x1
	.2byte	0x303
	.byte	0x4b
	.4byte	0xa8b
	.uleb128 0x2
	.byte	0x91
	.sleb128 -28
	.byte	0
	.uleb128 0x1c
	.byte	0x4
	.4byte	0x2e1
	.uleb128 0x1c
	.byte	0x4
	.4byte	0x31c
	.uleb128 0x1c
	.byte	0x4
	.4byte	0x62b
	.uleb128 0x1a
	.4byte	.LASF1359
	.byte	0x1
	.2byte	0x238
	.byte	0x6
	.4byte	.LFB2
	.4byte	.LFE2-.LFB2
	.uleb128 0x1
	.byte	0x9c
	.4byte	0xc6a
	.uleb128 0x1b
	.4byte	.LASF1355
	.byte	0x1
	.2byte	0x238
	.byte	0x23
	.4byte	0xa7f
	.uleb128 0x3
	.byte	0x91
	.sleb128 -324
	.uleb128 0x1b
	.4byte	.LASF1356
	.byte	0x1
	.2byte	0x238
	.byte	0x39
	.4byte	0xa85
	.uleb128 0x3
	.byte	0x91
	.sleb128 -328
	.uleb128 0x1b
	.4byte	.LASF1357
	.byte	0x1
	.2byte	0x238
	.byte	0x4f
	.4byte	0xa8b
	.uleb128 0x3
	.byte	0x91
	.sleb128 -332
	.uleb128 0x1d
	.string	"i"
	.byte	0x1
	.2byte	0x23a
	.byte	0xe
	.4byte	0x60
	.uleb128 0x2
	.byte	0x91
	.sleb128 -50
	.uleb128 0x14
	.4byte	.LASF1360
	.byte	0x1
	.2byte	0x23b
	.byte	0x11
	.4byte	0xe0
	.uleb128 0x3
	.byte	0x91
	.sleb128 -96
	.uleb128 0x1d
	.string	"q0"
	.byte	0x1
	.2byte	0x23c
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -104
	.uleb128 0x1d
	.string	"q1"
	.byte	0x1
	.2byte	0x23c
	.byte	0x14
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -112
	.uleb128 0x1d
	.string	"q2"
	.byte	0x1
	.2byte	0x23c
	.byte	0x1d
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -120
	.uleb128 0x1d
	.string	"q3"
	.byte	0x1
	.2byte	0x23c
	.byte	0x26
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -128
	.uleb128 0x14
	.4byte	.LASF1361
	.byte	0x1
	.2byte	0x23d
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -136
	.uleb128 0x14
	.4byte	.LASF1362
	.byte	0x1
	.2byte	0x23d
	.byte	0x1b
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -144
	.uleb128 0x14
	.4byte	.LASF1363
	.byte	0x1
	.2byte	0x23e
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -152
	.uleb128 0x14
	.4byte	.LASF1364
	.byte	0x1
	.2byte	0x23f
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -160
	.uleb128 0x14
	.4byte	.LASF1365
	.byte	0x1
	.2byte	0x23f
	.byte	0x18
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -168
	.uleb128 0x14
	.4byte	.LASF1366
	.byte	0x1
	.2byte	0x240
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -176
	.uleb128 0x14
	.4byte	.LASF1367
	.byte	0x1
	.2byte	0x240
	.byte	0x18
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -184
	.uleb128 0x14
	.4byte	.LASF1317
	.byte	0x1
	.2byte	0x241
	.byte	0xa
	.4byte	0x315
	.uleb128 0x2
	.byte	0x91
	.sleb128 -51
	.uleb128 0x14
	.4byte	.LASF1368
	.byte	0x1
	.2byte	0x243
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x2
	.byte	0x91
	.sleb128 -64
	.uleb128 0x14
	.4byte	.LASF1369
	.byte	0x1
	.2byte	0x244
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -72
	.uleb128 0x14
	.4byte	.LASF1370
	.byte	0x1
	.2byte	0x245
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -80
	.uleb128 0x14
	.4byte	.LASF1371
	.byte	0x1
	.2byte	0x247
	.byte	0xb
	.4byte	0x120
	.uleb128 0x3
	.byte	0x91
	.sleb128 -224
	.uleb128 0x14
	.4byte	.LASF1372
	.byte	0x1
	.2byte	0x248
	.byte	0xd
	.4byte	0x16a
	.uleb128 0x3
	.byte	0x91
	.sleb128 -256
	.uleb128 0x14
	.4byte	.LASF1373
	.byte	0x1
	.2byte	0x24a
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -88
	.uleb128 0x14
	.4byte	.LASF1374
	.byte	0x1
	.2byte	0x24b
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -192
	.uleb128 0x1d
	.string	"dq"
	.byte	0x1
	.2byte	0x24d
	.byte	0xb
	.4byte	0x214
	.uleb128 0x3
	.byte	0x91
	.sleb128 -288
	.uleb128 0x14
	.4byte	.LASF1375
	.byte	0x1
	.2byte	0x24e
	.byte	0xb
	.4byte	0x214
	.uleb128 0x3
	.byte	0x91
	.sleb128 -320
	.uleb128 0x14
	.4byte	.LASF1376
	.byte	0x1
	.2byte	0x250
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -200
	.byte	0
	.uleb128 0x1a
	.4byte	.LASF1377
	.byte	0x1
	.2byte	0x167
	.byte	0x6
	.4byte	.LFB1
	.4byte	.LFE1-.LFB1
	.uleb128 0x1
	.byte	0x9c
	.4byte	0xf25
	.uleb128 0x1b
	.4byte	.LASF1355
	.byte	0x1
	.2byte	0x167
	.byte	0x23
	.4byte	0xa7f
	.uleb128 0x3
	.byte	0x91
	.sleb128 -596
	.uleb128 0x1b
	.4byte	.LASF1357
	.byte	0x1
	.2byte	0x167
	.byte	0x3a
	.4byte	0xa8b
	.uleb128 0x3
	.byte	0x91
	.sleb128 -600
	.uleb128 0x14
	.4byte	.LASF1378
	.byte	0x1
	.2byte	0x169
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -88
	.uleb128 0x1d
	.string	"dt"
	.byte	0x1
	.2byte	0x16a
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -96
	.uleb128 0x14
	.4byte	.LASF1379
	.byte	0x1
	.2byte	0x16b
	.byte	0xb
	.4byte	0x120
	.uleb128 0x3
	.byte	0x91
	.sleb128 -296
	.uleb128 0x14
	.4byte	.LASF1380
	.byte	0x1
	.2byte	0x16d
	.byte	0xd
	.4byte	0x16a
	.uleb128 0x3
	.byte	0x91
	.sleb128 -328
	.uleb128 0x14
	.4byte	.LASF1381
	.byte	0x1
	.2byte	0x16e
	.byte	0xb
	.4byte	0x120
	.uleb128 0x3
	.byte	0x91
	.sleb128 -352
	.uleb128 0x1d
	.string	"q0"
	.byte	0x1
	.2byte	0x170
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -104
	.uleb128 0x1d
	.string	"q1"
	.byte	0x1
	.2byte	0x170
	.byte	0x14
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -112
	.uleb128 0x1d
	.string	"q2"
	.byte	0x1
	.2byte	0x170
	.byte	0x1d
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -120
	.uleb128 0x1d
	.string	"q3"
	.byte	0x1
	.2byte	0x170
	.byte	0x26
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -128
	.uleb128 0x14
	.4byte	.LASF1382
	.byte	0x1
	.2byte	0x171
	.byte	0xb
	.4byte	0xf25
	.uleb128 0x3
	.byte	0x91
	.sleb128 -424
	.uleb128 0x14
	.4byte	.LASF1281
	.byte	0x1
	.2byte	0x173
	.byte	0xb
	.4byte	0x120
	.uleb128 0x3
	.byte	0x91
	.sleb128 -448
	.uleb128 0x14
	.4byte	.LASF1383
	.byte	0x1
	.2byte	0x174
	.byte	0xb
	.4byte	0x120
	.uleb128 0x3
	.byte	0x91
	.sleb128 -472
	.uleb128 0x1d
	.string	"phi"
	.byte	0x1
	.2byte	0x175
	.byte	0xb
	.4byte	0x120
	.uleb128 0x3
	.byte	0x91
	.sleb128 -496
	.uleb128 0x1d
	.string	"qh"
	.byte	0x1
	.2byte	0x176
	.byte	0xb
	.4byte	0x214
	.uleb128 0x3
	.byte	0x91
	.sleb128 -528
	.uleb128 0x14
	.4byte	.LASF1375
	.byte	0x1
	.2byte	0x177
	.byte	0xb
	.4byte	0x214
	.uleb128 0x3
	.byte	0x91
	.sleb128 -560
	.uleb128 0x14
	.4byte	.LASF1376
	.byte	0x1
	.2byte	0x179
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -136
	.uleb128 0x1d
	.string	"acc"
	.byte	0x1
	.2byte	0x17b
	.byte	0xb
	.4byte	0x120
	.uleb128 0x3
	.byte	0x91
	.sleb128 -584
	.uleb128 0x1d
	.string	"s0"
	.byte	0x1
	.2byte	0x17d
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x2
	.byte	0x91
	.sleb128 -56
	.uleb128 0x1d
	.string	"s1"
	.byte	0x1
	.2byte	0x17d
	.byte	0x14
	.4byte	0xd4
	.uleb128 0x2
	.byte	0x91
	.sleb128 -64
	.uleb128 0x1d
	.string	"s2"
	.byte	0x1
	.2byte	0x17d
	.byte	0x1d
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -72
	.uleb128 0x1d
	.string	"s3"
	.byte	0x1
	.2byte	0x17d
	.byte	0x26
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -80
	.uleb128 0x1d
	.string	"ax"
	.byte	0x1
	.2byte	0x17e
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -144
	.uleb128 0x1d
	.string	"ay"
	.byte	0x1
	.2byte	0x17e
	.byte	0x14
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -152
	.uleb128 0x1d
	.string	"az"
	.byte	0x1
	.2byte	0x17e
	.byte	0x1d
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -160
	.uleb128 0x14
	.4byte	.LASF1384
	.byte	0x1
	.2byte	0x1c3
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -168
	.uleb128 0x1e
	.4byte	.LBB3
	.4byte	.LBE3-.LBB3
	.uleb128 0x1d
	.string	"tq0"
	.byte	0x1
	.2byte	0x1f5
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -176
	.uleb128 0x1d
	.string	"tq1"
	.byte	0x1
	.2byte	0x1f6
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -184
	.uleb128 0x1d
	.string	"tq2"
	.byte	0x1
	.2byte	0x1f7
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -192
	.uleb128 0x1d
	.string	"tq3"
	.byte	0x1
	.2byte	0x1f8
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -200
	.uleb128 0x1d
	.string	"fq0"
	.byte	0x1
	.2byte	0x1f9
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -208
	.uleb128 0x1d
	.string	"fq1"
	.byte	0x1
	.2byte	0x1fa
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -216
	.uleb128 0x1d
	.string	"fq2"
	.byte	0x1
	.2byte	0x1fb
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -224
	.uleb128 0x1d
	.string	"eq1"
	.byte	0x1
	.2byte	0x1fc
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -232
	.uleb128 0x1d
	.string	"eq2"
	.byte	0x1
	.2byte	0x1fd
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -240
	.uleb128 0x14
	.4byte	.LASF1385
	.byte	0x1
	.2byte	0x1ff
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -248
	.uleb128 0x14
	.4byte	.LASF1386
	.byte	0x1
	.2byte	0x200
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -256
	.uleb128 0x14
	.4byte	.LASF1387
	.byte	0x1
	.2byte	0x201
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -264
	.uleb128 0x14
	.4byte	.LASF1388
	.byte	0x1
	.2byte	0x202
	.byte	0xf
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -272
	.byte	0
	.byte	0
	.uleb128 0xf
	.4byte	0xd4
	.4byte	0xf3b
	.uleb128 0x10
	.4byte	0x30
	.byte	0x2
	.uleb128 0x10
	.4byte	0x30
	.byte	0x2
	.byte	0
	.uleb128 0x1f
	.4byte	.LASF1400
	.byte	0x1
	.byte	0x15
	.byte	0x6
	.4byte	.LFB0
	.4byte	.LFE0-.LFB0
	.uleb128 0x1
	.byte	0x9c
	.uleb128 0x20
	.4byte	.LASF1355
	.byte	0x1
	.byte	0x15
	.byte	0x1d
	.4byte	0xa7f
	.uleb128 0x3
	.byte	0x91
	.sleb128 -340
	.uleb128 0x20
	.4byte	.LASF1356
	.byte	0x1
	.byte	0x15
	.byte	0x33
	.4byte	0xa85
	.uleb128 0x3
	.byte	0x91
	.sleb128 -344
	.uleb128 0x20
	.4byte	.LASF1357
	.byte	0x1
	.byte	0x15
	.byte	0x49
	.4byte	0xa8b
	.uleb128 0x3
	.byte	0x91
	.sleb128 -348
	.uleb128 0x20
	.4byte	.LASF1305
	.byte	0x1
	.byte	0x15
	.byte	0x5c
	.4byte	0x60
	.uleb128 0x3
	.byte	0x91
	.sleb128 -350
	.uleb128 0x13
	.4byte	.LASF1317
	.byte	0x1
	.byte	0x17
	.byte	0xa
	.4byte	0x315
	.uleb128 0x3
	.byte	0x91
	.sleb128 -65
	.uleb128 0x13
	.4byte	.LASF1389
	.byte	0x1
	.byte	0x18
	.byte	0xa
	.4byte	0x315
	.uleb128 0x3
	.byte	0x91
	.sleb128 -66
	.uleb128 0x13
	.4byte	.LASF1390
	.byte	0x1
	.byte	0x19
	.byte	0xa
	.4byte	0x315
	.uleb128 0x3
	.byte	0x91
	.sleb128 -67
	.uleb128 0x13
	.4byte	.LASF1368
	.byte	0x1
	.byte	0x1b
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -104
	.uleb128 0x13
	.4byte	.LASF1380
	.byte	0x1
	.byte	0x1c
	.byte	0xb
	.4byte	0x120
	.uleb128 0x3
	.byte	0x91
	.sleb128 -304
	.uleb128 0x13
	.4byte	.LASF1371
	.byte	0x1
	.byte	0x1d
	.byte	0xb
	.4byte	0x120
	.uleb128 0x3
	.byte	0x91
	.sleb128 -328
	.uleb128 0x13
	.4byte	.LASF1284
	.byte	0x1
	.byte	0x1f
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -112
	.uleb128 0x13
	.4byte	.LASF1391
	.byte	0x1
	.byte	0x20
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -120
	.uleb128 0x13
	.4byte	.LASF1392
	.byte	0x1
	.byte	0x22
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -80
	.uleb128 0x13
	.4byte	.LASF1393
	.byte	0x1
	.byte	0x23
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -128
	.uleb128 0x13
	.4byte	.LASF1384
	.byte	0x1
	.byte	0x25
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -136
	.uleb128 0x13
	.4byte	.LASF1276
	.byte	0x1
	.byte	0x26
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -144
	.uleb128 0x13
	.4byte	.LASF1277
	.byte	0x1
	.byte	0x26
	.byte	0x15
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -152
	.uleb128 0x21
	.string	"yaw"
	.byte	0x1
	.byte	0x26
	.byte	0x20
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -88
	.uleb128 0x13
	.4byte	.LASF1364
	.byte	0x1
	.byte	0x27
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -160
	.uleb128 0x13
	.4byte	.LASF1366
	.byte	0x1
	.byte	0x27
	.byte	0x19
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -168
	.uleb128 0x13
	.4byte	.LASF1365
	.byte	0x1
	.byte	0x27
	.byte	0x27
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -176
	.uleb128 0x13
	.4byte	.LASF1367
	.byte	0x1
	.byte	0x27
	.byte	0x36
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -184
	.uleb128 0x13
	.4byte	.LASF1394
	.byte	0x1
	.byte	0x28
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -96
	.uleb128 0x13
	.4byte	.LASF1395
	.byte	0x1
	.byte	0x29
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -192
	.uleb128 0x21
	.string	"u1"
	.byte	0x1
	.byte	0x2b
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -200
	.uleb128 0x21
	.string	"u2"
	.byte	0x1
	.byte	0x2b
	.byte	0x14
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -208
	.uleb128 0x21
	.string	"u3"
	.byte	0x1
	.byte	0x2b
	.byte	0x1d
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -216
	.uleb128 0x21
	.string	"u4"
	.byte	0x1
	.byte	0x2b
	.byte	0x26
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -224
	.uleb128 0x21
	.string	"u5"
	.byte	0x1
	.byte	0x2b
	.byte	0x2f
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -232
	.uleb128 0x21
	.string	"u6"
	.byte	0x1
	.byte	0x2b
	.byte	0x38
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -240
	.uleb128 0x21
	.string	"q0"
	.byte	0x1
	.byte	0x2c
	.byte	0xb
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -248
	.uleb128 0x21
	.string	"q1"
	.byte	0x1
	.byte	0x2c
	.byte	0x14
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -256
	.uleb128 0x21
	.string	"q2"
	.byte	0x1
	.byte	0x2c
	.byte	0x1d
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -264
	.uleb128 0x21
	.string	"q3"
	.byte	0x1
	.byte	0x2c
	.byte	0x26
	.4byte	0xd4
	.uleb128 0x3
	.byte	0x91
	.sleb128 -272
	.uleb128 0x1e
	.4byte	.LBB2
	.4byte	.LBE2-.LBB2
	.uleb128 0x13
	.4byte	.LASF1396
	.byte	0x1
	.byte	0xe6
	.byte	0xe
	.4byte	0x315
	.uleb128 0x3
	.byte	0x91
	.sleb128 -273
	.byte	0
	.byte	0
	.byte	0
	.section	.debug_abbrev,"",@progbits
.Ldebug_abbrev0:
	.uleb128 0x1
	.uleb128 0x11
	.byte	0x1
	.uleb128 0x25
	.uleb128 0xe
	.uleb128 0x13
	.uleb128 0xb
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x1b
	.uleb128 0xe
	.uleb128 0x2134
	.uleb128 0x19
	.uleb128 0x55
	.uleb128 0x17
	.uleb128 0x11
	.uleb128 0x1
	.uleb128 0x10
	.uleb128 0x17
	.uleb128 0x2119
	.uleb128 0x17
	.byte	0
	.byte	0
	.uleb128 0x2
	.uleb128 0x24
	.byte	0
	.uleb128 0xb
	.uleb128 0xb
	.uleb128 0x3e
	.uleb128 0xb
	.uleb128 0x3
	.uleb128 0xe
	.byte	0
	.byte	0
	.uleb128 0x3
	.uleb128 0x16
	.byte	0
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x4
	.uleb128 0x24
	.byte	0
	.uleb128 0xb
	.uleb128 0xb
	.uleb128 0x3e
	.uleb128 0xb
	.uleb128 0x3
	.uleb128 0x8
	.byte	0
	.byte	0
	.uleb128 0x5
	.uleb128 0x16
	.byte	0
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0xb
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x6
	.uleb128 0x26
	.byte	0
	.uleb128 0x49
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x7
	.uleb128 0x34
	.byte	0
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0xb
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x3f
	.uleb128 0x19
	.uleb128 0x3c
	.uleb128 0x19
	.byte	0
	.byte	0
	.uleb128 0x8
	.uleb128 0x13
	.byte	0x1
	.uleb128 0xb
	.uleb128 0xb
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x1
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x9
	.uleb128 0xd
	.byte	0
	.uleb128 0x3
	.uleb128 0x8
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x38
	.uleb128 0xb
	.byte	0
	.byte	0
	.uleb128 0xa
	.uleb128 0xd
	.byte	0
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x38
	.uleb128 0xb
	.byte	0
	.byte	0
	.uleb128 0xb
	.uleb128 0x34
	.byte	0
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x3f
	.uleb128 0x19
	.uleb128 0x3c
	.uleb128 0x19
	.byte	0
	.byte	0
	.uleb128 0xc
	.uleb128 0x13
	.byte	0x1
	.uleb128 0xb
	.uleb128 0x5
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x1
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0xd
	.uleb128 0xd
	.byte	0
	.uleb128 0x3
	.uleb128 0x8
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x38
	.uleb128 0x5
	.byte	0
	.byte	0
	.uleb128 0xe
	.uleb128 0xd
	.byte	0
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x38
	.uleb128 0x5
	.byte	0
	.byte	0
	.uleb128 0xf
	.uleb128 0x1
	.byte	0x1
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x1
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x10
	.uleb128 0x21
	.byte	0
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x2f
	.uleb128 0xb
	.byte	0
	.byte	0
	.uleb128 0x11
	.uleb128 0x34
	.byte	0
	.uleb128 0x47
	.uleb128 0x13
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0xb
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x2
	.uleb128 0x18
	.byte	0
	.byte	0
	.uleb128 0x12
	.uleb128 0x34
	.byte	0
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0xb
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x3f
	.uleb128 0x19
	.uleb128 0x2
	.uleb128 0x18
	.byte	0
	.byte	0
	.uleb128 0x13
	.uleb128 0x34
	.byte	0
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0xb
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x2
	.uleb128 0x18
	.byte	0
	.byte	0
	.uleb128 0x14
	.uleb128 0x34
	.byte	0
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x2
	.uleb128 0x18
	.byte	0
	.byte	0
	.uleb128 0x15
	.uleb128 0x2e
	.byte	0x1
	.uleb128 0x3f
	.uleb128 0x19
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x27
	.uleb128 0x19
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x3c
	.uleb128 0x19
	.uleb128 0x1
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x16
	.uleb128 0x5
	.byte	0
	.uleb128 0x49
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x17
	.uleb128 0x2e
	.byte	0x1
	.uleb128 0x3f
	.uleb128 0x19
	.uleb128 0x3
	.uleb128 0x8
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0xb
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x27
	.uleb128 0x19
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x3c
	.uleb128 0x19
	.uleb128 0x1
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x18
	.uleb128 0x2e
	.byte	0x1
	.uleb128 0x3f
	.uleb128 0x19
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0xb
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x27
	.uleb128 0x19
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x3c
	.uleb128 0x19
	.uleb128 0x1
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x19
	.uleb128 0x2e
	.byte	0x1
	.uleb128 0x3f
	.uleb128 0x19
	.uleb128 0x3
	.uleb128 0x8
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x27
	.uleb128 0x19
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x3c
	.uleb128 0x19
	.uleb128 0x1
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x1a
	.uleb128 0x2e
	.byte	0x1
	.uleb128 0x3f
	.uleb128 0x19
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x27
	.uleb128 0x19
	.uleb128 0x11
	.uleb128 0x1
	.uleb128 0x12
	.uleb128 0x6
	.uleb128 0x40
	.uleb128 0x18
	.uleb128 0x2116
	.uleb128 0x19
	.uleb128 0x1
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x1b
	.uleb128 0x5
	.byte	0
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x2
	.uleb128 0x18
	.byte	0
	.byte	0
	.uleb128 0x1c
	.uleb128 0xf
	.byte	0
	.uleb128 0xb
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.byte	0
	.byte	0
	.uleb128 0x1d
	.uleb128 0x34
	.byte	0
	.uleb128 0x3
	.uleb128 0x8
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0x5
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x2
	.uleb128 0x18
	.byte	0
	.byte	0
	.uleb128 0x1e
	.uleb128 0xb
	.byte	0x1
	.uleb128 0x11
	.uleb128 0x1
	.uleb128 0x12
	.uleb128 0x6
	.byte	0
	.byte	0
	.uleb128 0x1f
	.uleb128 0x2e
	.byte	0x1
	.uleb128 0x3f
	.uleb128 0x19
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0xb
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x27
	.uleb128 0x19
	.uleb128 0x11
	.uleb128 0x1
	.uleb128 0x12
	.uleb128 0x6
	.uleb128 0x40
	.uleb128 0x18
	.uleb128 0x2116
	.uleb128 0x19
	.byte	0
	.byte	0
	.uleb128 0x20
	.uleb128 0x5
	.byte	0
	.uleb128 0x3
	.uleb128 0xe
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0xb
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x2
	.uleb128 0x18
	.byte	0
	.byte	0
	.uleb128 0x21
	.uleb128 0x34
	.byte	0
	.uleb128 0x3
	.uleb128 0x8
	.uleb128 0x3a
	.uleb128 0xb
	.uleb128 0x3b
	.uleb128 0xb
	.uleb128 0x39
	.uleb128 0xb
	.uleb128 0x49
	.uleb128 0x13
	.uleb128 0x2
	.uleb128 0x18
	.byte	0
	.byte	0
	.byte	0
	.section	.debug_pubnames,"",@progbits
	.4byte	0x159
	.2byte	0x2
	.4byte	.Ldebug_info0
	.4byte	0x117f
	.4byte	0x8ba
	.string	"ahrs_k"
	.4byte	0x8c8
	.string	"ahrs_titl_alig"
	.4byte	0x8da
	.string	"twoant_mean"
	.4byte	0x8ec
	.string	"imu_sample_cnt"
	.4byte	0x8fe
	.string	"twoant_sample_cnt"
	.4byte	0x910
	.string	"mag_sample_cnt"
	.4byte	0x922
	.string	"mag_mean"
	.4byte	0x934
	.string	"imu_sample_count"
	.4byte	0x946
	.string	"wie_mean"
	.4byte	0x958
	.string	"acc_mean"
	.4byte	0x96a
	.string	"dAng"
	.4byte	0x97d
	.string	"yaw_unfusion_cnt"
	.4byte	0x990
	.string	"v3_cross"
	.4byte	0x9ac
	.string	"cos"
	.4byte	0x9c2
	.string	"sin"
	.4byte	0x9d8
	.string	"asin"
	.4byte	0x9ee
	.string	"atan2"
	.4byte	0xa09
	.string	"sqrt"
	.4byte	0xa20
	.string	"SQR"
	.4byte	0xa37
	.string	"ahrs_update"
	.4byte	0xa91
	.string	"ahrs_mag_update"
	.4byte	0xc6a
	.string	"ahrs_imu_update"
	.4byte	0xf3b
	.string	"ahrs_init"
	.4byte	0
	.section	.debug_pubtypes,"",@progbits
	.4byte	0x1f9
	.2byte	0x2
	.4byte	.Ldebug_info0
	.4byte	0x117f
	.4byte	0x29
	.string	"char"
	.4byte	0x30
	.string	"unsigned int"
	.4byte	0x37
	.string	"long int"
	.4byte	0x3e
	.string	"signed char"
	.4byte	0x52
	.string	"unsigned char"
	.4byte	0x45
	.string	"uint8_t"
	.4byte	0x59
	.string	"short int"
	.4byte	0x6d
	.string	"short unsigned int"
	.4byte	0x60
	.string	"uint16_t"
	.4byte	0x81
	.string	"long unsigned int"
	.4byte	0x74
	.string	"uint32_t"
	.4byte	0x88
	.string	"long long int"
	.4byte	0x9c
	.string	"long long unsigned int"
	.4byte	0x8f
	.string	"uint64_t"
	.4byte	0xa3
	.string	"int"
	.4byte	0xaa
	.string	"float"
	.4byte	0xb1
	.string	"complex float"
	.4byte	0xb8
	.string	"double"
	.4byte	0xbf
	.string	"complex double"
	.4byte	0xc6
	.string	"long double"
	.4byte	0xcd
	.string	"complex long double"
	.4byte	0xd4
	.string	"ftype"
	.4byte	0x120
	.string	"V3_ST"
	.4byte	0x16a
	.string	"VNED_ST"
	.4byte	0x1c8
	.string	"EULER_STRUCT"
	.4byte	0x214
	.string	"Q4_ST"
	.4byte	0x2e1
	.string	"SENS_RAW_ST"
	.4byte	0x315
	.string	"_Bool"
	.4byte	0x31c
	.string	"MAG_RAW_ST"
	.4byte	0x3e6
	.string	"AHRS_ST"
	.4byte	0x4d7
	.string	"ORIG_ST"
	.4byte	0x62b
	.string	"GNSS_RAW_ST"
	.4byte	0x883
	.string	"INS_STATE_ST"
	.4byte	0
	.section	.debug_aranges,"",@progbits
	.4byte	0x34
	.2byte	0x2
	.4byte	.Ldebug_info0
	.byte	0x4
	.byte	0
	.2byte	0
	.2byte	0
	.4byte	.LFB0
	.4byte	.LFE0-.LFB0
	.4byte	.LFB1
	.4byte	.LFE1-.LFB1
	.4byte	.LFB2
	.4byte	.LFE2-.LFB2
	.4byte	.LFB3
	.4byte	.LFE3-.LFB3
	.4byte	0
	.4byte	0
	.section	.debug_ranges,"",@progbits
.Ldebug_ranges0:
	.4byte	.LFB0
	.4byte	.LFE0
	.4byte	.LFB1
	.4byte	.LFE1
	.4byte	.LFB2
	.4byte	.LFE2
	.4byte	.LFB3
	.4byte	.LFE3
	.4byte	0
	.4byte	0
	.section	.debug_macro,"",@progbits
.Ldebug_macro0:
	.2byte	0x4
	.byte	0x2
	.4byte	.Ldebug_line0
	.byte	0x7
	.4byte	.Ldebug_macro2
	.byte	0x3
	.uleb128 0
	.uleb128 0x1
	.byte	0x3
	.uleb128 0x1
	.uleb128 0x3
	.byte	0x5
	.uleb128 0x2
	.4byte	.LASF400
	.file 5 "D:/Program Files/SEGGER/SEGGER Embedded Studio 8.12a/include/stdio.h"
	.byte	0x3
	.uleb128 0x4
	.uleb128 0x5
	.byte	0x5
	.uleb128 0xb
	.4byte	.LASF401
	.file 6 "D:/Program Files/SEGGER/SEGGER Embedded Studio 8.12a/include/__SEGGER_RTL.h"
	.byte	0x3
	.uleb128 0x14
	.uleb128 0x6
	.byte	0x5
	.uleb128 0xb
	.4byte	.LASF402
	.file 7 "D:/Program Files/SEGGER/SEGGER Embedded Studio 8.12a/include/__SEGGER_RTL_ConfDefaults.h"
	.byte	0x3
	.uleb128 0x11
	.uleb128 0x7
	.byte	0x7
	.4byte	.Ldebug_macro3
	.file 8 "D:/Program Files/SEGGER/SEGGER Embedded Studio 8.12a/include/__SEGGER_RTL_Conf.h"
	.byte	0x3
	.uleb128 0x43
	.uleb128 0x8
	.byte	0x5
	.uleb128 0xb
	.4byte	.LASF415
	.file 9 "D:/Program Files/SEGGER/SEGGER Embedded Studio 8.12a/include/__SEGGER_RTL_RISCV_Conf.h"
	.byte	0x3
	.uleb128 0x25
	.uleb128 0x9
	.byte	0x7
	.4byte	.Ldebug_macro4
	.byte	0x4
	.byte	0x4
	.byte	0x7
	.4byte	.Ldebug_macro5
	.byte	0x4
	.byte	0x7
	.4byte	.Ldebug_macro6
	.byte	0x4
	.byte	0x7
	.4byte	.Ldebug_macro7
	.byte	0x4
	.file 10 "D:/Program Files/SEGGER/SEGGER Embedded Studio 8.12a/include/stdbool.h"
	.byte	0x3
	.uleb128 0x5
	.uleb128 0xa
	.byte	0x7
	.4byte	.Ldebug_macro8
	.byte	0x4
	.file 11 "D:/Program Files/SEGGER/SEGGER Embedded Studio 8.12a/include/string.h"
	.byte	0x3
	.uleb128 0x6
	.uleb128 0xb
	.byte	0x5
	.uleb128 0xb
	.4byte	.LASF738
	.byte	0x4
	.byte	0x3
	.uleb128 0x7
	.uleb128 0x2
	.byte	0x7
	.4byte	.Ldebug_macro9
	.byte	0x4
	.file 12 "D:/Program Files/SEGGER/SEGGER Embedded Studio 8.12a/include/ctype.h"
	.byte	0x3
	.uleb128 0x8
	.uleb128 0xc
	.byte	0x5
	.uleb128 0xb
	.4byte	.LASF834
	.byte	0x4
	.byte	0x3
	.uleb128 0x9
	.uleb128 0x4
	.byte	0x5
	.uleb128 0xb
	.4byte	.LASF835
	.file 13 "D:/Program Files/SEGGER/SEGGER Embedded Studio 8.12a/include/__SEGGER_RTL_FP.h"
	.byte	0x3
	.uleb128 0x14
	.uleb128 0xd
	.byte	0x7
	.4byte	.Ldebug_macro10
	.byte	0x4
	.byte	0x7
	.4byte	.Ldebug_macro11
	.byte	0x4
	.byte	0x7
	.4byte	.Ldebug_macro12
	.byte	0x4
	.file 14 "E:\\2014902\\HPM6750\\HPM6750_INVProject\\my_project\\app\\gpio\\src\\INAV\\CONST.h"
	.byte	0x3
	.uleb128 0x2
	.uleb128 0xe
	.byte	0x5
	.uleb128 0x2
	.4byte	.LASF920
	.file 15 "../../src/Source/inc/deviceconfig.h"
	.byte	0x3
	.uleb128 0x4
	.uleb128 0xf
	.byte	0x7
	.4byte	.Ldebug_macro13
	.byte	0x4
	.byte	0x7
	.4byte	.Ldebug_macro14
	.byte	0x4
	.byte	0x4
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.0.e5c79900f08a009f124df384d3bf8afc,comdat
.Ldebug_macro2:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0
	.4byte	.LASF0
	.byte	0x5
	.uleb128 0
	.4byte	.LASF1
	.byte	0x5
	.uleb128 0
	.4byte	.LASF2
	.byte	0x5
	.uleb128 0
	.4byte	.LASF3
	.byte	0x5
	.uleb128 0
	.4byte	.LASF4
	.byte	0x5
	.uleb128 0
	.4byte	.LASF5
	.byte	0x5
	.uleb128 0
	.4byte	.LASF6
	.byte	0x5
	.uleb128 0
	.4byte	.LASF7
	.byte	0x5
	.uleb128 0
	.4byte	.LASF8
	.byte	0x5
	.uleb128 0
	.4byte	.LASF9
	.byte	0x5
	.uleb128 0
	.4byte	.LASF10
	.byte	0x5
	.uleb128 0
	.4byte	.LASF11
	.byte	0x5
	.uleb128 0
	.4byte	.LASF12
	.byte	0x5
	.uleb128 0
	.4byte	.LASF13
	.byte	0x5
	.uleb128 0
	.4byte	.LASF14
	.byte	0x5
	.uleb128 0
	.4byte	.LASF15
	.byte	0x5
	.uleb128 0
	.4byte	.LASF16
	.byte	0x5
	.uleb128 0
	.4byte	.LASF17
	.byte	0x5
	.uleb128 0
	.4byte	.LASF18
	.byte	0x5
	.uleb128 0
	.4byte	.LASF19
	.byte	0x5
	.uleb128 0
	.4byte	.LASF20
	.byte	0x5
	.uleb128 0
	.4byte	.LASF21
	.byte	0x5
	.uleb128 0
	.4byte	.LASF22
	.byte	0x5
	.uleb128 0
	.4byte	.LASF23
	.byte	0x5
	.uleb128 0
	.4byte	.LASF24
	.byte	0x5
	.uleb128 0
	.4byte	.LASF25
	.byte	0x5
	.uleb128 0
	.4byte	.LASF26
	.byte	0x5
	.uleb128 0
	.4byte	.LASF27
	.byte	0x5
	.uleb128 0
	.4byte	.LASF28
	.byte	0x5
	.uleb128 0
	.4byte	.LASF29
	.byte	0x5
	.uleb128 0
	.4byte	.LASF30
	.byte	0x5
	.uleb128 0
	.4byte	.LASF31
	.byte	0x5
	.uleb128 0
	.4byte	.LASF32
	.byte	0x5
	.uleb128 0
	.4byte	.LASF33
	.byte	0x5
	.uleb128 0
	.4byte	.LASF34
	.byte	0x5
	.uleb128 0
	.4byte	.LASF35
	.byte	0x5
	.uleb128 0
	.4byte	.LASF36
	.byte	0x5
	.uleb128 0
	.4byte	.LASF37
	.byte	0x5
	.uleb128 0
	.4byte	.LASF38
	.byte	0x5
	.uleb128 0
	.4byte	.LASF39
	.byte	0x5
	.uleb128 0
	.4byte	.LASF40
	.byte	0x5
	.uleb128 0
	.4byte	.LASF41
	.byte	0x5
	.uleb128 0
	.4byte	.LASF42
	.byte	0x5
	.uleb128 0
	.4byte	.LASF43
	.byte	0x5
	.uleb128 0
	.4byte	.LASF44
	.byte	0x5
	.uleb128 0
	.4byte	.LASF45
	.byte	0x5
	.uleb128 0
	.4byte	.LASF46
	.byte	0x5
	.uleb128 0
	.4byte	.LASF47
	.byte	0x5
	.uleb128 0
	.4byte	.LASF48
	.byte	0x5
	.uleb128 0
	.4byte	.LASF49
	.byte	0x5
	.uleb128 0
	.4byte	.LASF50
	.byte	0x5
	.uleb128 0
	.4byte	.LASF51
	.byte	0x5
	.uleb128 0
	.4byte	.LASF52
	.byte	0x5
	.uleb128 0
	.4byte	.LASF53
	.byte	0x5
	.uleb128 0
	.4byte	.LASF54
	.byte	0x5
	.uleb128 0
	.4byte	.LASF55
	.byte	0x5
	.uleb128 0
	.4byte	.LASF56
	.byte	0x5
	.uleb128 0
	.4byte	.LASF57
	.byte	0x5
	.uleb128 0
	.4byte	.LASF58
	.byte	0x5
	.uleb128 0
	.4byte	.LASF59
	.byte	0x5
	.uleb128 0
	.4byte	.LASF60
	.byte	0x5
	.uleb128 0
	.4byte	.LASF61
	.byte	0x5
	.uleb128 0
	.4byte	.LASF62
	.byte	0x5
	.uleb128 0
	.4byte	.LASF63
	.byte	0x5
	.uleb128 0
	.4byte	.LASF64
	.byte	0x5
	.uleb128 0
	.4byte	.LASF65
	.byte	0x5
	.uleb128 0
	.4byte	.LASF66
	.byte	0x5
	.uleb128 0
	.4byte	.LASF67
	.byte	0x5
	.uleb128 0
	.4byte	.LASF68
	.byte	0x5
	.uleb128 0
	.4byte	.LASF69
	.byte	0x5
	.uleb128 0
	.4byte	.LASF70
	.byte	0x5
	.uleb128 0
	.4byte	.LASF71
	.byte	0x5
	.uleb128 0
	.4byte	.LASF72
	.byte	0x5
	.uleb128 0
	.4byte	.LASF73
	.byte	0x5
	.uleb128 0
	.4byte	.LASF74
	.byte	0x5
	.uleb128 0
	.4byte	.LASF75
	.byte	0x5
	.uleb128 0
	.4byte	.LASF76
	.byte	0x5
	.uleb128 0
	.4byte	.LASF77
	.byte	0x5
	.uleb128 0
	.4byte	.LASF78
	.byte	0x5
	.uleb128 0
	.4byte	.LASF79
	.byte	0x5
	.uleb128 0
	.4byte	.LASF80
	.byte	0x5
	.uleb128 0
	.4byte	.LASF81
	.byte	0x5
	.uleb128 0
	.4byte	.LASF82
	.byte	0x5
	.uleb128 0
	.4byte	.LASF83
	.byte	0x5
	.uleb128 0
	.4byte	.LASF84
	.byte	0x5
	.uleb128 0
	.4byte	.LASF85
	.byte	0x5
	.uleb128 0
	.4byte	.LASF86
	.byte	0x5
	.uleb128 0
	.4byte	.LASF87
	.byte	0x5
	.uleb128 0
	.4byte	.LASF88
	.byte	0x5
	.uleb128 0
	.4byte	.LASF89
	.byte	0x5
	.uleb128 0
	.4byte	.LASF90
	.byte	0x5
	.uleb128 0
	.4byte	.LASF91
	.byte	0x5
	.uleb128 0
	.4byte	.LASF92
	.byte	0x5
	.uleb128 0
	.4byte	.LASF93
	.byte	0x5
	.uleb128 0
	.4byte	.LASF94
	.byte	0x5
	.uleb128 0
	.4byte	.LASF95
	.byte	0x5
	.uleb128 0
	.4byte	.LASF96
	.byte	0x5
	.uleb128 0
	.4byte	.LASF97
	.byte	0x5
	.uleb128 0
	.4byte	.LASF98
	.byte	0x5
	.uleb128 0
	.4byte	.LASF99
	.byte	0x5
	.uleb128 0
	.4byte	.LASF100
	.byte	0x5
	.uleb128 0
	.4byte	.LASF101
	.byte	0x5
	.uleb128 0
	.4byte	.LASF102
	.byte	0x5
	.uleb128 0
	.4byte	.LASF103
	.byte	0x5
	.uleb128 0
	.4byte	.LASF104
	.byte	0x5
	.uleb128 0
	.4byte	.LASF105
	.byte	0x5
	.uleb128 0
	.4byte	.LASF106
	.byte	0x5
	.uleb128 0
	.4byte	.LASF107
	.byte	0x5
	.uleb128 0
	.4byte	.LASF108
	.byte	0x5
	.uleb128 0
	.4byte	.LASF109
	.byte	0x5
	.uleb128 0
	.4byte	.LASF110
	.byte	0x5
	.uleb128 0
	.4byte	.LASF111
	.byte	0x5
	.uleb128 0
	.4byte	.LASF112
	.byte	0x5
	.uleb128 0
	.4byte	.LASF113
	.byte	0x5
	.uleb128 0
	.4byte	.LASF114
	.byte	0x5
	.uleb128 0
	.4byte	.LASF115
	.byte	0x5
	.uleb128 0
	.4byte	.LASF116
	.byte	0x5
	.uleb128 0
	.4byte	.LASF117
	.byte	0x5
	.uleb128 0
	.4byte	.LASF118
	.byte	0x5
	.uleb128 0
	.4byte	.LASF119
	.byte	0x5
	.uleb128 0
	.4byte	.LASF120
	.byte	0x5
	.uleb128 0
	.4byte	.LASF121
	.byte	0x5
	.uleb128 0
	.4byte	.LASF122
	.byte	0x5
	.uleb128 0
	.4byte	.LASF123
	.byte	0x5
	.uleb128 0
	.4byte	.LASF124
	.byte	0x5
	.uleb128 0
	.4byte	.LASF125
	.byte	0x5
	.uleb128 0
	.4byte	.LASF126
	.byte	0x5
	.uleb128 0
	.4byte	.LASF127
	.byte	0x5
	.uleb128 0
	.4byte	.LASF128
	.byte	0x5
	.uleb128 0
	.4byte	.LASF129
	.byte	0x5
	.uleb128 0
	.4byte	.LASF130
	.byte	0x5
	.uleb128 0
	.4byte	.LASF131
	.byte	0x5
	.uleb128 0
	.4byte	.LASF132
	.byte	0x5
	.uleb128 0
	.4byte	.LASF133
	.byte	0x5
	.uleb128 0
	.4byte	.LASF134
	.byte	0x5
	.uleb128 0
	.4byte	.LASF135
	.byte	0x5
	.uleb128 0
	.4byte	.LASF136
	.byte	0x5
	.uleb128 0
	.4byte	.LASF137
	.byte	0x5
	.uleb128 0
	.4byte	.LASF138
	.byte	0x5
	.uleb128 0
	.4byte	.LASF139
	.byte	0x5
	.uleb128 0
	.4byte	.LASF140
	.byte	0x5
	.uleb128 0
	.4byte	.LASF141
	.byte	0x5
	.uleb128 0
	.4byte	.LASF142
	.byte	0x5
	.uleb128 0
	.4byte	.LASF143
	.byte	0x5
	.uleb128 0
	.4byte	.LASF144
	.byte	0x5
	.uleb128 0
	.4byte	.LASF145
	.byte	0x5
	.uleb128 0
	.4byte	.LASF146
	.byte	0x5
	.uleb128 0
	.4byte	.LASF147
	.byte	0x5
	.uleb128 0
	.4byte	.LASF148
	.byte	0x5
	.uleb128 0
	.4byte	.LASF149
	.byte	0x5
	.uleb128 0
	.4byte	.LASF150
	.byte	0x5
	.uleb128 0
	.4byte	.LASF151
	.byte	0x5
	.uleb128 0
	.4byte	.LASF152
	.byte	0x5
	.uleb128 0
	.4byte	.LASF153
	.byte	0x5
	.uleb128 0
	.4byte	.LASF154
	.byte	0x5
	.uleb128 0
	.4byte	.LASF155
	.byte	0x5
	.uleb128 0
	.4byte	.LASF156
	.byte	0x5
	.uleb128 0
	.4byte	.LASF157
	.byte	0x5
	.uleb128 0
	.4byte	.LASF158
	.byte	0x5
	.uleb128 0
	.4byte	.LASF159
	.byte	0x5
	.uleb128 0
	.4byte	.LASF160
	.byte	0x5
	.uleb128 0
	.4byte	.LASF161
	.byte	0x5
	.uleb128 0
	.4byte	.LASF162
	.byte	0x5
	.uleb128 0
	.4byte	.LASF163
	.byte	0x5
	.uleb128 0
	.4byte	.LASF164
	.byte	0x5
	.uleb128 0
	.4byte	.LASF165
	.byte	0x5
	.uleb128 0
	.4byte	.LASF166
	.byte	0x5
	.uleb128 0
	.4byte	.LASF167
	.byte	0x5
	.uleb128 0
	.4byte	.LASF168
	.byte	0x5
	.uleb128 0
	.4byte	.LASF169
	.byte	0x5
	.uleb128 0
	.4byte	.LASF170
	.byte	0x5
	.uleb128 0
	.4byte	.LASF171
	.byte	0x5
	.uleb128 0
	.4byte	.LASF172
	.byte	0x5
	.uleb128 0
	.4byte	.LASF173
	.byte	0x5
	.uleb128 0
	.4byte	.LASF174
	.byte	0x5
	.uleb128 0
	.4byte	.LASF175
	.byte	0x5
	.uleb128 0
	.4byte	.LASF176
	.byte	0x5
	.uleb128 0
	.4byte	.LASF177
	.byte	0x5
	.uleb128 0
	.4byte	.LASF178
	.byte	0x5
	.uleb128 0
	.4byte	.LASF179
	.byte	0x5
	.uleb128 0
	.4byte	.LASF180
	.byte	0x5
	.uleb128 0
	.4byte	.LASF181
	.byte	0x5
	.uleb128 0
	.4byte	.LASF182
	.byte	0x5
	.uleb128 0
	.4byte	.LASF183
	.byte	0x5
	.uleb128 0
	.4byte	.LASF184
	.byte	0x5
	.uleb128 0
	.4byte	.LASF185
	.byte	0x5
	.uleb128 0
	.4byte	.LASF186
	.byte	0x5
	.uleb128 0
	.4byte	.LASF187
	.byte	0x5
	.uleb128 0
	.4byte	.LASF188
	.byte	0x5
	.uleb128 0
	.4byte	.LASF189
	.byte	0x5
	.uleb128 0
	.4byte	.LASF190
	.byte	0x5
	.uleb128 0
	.4byte	.LASF191
	.byte	0x5
	.uleb128 0
	.4byte	.LASF192
	.byte	0x5
	.uleb128 0
	.4byte	.LASF193
	.byte	0x5
	.uleb128 0
	.4byte	.LASF194
	.byte	0x5
	.uleb128 0
	.4byte	.LASF195
	.byte	0x5
	.uleb128 0
	.4byte	.LASF196
	.byte	0x5
	.uleb128 0
	.4byte	.LASF197
	.byte	0x5
	.uleb128 0
	.4byte	.LASF198
	.byte	0x5
	.uleb128 0
	.4byte	.LASF199
	.byte	0x5
	.uleb128 0
	.4byte	.LASF200
	.byte	0x5
	.uleb128 0
	.4byte	.LASF201
	.byte	0x5
	.uleb128 0
	.4byte	.LASF202
	.byte	0x5
	.uleb128 0
	.4byte	.LASF203
	.byte	0x5
	.uleb128 0
	.4byte	.LASF204
	.byte	0x5
	.uleb128 0
	.4byte	.LASF205
	.byte	0x5
	.uleb128 0
	.4byte	.LASF206
	.byte	0x5
	.uleb128 0
	.4byte	.LASF207
	.byte	0x5
	.uleb128 0
	.4byte	.LASF208
	.byte	0x5
	.uleb128 0
	.4byte	.LASF209
	.byte	0x5
	.uleb128 0
	.4byte	.LASF210
	.byte	0x5
	.uleb128 0
	.4byte	.LASF211
	.byte	0x5
	.uleb128 0
	.4byte	.LASF212
	.byte	0x5
	.uleb128 0
	.4byte	.LASF213
	.byte	0x5
	.uleb128 0
	.4byte	.LASF214
	.byte	0x5
	.uleb128 0
	.4byte	.LASF215
	.byte	0x5
	.uleb128 0
	.4byte	.LASF216
	.byte	0x5
	.uleb128 0
	.4byte	.LASF217
	.byte	0x5
	.uleb128 0
	.4byte	.LASF218
	.byte	0x5
	.uleb128 0
	.4byte	.LASF219
	.byte	0x5
	.uleb128 0
	.4byte	.LASF220
	.byte	0x5
	.uleb128 0
	.4byte	.LASF221
	.byte	0x5
	.uleb128 0
	.4byte	.LASF222
	.byte	0x5
	.uleb128 0
	.4byte	.LASF223
	.byte	0x5
	.uleb128 0
	.4byte	.LASF224
	.byte	0x5
	.uleb128 0
	.4byte	.LASF225
	.byte	0x5
	.uleb128 0
	.4byte	.LASF226
	.byte	0x5
	.uleb128 0
	.4byte	.LASF227
	.byte	0x5
	.uleb128 0
	.4byte	.LASF228
	.byte	0x5
	.uleb128 0
	.4byte	.LASF229
	.byte	0x5
	.uleb128 0
	.4byte	.LASF230
	.byte	0x5
	.uleb128 0
	.4byte	.LASF231
	.byte	0x5
	.uleb128 0
	.4byte	.LASF232
	.byte	0x5
	.uleb128 0
	.4byte	.LASF233
	.byte	0x5
	.uleb128 0
	.4byte	.LASF234
	.byte	0x5
	.uleb128 0
	.4byte	.LASF235
	.byte	0x5
	.uleb128 0
	.4byte	.LASF236
	.byte	0x5
	.uleb128 0
	.4byte	.LASF237
	.byte	0x5
	.uleb128 0
	.4byte	.LASF238
	.byte	0x5
	.uleb128 0
	.4byte	.LASF239
	.byte	0x5
	.uleb128 0
	.4byte	.LASF240
	.byte	0x5
	.uleb128 0
	.4byte	.LASF241
	.byte	0x5
	.uleb128 0
	.4byte	.LASF242
	.byte	0x5
	.uleb128 0
	.4byte	.LASF243
	.byte	0x5
	.uleb128 0
	.4byte	.LASF244
	.byte	0x5
	.uleb128 0
	.4byte	.LASF245
	.byte	0x5
	.uleb128 0
	.4byte	.LASF246
	.byte	0x5
	.uleb128 0
	.4byte	.LASF247
	.byte	0x5
	.uleb128 0
	.4byte	.LASF248
	.byte	0x5
	.uleb128 0
	.4byte	.LASF249
	.byte	0x5
	.uleb128 0
	.4byte	.LASF250
	.byte	0x5
	.uleb128 0
	.4byte	.LASF251
	.byte	0x5
	.uleb128 0
	.4byte	.LASF252
	.byte	0x5
	.uleb128 0
	.4byte	.LASF253
	.byte	0x5
	.uleb128 0
	.4byte	.LASF254
	.byte	0x5
	.uleb128 0
	.4byte	.LASF255
	.byte	0x5
	.uleb128 0
	.4byte	.LASF256
	.byte	0x5
	.uleb128 0
	.4byte	.LASF257
	.byte	0x5
	.uleb128 0
	.4byte	.LASF258
	.byte	0x5
	.uleb128 0
	.4byte	.LASF259
	.byte	0x5
	.uleb128 0
	.4byte	.LASF260
	.byte	0x5
	.uleb128 0
	.4byte	.LASF261
	.byte	0x5
	.uleb128 0
	.4byte	.LASF262
	.byte	0x5
	.uleb128 0
	.4byte	.LASF263
	.byte	0x5
	.uleb128 0
	.4byte	.LASF264
	.byte	0x5
	.uleb128 0
	.4byte	.LASF265
	.byte	0x5
	.uleb128 0
	.4byte	.LASF266
	.byte	0x5
	.uleb128 0
	.4byte	.LASF267
	.byte	0x5
	.uleb128 0
	.4byte	.LASF268
	.byte	0x5
	.uleb128 0
	.4byte	.LASF269
	.byte	0x5
	.uleb128 0
	.4byte	.LASF270
	.byte	0x5
	.uleb128 0
	.4byte	.LASF271
	.byte	0x5
	.uleb128 0
	.4byte	.LASF272
	.byte	0x5
	.uleb128 0
	.4byte	.LASF273
	.byte	0x5
	.uleb128 0
	.4byte	.LASF274
	.byte	0x5
	.uleb128 0
	.4byte	.LASF275
	.byte	0x5
	.uleb128 0
	.4byte	.LASF276
	.byte	0x5
	.uleb128 0
	.4byte	.LASF277
	.byte	0x5
	.uleb128 0
	.4byte	.LASF278
	.byte	0x5
	.uleb128 0
	.4byte	.LASF279
	.byte	0x5
	.uleb128 0
	.4byte	.LASF280
	.byte	0x5
	.uleb128 0
	.4byte	.LASF281
	.byte	0x5
	.uleb128 0
	.4byte	.LASF282
	.byte	0x5
	.uleb128 0
	.4byte	.LASF283
	.byte	0x5
	.uleb128 0
	.4byte	.LASF284
	.byte	0x5
	.uleb128 0
	.4byte	.LASF285
	.byte	0x5
	.uleb128 0
	.4byte	.LASF286
	.byte	0x5
	.uleb128 0
	.4byte	.LASF287
	.byte	0x5
	.uleb128 0
	.4byte	.LASF288
	.byte	0x5
	.uleb128 0
	.4byte	.LASF289
	.byte	0x5
	.uleb128 0
	.4byte	.LASF290
	.byte	0x5
	.uleb128 0
	.4byte	.LASF291
	.byte	0x5
	.uleb128 0
	.4byte	.LASF292
	.byte	0x5
	.uleb128 0
	.4byte	.LASF293
	.byte	0x5
	.uleb128 0
	.4byte	.LASF294
	.byte	0x5
	.uleb128 0
	.4byte	.LASF295
	.byte	0x5
	.uleb128 0
	.4byte	.LASF296
	.byte	0x5
	.uleb128 0
	.4byte	.LASF297
	.byte	0x5
	.uleb128 0
	.4byte	.LASF298
	.byte	0x5
	.uleb128 0
	.4byte	.LASF299
	.byte	0x5
	.uleb128 0
	.4byte	.LASF300
	.byte	0x5
	.uleb128 0
	.4byte	.LASF301
	.byte	0x5
	.uleb128 0
	.4byte	.LASF302
	.byte	0x5
	.uleb128 0
	.4byte	.LASF303
	.byte	0x5
	.uleb128 0
	.4byte	.LASF304
	.byte	0x5
	.uleb128 0
	.4byte	.LASF305
	.byte	0x5
	.uleb128 0
	.4byte	.LASF306
	.byte	0x5
	.uleb128 0
	.4byte	.LASF307
	.byte	0x5
	.uleb128 0
	.4byte	.LASF308
	.byte	0x5
	.uleb128 0
	.4byte	.LASF309
	.byte	0x5
	.uleb128 0
	.4byte	.LASF310
	.byte	0x5
	.uleb128 0
	.4byte	.LASF311
	.byte	0x5
	.uleb128 0
	.4byte	.LASF312
	.byte	0x5
	.uleb128 0
	.4byte	.LASF313
	.byte	0x5
	.uleb128 0
	.4byte	.LASF314
	.byte	0x5
	.uleb128 0
	.4byte	.LASF315
	.byte	0x5
	.uleb128 0
	.4byte	.LASF316
	.byte	0x5
	.uleb128 0
	.4byte	.LASF317
	.byte	0x5
	.uleb128 0
	.4byte	.LASF318
	.byte	0x5
	.uleb128 0
	.4byte	.LASF319
	.byte	0x5
	.uleb128 0
	.4byte	.LASF320
	.byte	0x5
	.uleb128 0
	.4byte	.LASF321
	.byte	0x5
	.uleb128 0
	.4byte	.LASF322
	.byte	0x5
	.uleb128 0
	.4byte	.LASF323
	.byte	0x5
	.uleb128 0
	.4byte	.LASF324
	.byte	0x5
	.uleb128 0
	.4byte	.LASF325
	.byte	0x5
	.uleb128 0
	.4byte	.LASF326
	.byte	0x5
	.uleb128 0
	.4byte	.LASF327
	.byte	0x5
	.uleb128 0
	.4byte	.LASF328
	.byte	0x5
	.uleb128 0
	.4byte	.LASF329
	.byte	0x5
	.uleb128 0
	.4byte	.LASF330
	.byte	0x5
	.uleb128 0
	.4byte	.LASF331
	.byte	0x5
	.uleb128 0
	.4byte	.LASF332
	.byte	0x5
	.uleb128 0
	.4byte	.LASF333
	.byte	0x5
	.uleb128 0
	.4byte	.LASF334
	.byte	0x5
	.uleb128 0
	.4byte	.LASF335
	.byte	0x5
	.uleb128 0
	.4byte	.LASF336
	.byte	0x5
	.uleb128 0
	.4byte	.LASF337
	.byte	0x5
	.uleb128 0
	.4byte	.LASF338
	.byte	0x5
	.uleb128 0
	.4byte	.LASF339
	.byte	0x5
	.uleb128 0
	.4byte	.LASF340
	.byte	0x5
	.uleb128 0
	.4byte	.LASF341
	.byte	0x5
	.uleb128 0
	.4byte	.LASF342
	.byte	0x5
	.uleb128 0
	.4byte	.LASF343
	.byte	0x5
	.uleb128 0
	.4byte	.LASF344
	.byte	0x5
	.uleb128 0
	.4byte	.LASF345
	.byte	0x5
	.uleb128 0
	.4byte	.LASF346
	.byte	0x5
	.uleb128 0
	.4byte	.LASF347
	.byte	0x5
	.uleb128 0
	.4byte	.LASF348
	.byte	0x5
	.uleb128 0
	.4byte	.LASF349
	.byte	0x5
	.uleb128 0
	.4byte	.LASF350
	.byte	0x5
	.uleb128 0
	.4byte	.LASF351
	.byte	0x5
	.uleb128 0
	.4byte	.LASF352
	.byte	0x5
	.uleb128 0
	.4byte	.LASF353
	.byte	0x5
	.uleb128 0
	.4byte	.LASF354
	.byte	0x5
	.uleb128 0
	.4byte	.LASF355
	.byte	0x5
	.uleb128 0
	.4byte	.LASF356
	.byte	0x5
	.uleb128 0
	.4byte	.LASF357
	.byte	0x5
	.uleb128 0
	.4byte	.LASF358
	.byte	0x5
	.uleb128 0
	.4byte	.LASF359
	.byte	0x5
	.uleb128 0
	.4byte	.LASF360
	.byte	0x5
	.uleb128 0
	.4byte	.LASF361
	.byte	0x5
	.uleb128 0
	.4byte	.LASF362
	.byte	0x5
	.uleb128 0
	.4byte	.LASF363
	.byte	0x5
	.uleb128 0
	.4byte	.LASF364
	.byte	0x5
	.uleb128 0
	.4byte	.LASF365
	.byte	0x5
	.uleb128 0
	.4byte	.LASF366
	.byte	0x5
	.uleb128 0
	.4byte	.LASF367
	.byte	0x5
	.uleb128 0
	.4byte	.LASF368
	.byte	0x5
	.uleb128 0
	.4byte	.LASF369
	.byte	0x5
	.uleb128 0
	.4byte	.LASF370
	.byte	0x5
	.uleb128 0
	.4byte	.LASF371
	.byte	0x5
	.uleb128 0
	.4byte	.LASF372
	.byte	0x5
	.uleb128 0
	.4byte	.LASF373
	.byte	0x5
	.uleb128 0
	.4byte	.LASF374
	.byte	0x5
	.uleb128 0
	.4byte	.LASF375
	.byte	0x5
	.uleb128 0
	.4byte	.LASF376
	.byte	0x5
	.uleb128 0
	.4byte	.LASF377
	.byte	0x5
	.uleb128 0
	.4byte	.LASF378
	.byte	0x5
	.uleb128 0
	.4byte	.LASF379
	.byte	0x5
	.uleb128 0
	.4byte	.LASF380
	.byte	0x5
	.uleb128 0
	.4byte	.LASF381
	.byte	0x5
	.uleb128 0
	.4byte	.LASF382
	.byte	0x5
	.uleb128 0
	.4byte	.LASF383
	.byte	0x5
	.uleb128 0
	.4byte	.LASF384
	.byte	0x5
	.uleb128 0
	.4byte	.LASF385
	.byte	0x5
	.uleb128 0
	.4byte	.LASF386
	.byte	0x5
	.uleb128 0
	.4byte	.LASF387
	.byte	0x5
	.uleb128 0
	.4byte	.LASF388
	.byte	0x5
	.uleb128 0
	.4byte	.LASF389
	.byte	0x5
	.uleb128 0
	.4byte	.LASF390
	.byte	0x5
	.uleb128 0
	.4byte	.LASF391
	.byte	0x5
	.uleb128 0
	.4byte	.LASF392
	.byte	0x5
	.uleb128 0
	.4byte	.LASF393
	.byte	0x5
	.uleb128 0
	.4byte	.LASF394
	.byte	0x5
	.uleb128 0
	.4byte	.LASF395
	.byte	0x5
	.uleb128 0
	.4byte	.LASF396
	.byte	0x5
	.uleb128 0
	.4byte	.LASF397
	.byte	0x5
	.uleb128 0
	.4byte	.LASF398
	.byte	0x5
	.uleb128 0
	.4byte	.LASF399
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.__SEGGER_RTL_ConfDefaults.h.11.37ef32ceb52ba65a5b7e8947ee56f76c,comdat
.Ldebug_macro3:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0xb
	.4byte	.LASF403
	.byte	0x5
	.uleb128 0x17
	.4byte	.LASF404
	.byte	0x5
	.uleb128 0x2a
	.4byte	.LASF405
	.byte	0x5
	.uleb128 0x2b
	.4byte	.LASF406
	.byte	0x5
	.uleb128 0x2c
	.4byte	.LASF407
	.byte	0x5
	.uleb128 0x2d
	.4byte	.LASF408
	.byte	0x5
	.uleb128 0x2e
	.4byte	.LASF409
	.byte	0x5
	.uleb128 0x2f
	.4byte	.LASF410
	.byte	0x5
	.uleb128 0x30
	.4byte	.LASF411
	.byte	0x5
	.uleb128 0x31
	.4byte	.LASF412
	.byte	0x5
	.uleb128 0x32
	.4byte	.LASF413
	.byte	0x5
	.uleb128 0x36
	.4byte	.LASF414
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.__SEGGER_RTL_RISCV_Conf.h.11.e2b6ba0bf0784e8826ddf0df0b4d727b,comdat
.Ldebug_macro4:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0xb
	.4byte	.LASF416
	.byte	0x5
	.uleb128 0x2a
	.4byte	.LASF417
	.byte	0x5
	.uleb128 0x2b
	.4byte	.LASF418
	.byte	0x5
	.uleb128 0x2c
	.4byte	.LASF419
	.byte	0x5
	.uleb128 0x2d
	.4byte	.LASF420
	.byte	0x5
	.uleb128 0x2e
	.4byte	.LASF421
	.byte	0x5
	.uleb128 0x2f
	.4byte	.LASF422
	.byte	0x5
	.uleb128 0x30
	.4byte	.LASF423
	.byte	0x5
	.uleb128 0x31
	.4byte	.LASF424
	.byte	0x5
	.uleb128 0x32
	.4byte	.LASF425
	.byte	0x5
	.uleb128 0x33
	.4byte	.LASF426
	.byte	0x5
	.uleb128 0x40
	.4byte	.LASF427
	.byte	0x5
	.uleb128 0x4a
	.4byte	.LASF428
	.byte	0x5
	.uleb128 0x51
	.4byte	.LASF429
	.byte	0x5
	.uleb128 0x60
	.4byte	.LASF430
	.byte	0x5
	.uleb128 0x66
	.4byte	.LASF431
	.byte	0x5
	.uleb128 0x6c
	.4byte	.LASF432
	.byte	0x5
	.uleb128 0x70
	.4byte	.LASF433
	.byte	0x5
	.uleb128 0x76
	.4byte	.LASF434
	.byte	0x5
	.uleb128 0x7e
	.4byte	.LASF435
	.byte	0x5
	.uleb128 0x84
	.4byte	.LASF436
	.byte	0x5
	.uleb128 0x8a
	.4byte	.LASF437
	.byte	0x5
	.uleb128 0x90
	.4byte	.LASF438
	.byte	0x5
	.uleb128 0x96
	.4byte	.LASF439
	.byte	0x5
	.uleb128 0x9a
	.4byte	.LASF440
	.byte	0x5
	.uleb128 0x9e
	.4byte	.LASF441
	.byte	0x5
	.uleb128 0xac
	.4byte	.LASF442
	.byte	0x5
	.uleb128 0xb4
	.4byte	.LASF443
	.byte	0x5
	.uleb128 0xbd
	.4byte	.LASF444
	.byte	0x5
	.uleb128 0xc2
	.4byte	.LASF445
	.byte	0x5
	.uleb128 0xd4
	.4byte	.LASF446
	.byte	0x5
	.uleb128 0xd8
	.4byte	.LASF447
	.byte	0x5
	.uleb128 0xdf
	.4byte	.LASF448
	.byte	0x5
	.uleb128 0xe5
	.4byte	.LASF449
	.byte	0x5
	.uleb128 0xf6
	.4byte	.LASF450
	.byte	0x5
	.uleb128 0xfd
	.4byte	.LASF451
	.byte	0x5
	.uleb128 0x104
	.4byte	.LASF452
	.byte	0x5
	.uleb128 0x10b
	.4byte	.LASF453
	.byte	0x5
	.uleb128 0x115
	.4byte	.LASF454
	.byte	0x5
	.uleb128 0x11b
	.4byte	.LASF455
	.byte	0x5
	.uleb128 0x12c
	.4byte	.LASF456
	.byte	0x5
	.uleb128 0x137
	.4byte	.LASF457
	.byte	0x5
	.uleb128 0x142
	.4byte	.LASF458
	.byte	0x5
	.uleb128 0x147
	.4byte	.LASF459
	.byte	0x5
	.uleb128 0x14d
	.4byte	.LASF460
	.byte	0x5
	.uleb128 0x152
	.4byte	.LASF461
	.byte	0x5
	.uleb128 0x157
	.4byte	.LASF462
	.byte	0x5
	.uleb128 0x161
	.4byte	.LASF463
	.byte	0x5
	.uleb128 0x166
	.4byte	.LASF464
	.byte	0x5
	.uleb128 0x16b
	.4byte	.LASF465
	.byte	0x5
	.uleb128 0x17c
	.4byte	.LASF466
	.byte	0x5
	.uleb128 0x186
	.4byte	.LASF467
	.byte	0x5
	.uleb128 0x189
	.4byte	.LASF468
	.byte	0x5
	.uleb128 0x18a
	.4byte	.LASF469
	.byte	0x5
	.uleb128 0x191
	.4byte	.LASF470
	.byte	0x5
	.uleb128 0x192
	.4byte	.LASF471
	.byte	0x5
	.uleb128 0x198
	.4byte	.LASF472
	.byte	0x5
	.uleb128 0x19f
	.4byte	.LASF473
	.byte	0x5
	.uleb128 0x1a6
	.4byte	.LASF474
	.byte	0x5
	.uleb128 0x265
	.4byte	.LASF475
	.byte	0x5
	.uleb128 0x268
	.4byte	.LASF476
	.byte	0x5
	.uleb128 0x26b
	.4byte	.LASF477
	.byte	0x5
	.uleb128 0x273
	.4byte	.LASF478
	.byte	0x5
	.uleb128 0x280
	.4byte	.LASF479
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.__SEGGER_RTL_ConfDefaults.h.76.6803efa605c1ddad38308e1ef01f50dd,comdat
.Ldebug_macro5:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0x4c
	.4byte	.LASF480
	.byte	0x5
	.uleb128 0x4d
	.4byte	.LASF481
	.byte	0x5
	.uleb128 0x4e
	.4byte	.LASF482
	.byte	0x5
	.uleb128 0x50
	.4byte	.LASF483
	.byte	0x5
	.uleb128 0x51
	.4byte	.LASF484
	.byte	0x5
	.uleb128 0x52
	.4byte	.LASF485
	.byte	0x5
	.uleb128 0x54
	.4byte	.LASF486
	.byte	0x5
	.uleb128 0x55
	.4byte	.LASF487
	.byte	0x5
	.uleb128 0x56
	.4byte	.LASF488
	.byte	0x5
	.uleb128 0x63
	.4byte	.LASF489
	.byte	0x5
	.uleb128 0x67
	.4byte	.LASF490
	.byte	0x5
	.uleb128 0x6b
	.4byte	.LASF491
	.byte	0x5
	.uleb128 0x77
	.4byte	.LASF492
	.byte	0x5
	.uleb128 0x7b
	.4byte	.LASF493
	.byte	0x5
	.uleb128 0x7f
	.4byte	.LASF494
	.byte	0x5
	.uleb128 0x87
	.4byte	.LASF495
	.byte	0x5
	.uleb128 0x96
	.4byte	.LASF496
	.byte	0x5
	.uleb128 0x9d
	.4byte	.LASF497
	.byte	0x5
	.uleb128 0xa4
	.4byte	.LASF498
	.byte	0x5
	.uleb128 0xb5
	.4byte	.LASF499
	.byte	0x5
	.uleb128 0xba
	.4byte	.LASF500
	.byte	0x5
	.uleb128 0xbd
	.4byte	.LASF501
	.byte	0x5
	.uleb128 0xc2
	.4byte	.LASF502
	.byte	0x5
	.uleb128 0xc5
	.4byte	.LASF503
	.byte	0x5
	.uleb128 0xca
	.4byte	.LASF504
	.byte	0x5
	.uleb128 0xcd
	.4byte	.LASF505
	.byte	0x5
	.uleb128 0xd2
	.4byte	.LASF506
	.byte	0x5
	.uleb128 0xd5
	.4byte	.LASF507
	.byte	0x5
	.uleb128 0xda
	.4byte	.LASF508
	.byte	0x5
	.uleb128 0xdb
	.4byte	.LASF509
	.byte	0x5
	.uleb128 0xde
	.4byte	.LASF510
	.byte	0x5
	.uleb128 0xe3
	.4byte	.LASF511
	.byte	0x5
	.uleb128 0xe4
	.4byte	.LASF512
	.byte	0x5
	.uleb128 0xe7
	.4byte	.LASF513
	.byte	0x5
	.uleb128 0xec
	.4byte	.LASF514
	.byte	0x5
	.uleb128 0xed
	.4byte	.LASF515
	.byte	0x5
	.uleb128 0xf0
	.4byte	.LASF516
	.byte	0x5
	.uleb128 0xf5
	.4byte	.LASF517
	.byte	0x5
	.uleb128 0xf6
	.4byte	.LASF518
	.byte	0x5
	.uleb128 0xfc
	.4byte	.LASF519
	.byte	0x5
	.uleb128 0x103
	.4byte	.LASF520
	.byte	0x5
	.uleb128 0x10a
	.4byte	.LASF521
	.byte	0x5
	.uleb128 0x111
	.4byte	.LASF522
	.byte	0x5
	.uleb128 0x118
	.4byte	.LASF523
	.byte	0x5
	.uleb128 0x11f
	.4byte	.LASF524
	.byte	0x5
	.uleb128 0x126
	.4byte	.LASF525
	.byte	0x5
	.uleb128 0x12d
	.4byte	.LASF526
	.byte	0x5
	.uleb128 0x136
	.4byte	.LASF527
	.byte	0x5
	.uleb128 0x13d
	.4byte	.LASF528
	.byte	0x5
	.uleb128 0x144
	.4byte	.LASF529
	.byte	0x5
	.uleb128 0x14b
	.4byte	.LASF530
	.byte	0x5
	.uleb128 0x152
	.4byte	.LASF531
	.byte	0x5
	.uleb128 0x159
	.4byte	.LASF532
	.byte	0x5
	.uleb128 0x160
	.4byte	.LASF533
	.byte	0x5
	.uleb128 0x167
	.4byte	.LASF534
	.byte	0x5
	.uleb128 0x170
	.4byte	.LASF535
	.byte	0x5
	.uleb128 0x176
	.4byte	.LASF536
	.byte	0x5
	.uleb128 0x17f
	.4byte	.LASF537
	.byte	0x5
	.uleb128 0x186
	.4byte	.LASF538
	.byte	0x5
	.uleb128 0x18a
	.4byte	.LASF539
	.byte	0x5
	.uleb128 0x18e
	.4byte	.LASF540
	.byte	0x5
	.uleb128 0x192
	.4byte	.LASF541
	.byte	0x5
	.uleb128 0x199
	.4byte	.LASF542
	.byte	0x5
	.uleb128 0x1a0
	.4byte	.LASF543
	.byte	0x5
	.uleb128 0x1a7
	.4byte	.LASF544
	.byte	0x5
	.uleb128 0x1c8
	.4byte	.LASF545
	.byte	0x5
	.uleb128 0x1cb
	.4byte	.LASF546
	.byte	0x5
	.uleb128 0x1ce
	.4byte	.LASF547
	.byte	0x5
	.uleb128 0x1d2
	.4byte	.LASF548
	.byte	0x5
	.uleb128 0x1d5
	.4byte	.LASF549
	.byte	0x5
	.uleb128 0x1d8
	.4byte	.LASF550
	.byte	0x5
	.uleb128 0x1dc
	.4byte	.LASF551
	.byte	0x5
	.uleb128 0x1df
	.4byte	.LASF552
	.byte	0x5
	.uleb128 0x1e2
	.4byte	.LASF553
	.byte	0x5
	.uleb128 0x1e6
	.4byte	.LASF554
	.byte	0x5
	.uleb128 0x1e9
	.4byte	.LASF555
	.byte	0x5
	.uleb128 0x1ec
	.4byte	.LASF556
	.byte	0x5
	.uleb128 0x1ff
	.4byte	.LASF557
	.byte	0x5
	.uleb128 0x205
	.4byte	.LASF558
	.byte	0x5
	.uleb128 0x208
	.4byte	.LASF559
	.byte	0x5
	.uleb128 0x20f
	.4byte	.LASF560
	.byte	0x5
	.uleb128 0x215
	.4byte	.LASF561
	.byte	0x5
	.uleb128 0x218
	.4byte	.LASF562
	.byte	0x5
	.uleb128 0x21f
	.4byte	.LASF563
	.byte	0x5
	.uleb128 0x225
	.4byte	.LASF564
	.byte	0x5
	.uleb128 0x228
	.4byte	.LASF565
	.byte	0x5
	.uleb128 0x22f
	.4byte	.LASF566
	.byte	0x5
	.uleb128 0x235
	.4byte	.LASF567
	.byte	0x5
	.uleb128 0x238
	.4byte	.LASF568
	.byte	0x5
	.uleb128 0x254
	.4byte	.LASF569
	.byte	0x5
	.uleb128 0x25a
	.4byte	.LASF570
	.byte	0x5
	.uleb128 0x25d
	.4byte	.LASF571
	.byte	0x5
	.uleb128 0x264
	.4byte	.LASF572
	.byte	0x5
	.uleb128 0x26a
	.4byte	.LASF573
	.byte	0x5
	.uleb128 0x26d
	.4byte	.LASF574
	.byte	0x5
	.uleb128 0x274
	.4byte	.LASF575
	.byte	0x5
	.uleb128 0x27a
	.4byte	.LASF576
	.byte	0x5
	.uleb128 0x27d
	.4byte	.LASF577
	.byte	0x5
	.uleb128 0x284
	.4byte	.LASF578
	.byte	0x5
	.uleb128 0x28a
	.4byte	.LASF579
	.byte	0x5
	.uleb128 0x28d
	.4byte	.LASF580
	.byte	0x5
	.uleb128 0x2a9
	.4byte	.LASF581
	.byte	0x5
	.uleb128 0x2af
	.4byte	.LASF582
	.byte	0x5
	.uleb128 0x2b2
	.4byte	.LASF583
	.byte	0x5
	.uleb128 0x2bc
	.4byte	.LASF584
	.byte	0x5
	.uleb128 0x2c3
	.4byte	.LASF585
	.byte	0x5
	.uleb128 0x2c6
	.4byte	.LASF586
	.byte	0x5
	.uleb128 0x2cd
	.4byte	.LASF587
	.byte	0x5
	.uleb128 0x2d6
	.4byte	.LASF588
	.byte	0x5
	.uleb128 0x2e0
	.4byte	.LASF589
	.byte	0x5
	.uleb128 0x2e6
	.4byte	.LASF590
	.byte	0x5
	.uleb128 0x2ea
	.4byte	.LASF591
	.byte	0x5
	.uleb128 0x2f0
	.4byte	.LASF592
	.byte	0x5
	.uleb128 0x2f4
	.4byte	.LASF593
	.byte	0x5
	.uleb128 0x2fb
	.4byte	.LASF594
	.byte	0x5
	.uleb128 0x301
	.4byte	.LASF595
	.byte	0x5
	.uleb128 0x307
	.4byte	.LASF596
	.byte	0x5
	.uleb128 0x30c
	.4byte	.LASF597
	.byte	0x5
	.uleb128 0x312
	.4byte	.LASF598
	.byte	0x5
	.uleb128 0x318
	.4byte	.LASF599
	.byte	0x5
	.uleb128 0x31e
	.4byte	.LASF600
	.byte	0x5
	.uleb128 0x327
	.4byte	.LASF601
	.byte	0x5
	.uleb128 0x333
	.4byte	.LASF602
	.byte	0x5
	.uleb128 0x33a
	.4byte	.LASF603
	.byte	0x5
	.uleb128 0x341
	.4byte	.LASF604
	.byte	0x5
	.uleb128 0x348
	.4byte	.LASF605
	.byte	0x5
	.uleb128 0x34f
	.4byte	.LASF606
	.byte	0x5
	.uleb128 0x356
	.4byte	.LASF607
	.byte	0x5
	.uleb128 0x35d
	.4byte	.LASF608
	.byte	0x5
	.uleb128 0x364
	.4byte	.LASF609
	.byte	0x5
	.uleb128 0x36b
	.4byte	.LASF610
	.byte	0x5
	.uleb128 0x372
	.4byte	.LASF611
	.byte	0x5
	.uleb128 0x376
	.4byte	.LASF612
	.byte	0x5
	.uleb128 0x37d
	.4byte	.LASF613
	.byte	0x5
	.uleb128 0x384
	.4byte	.LASF614
	.byte	0x5
	.uleb128 0x38b
	.4byte	.LASF615
	.byte	0x5
	.uleb128 0x395
	.4byte	.LASF616
	.byte	0x5
	.uleb128 0x399
	.4byte	.LASF617
	.byte	0x5
	.uleb128 0x3a0
	.4byte	.LASF618
	.byte	0x5
	.uleb128 0x3a7
	.4byte	.LASF619
	.byte	0x5
	.uleb128 0x3ae
	.4byte	.LASF620
	.byte	0x5
	.uleb128 0x3b8
	.4byte	.LASF621
	.byte	0x5
	.uleb128 0x3bc
	.4byte	.LASF622
	.byte	0x5
	.uleb128 0x3c3
	.4byte	.LASF623
	.byte	0x5
	.uleb128 0x3ca
	.4byte	.LASF624
	.byte	0x5
	.uleb128 0x3d1
	.4byte	.LASF625
	.byte	0x5
	.uleb128 0x3db
	.4byte	.LASF626
	.byte	0x5
	.uleb128 0x3df
	.4byte	.LASF627
	.byte	0x5
	.uleb128 0x3e6
	.4byte	.LASF628
	.byte	0x5
	.uleb128 0x3ed
	.4byte	.LASF629
	.byte	0x5
	.uleb128 0x3f4
	.4byte	.LASF630
	.byte	0x5
	.uleb128 0x3fe
	.4byte	.LASF631
	.byte	0x5
	.uleb128 0x402
	.4byte	.LASF632
	.byte	0x5
	.uleb128 0x406
	.4byte	.LASF633
	.byte	0x5
	.uleb128 0x40a
	.4byte	.LASF634
	.byte	0x5
	.uleb128 0x40e
	.4byte	.LASF635
	.byte	0x5
	.uleb128 0x412
	.4byte	.LASF636
	.byte	0x5
	.uleb128 0x416
	.4byte	.LASF637
	.byte	0x5
	.uleb128 0x41a
	.4byte	.LASF638
	.byte	0x5
	.uleb128 0x41e
	.4byte	.LASF639
	.byte	0x5
	.uleb128 0x424
	.4byte	.LASF640
	.byte	0x5
	.uleb128 0x425
	.4byte	.LASF641
	.byte	0x5
	.uleb128 0x426
	.4byte	.LASF642
	.byte	0x5
	.uleb128 0x42b
	.4byte	.LASF643
	.byte	0x5
	.uleb128 0x42f
	.4byte	.LASF644
	.byte	0x5
	.uleb128 0x437
	.4byte	.LASF645
	.byte	0x5
	.uleb128 0x43d
	.4byte	.LASF646
	.byte	0x5
	.uleb128 0x443
	.4byte	.LASF647
	.byte	0x5
	.uleb128 0x449
	.4byte	.LASF648
	.byte	0x5
	.uleb128 0x44f
	.4byte	.LASF649
	.byte	0x5
	.uleb128 0x455
	.4byte	.LASF650
	.byte	0x5
	.uleb128 0x469
	.4byte	.LASF651
	.byte	0x5
	.uleb128 0x46f
	.4byte	.LASF652
	.byte	0x5
	.uleb128 0x475
	.4byte	.LASF653
	.byte	0x5
	.uleb128 0x478
	.4byte	.LASF654
	.byte	0x5
	.uleb128 0x47e
	.4byte	.LASF655
	.byte	0x5
	.uleb128 0x48e
	.4byte	.LASF656
	.byte	0x5
	.uleb128 0x492
	.4byte	.LASF657
	.byte	0x5
	.uleb128 0x496
	.4byte	.LASF658
	.byte	0x5
	.uleb128 0x49a
	.4byte	.LASF659
	.byte	0x5
	.uleb128 0x49e
	.4byte	.LASF660
	.byte	0x5
	.uleb128 0x4a2
	.4byte	.LASF661
	.byte	0x6
	.uleb128 0x4a6
	.4byte	.LASF662
	.byte	0x5
	.uleb128 0x4a7
	.4byte	.LASF657
	.byte	0x6
	.uleb128 0x4a8
	.4byte	.LASF663
	.byte	0x5
	.uleb128 0x4a9
	.4byte	.LASF660
	.byte	0x5
	.uleb128 0x4ad
	.4byte	.LASF664
	.byte	0x5
	.uleb128 0x4b1
	.4byte	.LASF665
	.byte	0x5
	.uleb128 0x4b5
	.4byte	.LASF666
	.byte	0x5
	.uleb128 0x4b9
	.4byte	.LASF667
	.byte	0x5
	.uleb128 0x4c1
	.4byte	.LASF668
	.byte	0x5
	.uleb128 0x4c5
	.4byte	.LASF669
	.byte	0x5
	.uleb128 0x4d1
	.4byte	.LASF670
	.byte	0x5
	.uleb128 0x4d5
	.4byte	.LASF671
	.byte	0x5
	.uleb128 0x4d9
	.4byte	.LASF672
	.byte	0x5
	.uleb128 0x4e3
	.4byte	.LASF673
	.byte	0x5
	.uleb128 0x5c5
	.4byte	.LASF674
	.byte	0x5
	.uleb128 0x5cd
	.4byte	.LASF675
	.byte	0x5
	.uleb128 0x5d5
	.4byte	.LASF676
	.byte	0x5
	.uleb128 0x5dd
	.4byte	.LASF677
	.byte	0x5
	.uleb128 0x637
	.4byte	.LASF678
	.byte	0x5
	.uleb128 0x63b
	.4byte	.LASF679
	.byte	0x5
	.uleb128 0x63f
	.4byte	.LASF680
	.byte	0x5
	.uleb128 0x643
	.4byte	.LASF681
	.byte	0x5
	.uleb128 0x64b
	.4byte	.LASF682
	.byte	0x5
	.uleb128 0x64c
	.4byte	.LASF683
	.byte	0x5
	.uleb128 0x650
	.4byte	.LASF684
	.byte	0x5
	.uleb128 0x651
	.4byte	.LASF685
	.byte	0x5
	.uleb128 0x655
	.4byte	.LASF686
	.byte	0x5
	.uleb128 0x659
	.4byte	.LASF687
	.byte	0x5
	.uleb128 0x65d
	.4byte	.LASF688
	.byte	0x5
	.uleb128 0x661
	.4byte	.LASF689
	.byte	0x5
	.uleb128 0x664
	.4byte	.LASF690
	.byte	0x5
	.uleb128 0x667
	.4byte	.LASF691
	.byte	0x5
	.uleb128 0x66a
	.4byte	.LASF692
	.byte	0x5
	.uleb128 0x66d
	.4byte	.LASF693
	.byte	0x5
	.uleb128 0x671
	.4byte	.LASF694
	.byte	0x5
	.uleb128 0x685
	.4byte	.LASF695
	.byte	0x5
	.uleb128 0x689
	.4byte	.LASF696
	.byte	0x5
	.uleb128 0x68d
	.4byte	.LASF697
	.byte	0x5
	.uleb128 0x691
	.4byte	.LASF698
	.byte	0x5
	.uleb128 0x695
	.4byte	.LASF699
	.byte	0x5
	.uleb128 0x6a5
	.4byte	.LASF700
	.byte	0x5
	.uleb128 0x6aa
	.4byte	.LASF701
	.byte	0x5
	.uleb128 0x6b1
	.4byte	.LASF702
	.byte	0x5
	.uleb128 0x6b2
	.4byte	.LASF703
	.byte	0x5
	.uleb128 0x6bd
	.4byte	.LASF704
	.byte	0x5
	.uleb128 0x6c1
	.4byte	.LASF705
	.byte	0x5
	.uleb128 0x6c5
	.4byte	.LASF706
	.byte	0x5
	.uleb128 0x6c9
	.4byte	.LASF707
	.byte	0x5
	.uleb128 0x6d4
	.4byte	.LASF708
	.byte	0x5
	.uleb128 0x6d5
	.4byte	.LASF709
	.byte	0x5
	.uleb128 0x6d6
	.4byte	.LASF710
	.byte	0x5
	.uleb128 0x6d7
	.4byte	.LASF711
	.byte	0x5
	.uleb128 0x6d8
	.4byte	.LASF712
	.byte	0x5
	.uleb128 0x6d9
	.4byte	.LASF713
	.byte	0x5
	.uleb128 0x6da
	.4byte	.LASF714
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.__SEGGER_RTL.h.26.02ccb90189f593cbe1d6d8bdd77daad3,comdat
.Ldebug_macro6:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0x1a
	.4byte	.LASF715
	.byte	0x5
	.uleb128 0x1b
	.4byte	.LASF716
	.byte	0x5
	.uleb128 0x25
	.4byte	.LASF717
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.stdio.h.34.42aa5dfc4dec4b7eea22339ad13bc805,comdat
.Ldebug_macro7:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0x22
	.4byte	.LASF718
	.byte	0x5
	.uleb128 0x26
	.4byte	.LASF719
	.byte	0x5
	.uleb128 0x2a
	.4byte	.LASF720
	.byte	0x5
	.uleb128 0x2b
	.4byte	.LASF721
	.byte	0x5
	.uleb128 0x35
	.4byte	.LASF722
	.byte	0x5
	.uleb128 0x36
	.4byte	.LASF723
	.byte	0x5
	.uleb128 0x37
	.4byte	.LASF724
	.byte	0x5
	.uleb128 0x39
	.4byte	.LASF725
	.byte	0x5
	.uleb128 0x3a
	.4byte	.LASF726
	.byte	0x5
	.uleb128 0x3b
	.4byte	.LASF727
	.byte	0x5
	.uleb128 0x3c
	.4byte	.LASF728
	.byte	0x5
	.uleb128 0x47
	.4byte	.LASF729
	.byte	0x5
	.uleb128 0x4c
	.4byte	.LASF730
	.byte	0x5
	.uleb128 0x51
	.4byte	.LASF731
	.byte	0x5
	.uleb128 0x56
	.4byte	.LASF732
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.stdbool.h.11.63829d96d260d9a3af29b7ad3c6c191c,comdat
.Ldebug_macro8:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0xb
	.4byte	.LASF733
	.byte	0x5
	.uleb128 0x1d
	.4byte	.LASF734
	.byte	0x5
	.uleb128 0x1e
	.4byte	.LASF735
	.byte	0x5
	.uleb128 0x1f
	.4byte	.LASF736
	.byte	0x5
	.uleb128 0x28
	.4byte	.LASF737
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.stdint.h.11.a90530b67bea3e3b875dbd9f20d72a3e,comdat
.Ldebug_macro9:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0xb
	.4byte	.LASF739
	.byte	0x5
	.uleb128 0x24
	.4byte	.LASF740
	.byte	0x5
	.uleb128 0x25
	.4byte	.LASF741
	.byte	0x5
	.uleb128 0x26
	.4byte	.LASF742
	.byte	0x5
	.uleb128 0x27
	.4byte	.LASF743
	.byte	0x5
	.uleb128 0x28
	.4byte	.LASF744
	.byte	0x5
	.uleb128 0x29
	.4byte	.LASF745
	.byte	0x5
	.uleb128 0x2a
	.4byte	.LASF746
	.byte	0x5
	.uleb128 0x2b
	.4byte	.LASF747
	.byte	0x5
	.uleb128 0x2c
	.4byte	.LASF748
	.byte	0x5
	.uleb128 0x2d
	.4byte	.LASF749
	.byte	0x5
	.uleb128 0x2e
	.4byte	.LASF750
	.byte	0x5
	.uleb128 0x2f
	.4byte	.LASF751
	.byte	0x5
	.uleb128 0x3e
	.4byte	.LASF752
	.byte	0x5
	.uleb128 0x3f
	.4byte	.LASF753
	.byte	0x5
	.uleb128 0x40
	.4byte	.LASF754
	.byte	0x5
	.uleb128 0x41
	.4byte	.LASF755
	.byte	0x5
	.uleb128 0x42
	.4byte	.LASF756
	.byte	0x5
	.uleb128 0x43
	.4byte	.LASF757
	.byte	0x5
	.uleb128 0x44
	.4byte	.LASF758
	.byte	0x5
	.uleb128 0x45
	.4byte	.LASF759
	.byte	0x5
	.uleb128 0x54
	.4byte	.LASF760
	.byte	0x5
	.uleb128 0x55
	.4byte	.LASF761
	.byte	0x5
	.uleb128 0x56
	.4byte	.LASF762
	.byte	0x5
	.uleb128 0x57
	.4byte	.LASF763
	.byte	0x5
	.uleb128 0x58
	.4byte	.LASF764
	.byte	0x5
	.uleb128 0x62
	.4byte	.LASF765
	.byte	0x5
	.uleb128 0x63
	.4byte	.LASF766
	.byte	0x5
	.uleb128 0x64
	.4byte	.LASF767
	.byte	0x5
	.uleb128 0x65
	.4byte	.LASF768
	.byte	0x5
	.uleb128 0x66
	.4byte	.LASF769
	.byte	0x5
	.uleb128 0x67
	.4byte	.LASF770
	.byte	0x5
	.uleb128 0x68
	.4byte	.LASF771
	.byte	0x5
	.uleb128 0x69
	.4byte	.LASF772
	.byte	0x5
	.uleb128 0x6a
	.4byte	.LASF773
	.byte	0x5
	.uleb128 0x6b
	.4byte	.LASF774
	.byte	0x5
	.uleb128 0x6c
	.4byte	.LASF775
	.byte	0x5
	.uleb128 0x6d
	.4byte	.LASF776
	.byte	0x5
	.uleb128 0x6e
	.4byte	.LASF777
	.byte	0x5
	.uleb128 0x6f
	.4byte	.LASF778
	.byte	0x5
	.uleb128 0x70
	.4byte	.LASF779
	.byte	0x5
	.uleb128 0x71
	.4byte	.LASF780
	.byte	0x5
	.uleb128 0x72
	.4byte	.LASF781
	.byte	0x5
	.uleb128 0x73
	.4byte	.LASF782
	.byte	0x5
	.uleb128 0x74
	.4byte	.LASF783
	.byte	0x5
	.uleb128 0x75
	.4byte	.LASF784
	.byte	0x5
	.uleb128 0x87
	.4byte	.LASF785
	.byte	0x5
	.uleb128 0x88
	.4byte	.LASF786
	.byte	0x5
	.uleb128 0x89
	.4byte	.LASF787
	.byte	0x5
	.uleb128 0x8a
	.4byte	.LASF788
	.byte	0x5
	.uleb128 0x8b
	.4byte	.LASF789
	.byte	0x5
	.uleb128 0x8c
	.4byte	.LASF790
	.byte	0x5
	.uleb128 0x8d
	.4byte	.LASF791
	.byte	0x5
	.uleb128 0x8e
	.4byte	.LASF792
	.byte	0x5
	.uleb128 0x8f
	.4byte	.LASF793
	.byte	0x5
	.uleb128 0x90
	.4byte	.LASF794
	.byte	0x5
	.uleb128 0x91
	.4byte	.LASF795
	.byte	0x5
	.uleb128 0x92
	.4byte	.LASF796
	.byte	0x5
	.uleb128 0x93
	.4byte	.LASF797
	.byte	0x5
	.uleb128 0x94
	.4byte	.LASF798
	.byte	0x5
	.uleb128 0x95
	.4byte	.LASF799
	.byte	0x5
	.uleb128 0x96
	.4byte	.LASF800
	.byte	0x5
	.uleb128 0x97
	.4byte	.LASF801
	.byte	0x5
	.uleb128 0x98
	.4byte	.LASF802
	.byte	0x5
	.uleb128 0x99
	.4byte	.LASF803
	.byte	0x5
	.uleb128 0x9a
	.4byte	.LASF804
	.byte	0x5
	.uleb128 0xaa
	.4byte	.LASF805
	.byte	0x5
	.uleb128 0xab
	.4byte	.LASF806
	.byte	0x5
	.uleb128 0xac
	.4byte	.LASF807
	.byte	0x5
	.uleb128 0xad
	.4byte	.LASF808
	.byte	0x5
	.uleb128 0xae
	.4byte	.LASF809
	.byte	0x5
	.uleb128 0xaf
	.4byte	.LASF810
	.byte	0x5
	.uleb128 0xb0
	.4byte	.LASF811
	.byte	0x5
	.uleb128 0xb1
	.4byte	.LASF812
	.byte	0x5
	.uleb128 0xb2
	.4byte	.LASF813
	.byte	0x5
	.uleb128 0xb3
	.4byte	.LASF814
	.byte	0x5
	.uleb128 0xbd
	.4byte	.LASF815
	.byte	0x5
	.uleb128 0xc6
	.4byte	.LASF816
	.byte	0x5
	.uleb128 0xc7
	.4byte	.LASF817
	.byte	0x5
	.uleb128 0xc8
	.4byte	.LASF818
	.byte	0x5
	.uleb128 0xd1
	.4byte	.LASF819
	.byte	0x5
	.uleb128 0xd2
	.4byte	.LASF820
	.byte	0x5
	.uleb128 0xd3
	.4byte	.LASF821
	.byte	0x5
	.uleb128 0xd4
	.4byte	.LASF822
	.byte	0x5
	.uleb128 0xe1
	.4byte	.LASF823
	.byte	0x5
	.uleb128 0xe2
	.4byte	.LASF824
	.byte	0x5
	.uleb128 0xe3
	.4byte	.LASF825
	.byte	0x5
	.uleb128 0xe4
	.4byte	.LASF826
	.byte	0x5
	.uleb128 0xf1
	.4byte	.LASF827
	.byte	0x5
	.uleb128 0xf2
	.4byte	.LASF828
	.byte	0x5
	.uleb128 0xfb
	.4byte	.LASF829
	.byte	0x5
	.uleb128 0xfc
	.4byte	.LASF830
	.byte	0x5
	.uleb128 0xfd
	.4byte	.LASF831
	.byte	0x5
	.uleb128 0x106
	.4byte	.LASF832
	.byte	0x5
	.uleb128 0x107
	.4byte	.LASF833
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.__SEGGER_RTL_FP.h.11.18df31d68207eca316c18f853d5cb41a,comdat
.Ldebug_macro10:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0xb
	.4byte	.LASF836
	.byte	0x5
	.uleb128 0x1d
	.4byte	.LASF837
	.byte	0x5
	.uleb128 0x22
	.4byte	.LASF838
	.byte	0x5
	.uleb128 0x23
	.4byte	.LASF839
	.byte	0x5
	.uleb128 0x24
	.4byte	.LASF840
	.byte	0x5
	.uleb128 0x25
	.4byte	.LASF841
	.byte	0x5
	.uleb128 0x26
	.4byte	.LASF842
	.byte	0x5
	.uleb128 0x31
	.4byte	.LASF843
	.byte	0x5
	.uleb128 0x32
	.4byte	.LASF844
	.byte	0x5
	.uleb128 0x33
	.4byte	.LASF845
	.byte	0x5
	.uleb128 0x34
	.4byte	.LASF846
	.byte	0x5
	.uleb128 0x35
	.4byte	.LASF847
	.byte	0x5
	.uleb128 0x37
	.4byte	.LASF848
	.byte	0x5
	.uleb128 0x38
	.4byte	.LASF849
	.byte	0x5
	.uleb128 0x39
	.4byte	.LASF850
	.byte	0x5
	.uleb128 0x3a
	.4byte	.LASF851
	.byte	0x5
	.uleb128 0x3b
	.4byte	.LASF852
	.byte	0x5
	.uleb128 0x3c
	.4byte	.LASF853
	.byte	0x5
	.uleb128 0x3d
	.4byte	.LASF854
	.byte	0x5
	.uleb128 0x3e
	.4byte	.LASF855
	.byte	0x5
	.uleb128 0x3f
	.4byte	.LASF856
	.byte	0x5
	.uleb128 0x40
	.4byte	.LASF857
	.byte	0x5
	.uleb128 0x41
	.4byte	.LASF858
	.byte	0x5
	.uleb128 0x42
	.4byte	.LASF859
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.math.h.29.2977100bc3297678f06649ddc7382d7b,comdat
.Ldebug_macro11:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0x1d
	.4byte	.LASF860
	.byte	0x5
	.uleb128 0x1e
	.4byte	.LASF861
	.byte	0x5
	.uleb128 0x21
	.4byte	.LASF862
	.byte	0x5
	.uleb128 0x23
	.4byte	.LASF863
	.byte	0x5
	.uleb128 0x24
	.4byte	.LASF864
	.byte	0x5
	.uleb128 0x26
	.4byte	.LASF865
	.byte	0x5
	.uleb128 0x27
	.4byte	.LASF866
	.byte	0x5
	.uleb128 0x28
	.4byte	.LASF867
	.byte	0x5
	.uleb128 0x29
	.4byte	.LASF868
	.byte	0x5
	.uleb128 0x2a
	.4byte	.LASF869
	.byte	0x5
	.uleb128 0x2b
	.4byte	.LASF870
	.byte	0x5
	.uleb128 0x2c
	.4byte	.LASF871
	.byte	0x5
	.uleb128 0x2d
	.4byte	.LASF872
	.byte	0x5
	.uleb128 0x2e
	.4byte	.LASF873
	.byte	0x5
	.uleb128 0x2f
	.4byte	.LASF874
	.byte	0x5
	.uleb128 0x30
	.4byte	.LASF875
	.byte	0x5
	.uleb128 0x31
	.4byte	.LASF876
	.byte	0x5
	.uleb128 0x32
	.4byte	.LASF877
	.byte	0x5
	.uleb128 0x38
	.4byte	.LASF843
	.byte	0x5
	.uleb128 0x39
	.4byte	.LASF844
	.byte	0x5
	.uleb128 0x3a
	.4byte	.LASF845
	.byte	0x5
	.uleb128 0x3b
	.4byte	.LASF846
	.byte	0x5
	.uleb128 0x3c
	.4byte	.LASF847
	.byte	0x5
	.uleb128 0x3e
	.4byte	.LASF878
	.byte	0x5
	.uleb128 0x3f
	.4byte	.LASF879
	.byte	0x5
	.uleb128 0x40
	.4byte	.LASF880
	.byte	0x5
	.uleb128 0x41
	.4byte	.LASF881
	.byte	0x5
	.uleb128 0x43
	.4byte	.LASF848
	.byte	0x5
	.uleb128 0x44
	.4byte	.LASF849
	.byte	0x5
	.uleb128 0x45
	.4byte	.LASF850
	.byte	0x5
	.uleb128 0x46
	.4byte	.LASF851
	.byte	0x5
	.uleb128 0x47
	.4byte	.LASF852
	.byte	0x5
	.uleb128 0x48
	.4byte	.LASF853
	.byte	0x5
	.uleb128 0x4a
	.4byte	.LASF854
	.byte	0x5
	.uleb128 0x4b
	.4byte	.LASF855
	.byte	0x5
	.uleb128 0x4c
	.4byte	.LASF856
	.byte	0x5
	.uleb128 0x4d
	.4byte	.LASF857
	.byte	0x5
	.uleb128 0x4e
	.4byte	.LASF858
	.byte	0x5
	.uleb128 0x4f
	.4byte	.LASF859
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.ins.h.23.dd3cbb63ac4ca10dfc04e1ebdbdee3a7,comdat
.Ldebug_macro12:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0x17
	.4byte	.LASF882
	.byte	0x5
	.uleb128 0x26
	.4byte	.LASF883
	.byte	0x5
	.uleb128 0x29
	.4byte	.LASF884
	.byte	0x5
	.uleb128 0x2b
	.4byte	.LASF885
	.byte	0x5
	.uleb128 0x2c
	.4byte	.LASF886
	.byte	0x5
	.uleb128 0x2d
	.4byte	.LASF887
	.byte	0x5
	.uleb128 0x2e
	.4byte	.LASF888
	.byte	0x5
	.uleb128 0x30
	.4byte	.LASF889
	.byte	0x5
	.uleb128 0x31
	.4byte	.LASF890
	.byte	0x5
	.uleb128 0x33
	.4byte	.LASF891
	.byte	0x5
	.uleb128 0x44
	.4byte	.LASF892
	.byte	0x5
	.uleb128 0x45
	.4byte	.LASF893
	.byte	0x5
	.uleb128 0x46
	.4byte	.LASF894
	.byte	0x5
	.uleb128 0x47
	.4byte	.LASF895
	.byte	0x5
	.uleb128 0x4a
	.4byte	.LASF896
	.byte	0x5
	.uleb128 0x4b
	.4byte	.LASF897
	.byte	0x5
	.uleb128 0x4d
	.4byte	.LASF898
	.byte	0x5
	.uleb128 0x4e
	.4byte	.LASF899
	.byte	0x5
	.uleb128 0x4f
	.4byte	.LASF900
	.byte	0x5
	.uleb128 0x51
	.4byte	.LASF901
	.byte	0x5
	.uleb128 0x52
	.4byte	.LASF902
	.byte	0x5
	.uleb128 0x53
	.4byte	.LASF903
	.byte	0x5
	.uleb128 0x55
	.4byte	.LASF904
	.byte	0x5
	.uleb128 0x56
	.4byte	.LASF905
	.byte	0x5
	.uleb128 0x57
	.4byte	.LASF906
	.byte	0x5
	.uleb128 0x59
	.4byte	.LASF907
	.byte	0x5
	.uleb128 0x5a
	.4byte	.LASF908
	.byte	0x5
	.uleb128 0x5b
	.4byte	.LASF909
	.byte	0x5
	.uleb128 0x5d
	.4byte	.LASF910
	.byte	0x5
	.uleb128 0x5e
	.4byte	.LASF911
	.byte	0x5
	.uleb128 0x5f
	.4byte	.LASF912
	.byte	0x5
	.uleb128 0x61
	.4byte	.LASF913
	.byte	0x5
	.uleb128 0x62
	.4byte	.LASF914
	.byte	0x5
	.uleb128 0x63
	.4byte	.LASF915
	.byte	0x5
	.uleb128 0x65
	.4byte	.LASF916
	.byte	0x5
	.uleb128 0x66
	.4byte	.LASF917
	.byte	0x5
	.uleb128 0x67
	.4byte	.LASF918
	.byte	0x5
	.uleb128 0x219
	.4byte	.LASF919
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.deviceconfig.h.2.7304ce8be4a20c7e48a7c6c9303013c1,comdat
.Ldebug_macro13:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0x2
	.4byte	.LASF921
	.byte	0x5
	.uleb128 0x1c
	.4byte	.LASF922
	.byte	0x5
	.uleb128 0x25
	.4byte	.LASF923
	.byte	0x5
	.uleb128 0x29
	.4byte	.LASF924
	.byte	0x5
	.uleb128 0x32
	.4byte	.LASF925
	.byte	0x5
	.uleb128 0x33
	.4byte	.LASF926
	.byte	0x5
	.uleb128 0x34
	.4byte	.LASF927
	.byte	0x5
	.uleb128 0x35
	.4byte	.LASF928
	.byte	0x5
	.uleb128 0x36
	.4byte	.LASF929
	.byte	0x5
	.uleb128 0x37
	.4byte	.LASF930
	.byte	0x5
	.uleb128 0x38
	.4byte	.LASF931
	.byte	0x5
	.uleb128 0x39
	.4byte	.LASF932
	.byte	0x5
	.uleb128 0x3a
	.4byte	.LASF933
	.byte	0x5
	.uleb128 0x48
	.4byte	.LASF934
	.byte	0x5
	.uleb128 0x49
	.4byte	.LASF935
	.byte	0x5
	.uleb128 0x60
	.4byte	.LASF936
	.byte	0x5
	.uleb128 0x72
	.4byte	.LASF937
	.byte	0x5
	.uleb128 0x83
	.4byte	.LASF938
	.byte	0x5
	.uleb128 0x89
	.4byte	.LASF939
	.byte	0
	.section	.debug_macro,"G",@progbits,wm4.CONST.h.15.d4f37c9f2a5fb31d3077616cc00ee100,comdat
.Ldebug_macro14:
	.2byte	0x4
	.byte	0
	.byte	0x5
	.uleb128 0xf
	.4byte	.LASF940
	.byte	0x5
	.uleb128 0x11
	.4byte	.LASF941
	.byte	0x5
	.uleb128 0x13
	.4byte	.LASF942
	.byte	0x5
	.uleb128 0x14
	.4byte	.LASF943
	.byte	0x5
	.uleb128 0x18
	.4byte	.LASF944
	.byte	0x5
	.uleb128 0x19
	.4byte	.LASF945
	.byte	0x5
	.uleb128 0x1d
	.4byte	.LASF946
	.byte	0x5
	.uleb128 0x1f
	.4byte	.LASF947
	.byte	0x5
	.uleb128 0x20
	.4byte	.LASF948
	.byte	0x5
	.uleb128 0x21
	.4byte	.LASF949
	.byte	0x5
	.uleb128 0x22
	.4byte	.LASF950
	.byte	0x5
	.uleb128 0x23
	.4byte	.LASF951
	.byte	0x5
	.uleb128 0x24
	.4byte	.LASF952
	.byte	0x5
	.uleb128 0x25
	.4byte	.LASF953
	.byte	0x5
	.uleb128 0x26
	.4byte	.LASF954
	.byte	0x5
	.uleb128 0x27
	.4byte	.LASF955
	.byte	0x5
	.uleb128 0x28
	.4byte	.LASF956
	.byte	0x5
	.uleb128 0x29
	.4byte	.LASF957
	.byte	0x5
	.uleb128 0x2b
	.4byte	.LASF958
	.byte	0x5
	.uleb128 0x2e
	.4byte	.LASF959
	.byte	0x5
	.uleb128 0x2f
	.4byte	.LASF960
	.byte	0x5
	.uleb128 0x31
	.4byte	.LASF961
	.byte	0x5
	.uleb128 0x34
	.4byte	.LASF962
	.byte	0x5
	.uleb128 0x37
	.4byte	.LASF963
	.byte	0x5
	.uleb128 0x38
	.4byte	.LASF964
	.byte	0x5
	.uleb128 0x39
	.4byte	.LASF965
	.byte	0x5
	.uleb128 0x3a
	.4byte	.LASF966
	.byte	0x5
	.uleb128 0x3b
	.4byte	.LASF967
	.byte	0x5
	.uleb128 0x3c
	.4byte	.LASF968
	.byte	0x5
	.uleb128 0x3e
	.4byte	.LASF969
	.byte	0x5
	.uleb128 0x3f
	.4byte	.LASF970
	.byte	0x5
	.uleb128 0x41
	.4byte	.LASF971
	.byte	0x5
	.uleb128 0x42
	.4byte	.LASF972
	.byte	0x5
	.uleb128 0x43
	.4byte	.LASF973
	.byte	0x5
	.uleb128 0x44
	.4byte	.LASF974
	.byte	0x5
	.uleb128 0x45
	.4byte	.LASF975
	.byte	0x5
	.uleb128 0x46
	.4byte	.LASF976
	.byte	0x5
	.uleb128 0x47
	.4byte	.LASF977
	.byte	0x5
	.uleb128 0x48
	.4byte	.LASF978
	.byte	0x5
	.uleb128 0x49
	.4byte	.LASF979
	.byte	0x5
	.uleb128 0x4a
	.4byte	.LASF980
	.byte	0x5
	.uleb128 0x4b
	.4byte	.LASF981
	.byte	0x5
	.uleb128 0x4d
	.4byte	.LASF982
	.byte	0x5
	.uleb128 0x4e
	.4byte	.LASF983
	.byte	0x5
	.uleb128 0x4f
	.4byte	.LASF984
	.byte	0x5
	.uleb128 0x50
	.4byte	.LASF985
	.byte	0x5
	.uleb128 0x51
	.4byte	.LASF986
	.byte	0x5
	.uleb128 0x52
	.4byte	.LASF987
	.byte	0x5
	.uleb128 0x53
	.4byte	.LASF988
	.byte	0x5
	.uleb128 0x54
	.4byte	.LASF989
	.byte	0x5
	.uleb128 0x55
	.4byte	.LASF990
	.byte	0x5
	.uleb128 0x57
	.4byte	.LASF991
	.byte	0x5
	.uleb128 0x58
	.4byte	.LASF992
	.byte	0x5
	.uleb128 0x59
	.4byte	.LASF993
	.byte	0x5
	.uleb128 0x64
	.4byte	.LASF994
	.byte	0x5
	.uleb128 0x65
	.4byte	.LASF995
	.byte	0x5
	.uleb128 0x67
	.4byte	.LASF996
	.byte	0x5
	.uleb128 0x69
	.4byte	.LASF997
	.byte	0x5
	.uleb128 0x6a
	.4byte	.LASF998
	.byte	0x5
	.uleb128 0x6d
	.4byte	.LASF999
	.byte	0x5
	.uleb128 0x6e
	.4byte	.LASF1000
	.byte	0x5
	.uleb128 0x6f
	.4byte	.LASF1001
	.byte	0x5
	.uleb128 0x72
	.4byte	.LASF1002
	.byte	0x5
	.uleb128 0x74
	.4byte	.LASF1003
	.byte	0x5
	.uleb128 0x76
	.4byte	.LASF1004
	.byte	0x5
	.uleb128 0x77
	.4byte	.LASF1005
	.byte	0x5
	.uleb128 0x78
	.4byte	.LASF1006
	.byte	0x5
	.uleb128 0x7c
	.4byte	.LASF1007
	.byte	0x5
	.uleb128 0x80
	.4byte	.LASF1008
	.byte	0x5
	.uleb128 0x81
	.4byte	.LASF1009
	.byte	0x5
	.uleb128 0x83
	.4byte	.LASF1010
	.byte	0x5
	.uleb128 0x84
	.4byte	.LASF1011
	.byte	0x5
	.uleb128 0x85
	.4byte	.LASF1012
	.byte	0x5
	.uleb128 0x87
	.4byte	.LASF1013
	.byte	0x5
	.uleb128 0x8a
	.4byte	.LASF1014
	.byte	0x5
	.uleb128 0x8b
	.4byte	.LASF1015
	.byte	0x5
	.uleb128 0x8c
	.4byte	.LASF1016
	.byte	0x5
	.uleb128 0x8f
	.4byte	.LASF1017
	.byte	0x5
	.uleb128 0x92
	.4byte	.LASF1018
	.byte	0x5
	.uleb128 0x96
	.4byte	.LASF1019
	.byte	0x5
	.uleb128 0x97
	.4byte	.LASF1020
	.byte	0x5
	.uleb128 0x99
	.4byte	.LASF1021
	.byte	0x5
	.uleb128 0x9c
	.4byte	.LASF1022
	.byte	0x5
	.uleb128 0x9f
	.4byte	.LASF1023
	.byte	0x5
	.uleb128 0xa1
	.4byte	.LASF1024
	.byte	0x5
	.uleb128 0xa4
	.4byte	.LASF1025
	.byte	0x5
	.uleb128 0xa5
	.4byte	.LASF1026
	.byte	0x5
	.uleb128 0xa8
	.4byte	.LASF1027
	.byte	0x5
	.uleb128 0xa9
	.4byte	.LASF1028
	.byte	0x5
	.uleb128 0xaa
	.4byte	.LASF1029
	.byte	0x5
	.uleb128 0xab
	.4byte	.LASF1030
	.byte	0x5
	.uleb128 0xae
	.4byte	.LASF1031
	.byte	0x5
	.uleb128 0xaf
	.4byte	.LASF1032
	.byte	0x5
	.uleb128 0xb0
	.4byte	.LASF1033
	.byte	0x5
	.uleb128 0xb1
	.4byte	.LASF1034
	.byte	0x5
	.uleb128 0xb2
	.4byte	.LASF1035
	.byte	0x5
	.uleb128 0xb3
	.4byte	.LASF1036
	.byte	0x5
	.uleb128 0xb5
	.4byte	.LASF1037
	.byte	0x5
	.uleb128 0xb6
	.4byte	.LASF1038
	.byte	0x5
	.uleb128 0xb7
	.4byte	.LASF1039
	.byte	0x5
	.uleb128 0xb8
	.4byte	.LASF1040
	.byte	0x5
	.uleb128 0xba
	.4byte	.LASF1041
	.byte	0x5
	.uleb128 0xbc
	.4byte	.LASF1042
	.byte	0x5
	.uleb128 0xbd
	.4byte	.LASF1043
	.byte	0x5
	.uleb128 0xbf
	.4byte	.LASF1044
	.byte	0x5
	.uleb128 0xc0
	.4byte	.LASF1045
	.byte	0x5
	.uleb128 0xc2
	.4byte	.LASF1046
	.byte	0x5
	.uleb128 0xc3
	.4byte	.LASF1047
	.byte	0x5
	.uleb128 0xc5
	.4byte	.LASF1048
	.byte	0x5
	.uleb128 0xc7
	.4byte	.LASF1049
	.byte	0x5
	.uleb128 0xc9
	.4byte	.LASF1050
	.byte	0x5
	.uleb128 0xcb
	.4byte	.LASF1051
	.byte	0x5
	.uleb128 0xcc
	.4byte	.LASF1052
	.byte	0x5
	.uleb128 0xcd
	.4byte	.LASF1053
	.byte	0x5
	.uleb128 0xce
	.4byte	.LASF1054
	.byte	0x5
	.uleb128 0xcf
	.4byte	.LASF1055
	.byte	0x5
	.uleb128 0xd0
	.4byte	.LASF1056
	.byte	0x5
	.uleb128 0xd1
	.4byte	.LASF1057
	.byte	0x5
	.uleb128 0xd4
	.4byte	.LASF1058
	.byte	0x5
	.uleb128 0xd5
	.4byte	.LASF1059
	.byte	0x5
	.uleb128 0xd6
	.4byte	.LASF1060
	.byte	0x5
	.uleb128 0xd7
	.4byte	.LASF1061
	.byte	0x5
	.uleb128 0xda
	.4byte	.LASF1062
	.byte	0x5
	.uleb128 0xdb
	.4byte	.LASF1063
	.byte	0x5
	.uleb128 0xdc
	.4byte	.LASF1064
	.byte	0x5
	.uleb128 0xdd
	.4byte	.LASF1065
	.byte	0x5
	.uleb128 0xe0
	.4byte	.LASF1066
	.byte	0x5
	.uleb128 0xe1
	.4byte	.LASF1067
	.byte	0x5
	.uleb128 0xe3
	.4byte	.LASF1068
	.byte	0x5
	.uleb128 0xe4
	.4byte	.LASF1069
	.byte	0x5
	.uleb128 0xe6
	.4byte	.LASF1070
	.byte	0x5
	.uleb128 0xe7
	.4byte	.LASF1071
	.byte	0x5
	.uleb128 0xe8
	.4byte	.LASF1072
	.byte	0x5
	.uleb128 0xe9
	.4byte	.LASF1073
	.byte	0x5
	.uleb128 0xec
	.4byte	.LASF1074
	.byte	0x5
	.uleb128 0xed
	.4byte	.LASF1075
	.byte	0x5
	.uleb128 0xee
	.4byte	.LASF1076
	.byte	0x5
	.uleb128 0xef
	.4byte	.LASF1077
	.byte	0x5
	.uleb128 0xf4
	.4byte	.LASF1078
	.byte	0x5
	.uleb128 0xf6
	.4byte	.LASF1079
	.byte	0x5
	.uleb128 0xf7
	.4byte	.LASF1080
	.byte	0x5
	.uleb128 0xf8
	.4byte	.LASF1081
	.byte	0x5
	.uleb128 0xfa
	.4byte	.LASF1082
	.byte	0x5
	.uleb128 0xfb
	.4byte	.LASF1083
	.byte	0x5
	.uleb128 0xfc
	.4byte	.LASF1084
	.byte	0x5
	.uleb128 0xfd
	.4byte	.LASF1085
	.byte	0x5
	.uleb128 0x100
	.4byte	.LASF1086
	.byte	0x5
	.uleb128 0x101
	.4byte	.LASF1087
	.byte	0x5
	.uleb128 0x102
	.4byte	.LASF1088
	.byte	0x5
	.uleb128 0x104
	.4byte	.LASF1089
	.byte	0x5
	.uleb128 0x105
	.4byte	.LASF1090
	.byte	0x5
	.uleb128 0x106
	.4byte	.LASF1091
	.byte	0x5
	.uleb128 0x107
	.4byte	.LASF1092
	.byte	0x5
	.uleb128 0x108
	.4byte	.LASF1093
	.byte	0x5
	.uleb128 0x109
	.4byte	.LASF1094
	.byte	0x5
	.uleb128 0x10a
	.4byte	.LASF1095
	.byte	0x5
	.uleb128 0x10b
	.4byte	.LASF1096
	.byte	0x5
	.uleb128 0x10c
	.4byte	.LASF1097
	.byte	0x5
	.uleb128 0x10d
	.4byte	.LASF1098
	.byte	0x5
	.uleb128 0x10f
	.4byte	.LASF1099
	.byte	0x5
	.uleb128 0x110
	.4byte	.LASF1100
	.byte	0x5
	.uleb128 0x112
	.4byte	.LASF1101
	.byte	0x5
	.uleb128 0x113
	.4byte	.LASF1102
	.byte	0x5
	.uleb128 0x115
	.4byte	.LASF1103
	.byte	0x5
	.uleb128 0x116
	.4byte	.LASF1104
	.byte	0x5
	.uleb128 0x119
	.4byte	.LASF1105
	.byte	0x5
	.uleb128 0x11a
	.4byte	.LASF1106
	.byte	0x5
	.uleb128 0x11b
	.4byte	.LASF1107
	.byte	0x5
	.uleb128 0x11c
	.4byte	.LASF1108
	.byte	0x5
	.uleb128 0x11e
	.4byte	.LASF1109
	.byte	0x5
	.uleb128 0x11f
	.4byte	.LASF1110
	.byte	0x5
	.uleb128 0x120
	.4byte	.LASF1111
	.byte	0x5
	.uleb128 0x122
	.4byte	.LASF1112
	.byte	0x5
	.uleb128 0x123
	.4byte	.LASF1113
	.byte	0x5
	.uleb128 0x124
	.4byte	.LASF1114
	.byte	0x5
	.uleb128 0x125
	.4byte	.LASF1115
	.byte	0x5
	.uleb128 0x126
	.4byte	.LASF1116
	.byte	0x5
	.uleb128 0x128
	.4byte	.LASF1117
	.byte	0x5
	.uleb128 0x12c
	.4byte	.LASF1118
	.byte	0x5
	.uleb128 0x12d
	.4byte	.LASF1119
	.byte	0x5
	.uleb128 0x12f
	.4byte	.LASF1120
	.byte	0x5
	.uleb128 0x131
	.4byte	.LASF1121
	.byte	0x5
	.uleb128 0x132
	.4byte	.LASF1122
	.byte	0x5
	.uleb128 0x133
	.4byte	.LASF1123
	.byte	0x5
	.uleb128 0x134
	.4byte	.LASF1124
	.byte	0x5
	.uleb128 0x135
	.4byte	.LASF1125
	.byte	0x5
	.uleb128 0x136
	.4byte	.LASF1126
	.byte	0x5
	.uleb128 0x138
	.4byte	.LASF1127
	.byte	0x5
	.uleb128 0x139
	.4byte	.LASF1128
	.byte	0x5
	.uleb128 0x13a
	.4byte	.LASF1129
	.byte	0x5
	.uleb128 0x13b
	.4byte	.LASF1130
	.byte	0x5
	.uleb128 0x13c
	.4byte	.LASF1131
	.byte	0x5
	.uleb128 0x13e
	.4byte	.LASF1132
	.byte	0x5
	.uleb128 0x13f
	.4byte	.LASF1133
	.byte	0x5
	.uleb128 0x140
	.4byte	.LASF1134
	.byte	0x5
	.uleb128 0x141
	.4byte	.LASF1135
	.byte	0x5
	.uleb128 0x142
	.4byte	.LASF1136
	.byte	0x5
	.uleb128 0x144
	.4byte	.LASF1137
	.byte	0x5
	.uleb128 0x145
	.4byte	.LASF1138
	.byte	0x5
	.uleb128 0x146
	.4byte	.LASF1139
	.byte	0x5
	.uleb128 0x148
	.4byte	.LASF1140
	.byte	0x5
	.uleb128 0x149
	.4byte	.LASF1141
	.byte	0x5
	.uleb128 0x14a
	.4byte	.LASF1142
	.byte	0x5
	.uleb128 0x14b
	.4byte	.LASF1143
	.byte	0x5
	.uleb128 0x14c
	.4byte	.LASF1144
	.byte	0x5
	.uleb128 0x14e
	.4byte	.LASF1145
	.byte	0x5
	.uleb128 0x14f
	.4byte	.LASF1146
	.byte	0x5
	.uleb128 0x151
	.4byte	.LASF1147
	.byte	0x5
	.uleb128 0x153
	.4byte	.LASF1141
	.byte	0x5
	.uleb128 0x154
	.4byte	.LASF1148
	.byte	0x5
	.uleb128 0x155
	.4byte	.LASF1149
	.byte	0x5
	.uleb128 0x156
	.4byte	.LASF1150
	.byte	0x5
	.uleb128 0x158
	.4byte	.LASF1151
	.byte	0x5
	.uleb128 0x159
	.4byte	.LASF1152
	.byte	0x5
	.uleb128 0x15a
	.4byte	.LASF1153
	.byte	0x5
	.uleb128 0x15b
	.4byte	.LASF1154
	.byte	0x5
	.uleb128 0x15d
	.4byte	.LASF1155
	.byte	0x5
	.uleb128 0x160
	.4byte	.LASF1156
	.byte	0x5
	.uleb128 0x161
	.4byte	.LASF1157
	.byte	0x5
	.uleb128 0x163
	.4byte	.LASF1158
	.byte	0x5
	.uleb128 0x164
	.4byte	.LASF1159
	.byte	0x5
	.uleb128 0x165
	.4byte	.LASF1160
	.byte	0x5
	.uleb128 0x166
	.4byte	.LASF1161
	.byte	0x5
	.uleb128 0x167
	.4byte	.LASF1162
	.byte	0x5
	.uleb128 0x168
	.4byte	.LASF1163
	.byte	0x5
	.uleb128 0x169
	.4byte	.LASF1164
	.byte	0x5
	.uleb128 0x16a
	.4byte	.LASF1165
	.byte	0x5
	.uleb128 0x16e
	.4byte	.LASF1166
	.byte	0x5
	.uleb128 0x16f
	.4byte	.LASF1167
	.byte	0x5
	.uleb128 0x171
	.4byte	.LASF1168
	.byte	0x5
	.uleb128 0x172
	.4byte	.LASF1169
	.byte	0x5
	.uleb128 0x175
	.4byte	.LASF1170
	.byte	0x5
	.uleb128 0x176
	.4byte	.LASF1171
	.byte	0x5
	.uleb128 0x177
	.4byte	.LASF1172
	.byte	0x5
	.uleb128 0x178
	.4byte	.LASF1173
	.byte	0x5
	.uleb128 0x17d
	.4byte	.LASF1174
	.byte	0x5
	.uleb128 0x17e
	.4byte	.LASF1175
	.byte	0x5
	.uleb128 0x17f
	.4byte	.LASF1176
	.byte	0x5
	.uleb128 0x180
	.4byte	.LASF1177
	.byte	0x5
	.uleb128 0x181
	.4byte	.LASF1178
	.byte	0x5
	.uleb128 0x182
	.4byte	.LASF1179
	.byte	0x5
	.uleb128 0x183
	.4byte	.LASF1180
	.byte	0x5
	.uleb128 0x184
	.4byte	.LASF1181
	.byte	0x5
	.uleb128 0x185
	.4byte	.LASF1182
	.byte	0x5
	.uleb128 0x187
	.4byte	.LASF1183
	.byte	0x5
	.uleb128 0x188
	.4byte	.LASF1184
	.byte	0x5
	.uleb128 0x189
	.4byte	.LASF1185
	.byte	0x5
	.uleb128 0x18a
	.4byte	.LASF1186
	.byte	0x5
	.uleb128 0x18d
	.4byte	.LASF1187
	.byte	0x5
	.uleb128 0x18e
	.4byte	.LASF1188
	.byte	0x5
	.uleb128 0x18f
	.4byte	.LASF1189
	.byte	0x5
	.uleb128 0x190
	.4byte	.LASF1190
	.byte	0x5
	.uleb128 0x192
	.4byte	.LASF1191
	.byte	0x5
	.uleb128 0x193
	.4byte	.LASF1192
	.byte	0x5
	.uleb128 0x194
	.4byte	.LASF1193
	.byte	0x5
	.uleb128 0x196
	.4byte	.LASF1194
	.byte	0x5
	.uleb128 0x199
	.4byte	.LASF1195
	.byte	0x5
	.uleb128 0x19a
	.4byte	.LASF1196
	.byte	0x5
	.uleb128 0x19b
	.4byte	.LASF1197
	.byte	0x5
	.uleb128 0x19c
	.4byte	.LASF1198
	.byte	0x5
	.uleb128 0x19d
	.4byte	.LASF1199
	.byte	0x5
	.uleb128 0x19e
	.4byte	.LASF1200
	.byte	0x5
	.uleb128 0x19f
	.4byte	.LASF1201
	.byte	0x5
	.uleb128 0x1a0
	.4byte	.LASF1202
	.byte	0x5
	.uleb128 0x1a2
	.4byte	.LASF1203
	.byte	0x5
	.uleb128 0x1a3
	.4byte	.LASF1204
	.byte	0x5
	.uleb128 0x1a4
	.4byte	.LASF1205
	.byte	0x5
	.uleb128 0x1a6
	.4byte	.LASF1206
	.byte	0x5
	.uleb128 0x1a7
	.4byte	.LASF1207
	.byte	0x5
	.uleb128 0x1a8
	.4byte	.LASF1208
	.byte	0x5
	.uleb128 0x1ab
	.4byte	.LASF1209
	.byte	0x5
	.uleb128 0x1ac
	.4byte	.LASF1210
	.byte	0x5
	.uleb128 0x1af
	.4byte	.LASF1211
	.byte	0x5
	.uleb128 0x1b0
	.4byte	.LASF1212
	.byte	0x5
	.uleb128 0x1b3
	.4byte	.LASF1213
	.byte	0x5
	.uleb128 0x1b4
	.4byte	.LASF1214
	.byte	0x5
	.uleb128 0x1b7
	.4byte	.LASF1215
	.byte	0x5
	.uleb128 0x1b8
	.4byte	.LASF1216
	.byte	0x5
	.uleb128 0x1bb
	.4byte	.LASF1217
	.byte	0x5
	.uleb128 0x1bc
	.4byte	.LASF1218
	.byte	0x5
	.uleb128 0x1bf
	.4byte	.LASF1219
	.byte	0x5
	.uleb128 0x1c2
	.4byte	.LASF1220
	.byte	0x5
	.uleb128 0x1c4
	.4byte	.LASF1221
	.byte	0x5
	.uleb128 0x1c7
	.4byte	.LASF1222
	.byte	0x5
	.uleb128 0x1c9
	.4byte	.LASF1223
	.byte	0x5
	.uleb128 0x1ca
	.4byte	.LASF1224
	.byte	0x5
	.uleb128 0x1cc
	.4byte	.LASF1225
	.byte	0x5
	.uleb128 0x1cd
	.4byte	.LASF1226
	.byte	0x5
	.uleb128 0x1ce
	.4byte	.LASF1227
	.byte	0x5
	.uleb128 0x1cf
	.4byte	.LASF1228
	.byte	0x5
	.uleb128 0x1d1
	.4byte	.LASF1229
	.byte	0x5
	.uleb128 0x1d2
	.4byte	.LASF1230
	.byte	0x5
	.uleb128 0x1d3
	.4byte	.LASF1231
	.byte	0x5
	.uleb128 0x1d5
	.4byte	.LASF1232
	.byte	0x5
	.uleb128 0x1d8
	.4byte	.LASF1233
	.byte	0x5
	.uleb128 0x1d9
	.4byte	.LASF1234
	.byte	0x5
	.uleb128 0x1da
	.4byte	.LASF1235
	.byte	0x5
	.uleb128 0x1db
	.4byte	.LASF1236
	.byte	0x5
	.uleb128 0x1dc
	.4byte	.LASF1237
	.byte	0x5
	.uleb128 0x1dd
	.4byte	.LASF1238
	.byte	0x5
	.uleb128 0x1df
	.4byte	.LASF1239
	.byte	0x5
	.uleb128 0x1e0
	.4byte	.LASF1240
	.byte	0x5
	.uleb128 0x1e3
	.4byte	.LASF1241
	.byte	0x5
	.uleb128 0x1e4
	.4byte	.LASF1242
	.byte	0x5
	.uleb128 0x1e5
	.4byte	.LASF1243
	.byte	0x5
	.uleb128 0x1e6
	.4byte	.LASF1244
	.byte	0x5
	.uleb128 0x1e7
	.4byte	.LASF1245
	.byte	0x5
	.uleb128 0x1e8
	.4byte	.LASF1246
	.byte	0x5
	.uleb128 0x1ea
	.4byte	.LASF1247
	.byte	0x5
	.uleb128 0x1eb
	.4byte	.LASF1248
	.byte	0x5
	.uleb128 0x1ec
	.4byte	.LASF1249
	.byte	0x5
	.uleb128 0x1ed
	.4byte	.LASF1250
	.byte	0x5
	.uleb128 0x1ee
	.4byte	.LASF1251
	.byte	0
	.section	.debug_line,"",@progbits
.Ldebug_line0:
	.section	.debug_str,"MS",@progbits,1
.LASF1033:
	.string	"ATTITUDE_CORRECT_COUNT (50)"
.LASF677:
	.string	"__SEGGER_RTL_BitcastToF64(X) __SEGGER_RTL_BitcastToF64_inline(X)"
.LASF341:
	.string	"HPMSOC_HAS_HPMSDK_MCHTMR y"
.LASF726:
	.string	"_IOFBF 0"
.LASF1036:
	.string	"ACC_DRIFT_CORRECT_COUNT (2500)"
.LASF316:
	.string	"__riscv 1"
.LASF802:
	.string	"UINT_FAST32_WIDTH __SEGGER_RTL_UINT_FAST32_WIDTH"
.LASF247:
	.string	"__FLT128_MIN_10_EXP__ (-4931)"
.LASF849:
	.string	"isnan(x) (sizeof(x) == sizeof(float) ? __SEGGER_RTL_float32_isnan(x) : __SEGGER_RTL_float64_isnan(x))"
.LASF323:
	.string	"__riscv_float_abi_soft 1"
.LASF1069:
	.string	"NUM_1S_COUNT (200)"
.LASF304:
	.string	"__GCC_ATOMIC_WCHAR_T_LOCK_FREE 2"
.LASF296:
	.string	"__CHAR_UNSIGNED__ 1"
.LASF636:
	.string	"__SEGGER_RTL_PTRDIFF_WIDTH __PTRDIFF_WIDTH__"
.LASF828:
	.string	"UINTMAX_C(x) __SEGGER_RTL_UINTMAX_C(x)"
.LASF609:
	.string	"__SEGGER_RTL_UINT32_WIDTH 32"
.LASF826:
	.string	"UINT64_C(x) __SEGGER_RTL_UINT64_C(x)"
.LASF1205:
	.string	"GYRO_Z_FAIL (0x04)"
.LASF1170:
	.string	"AIR_HEIGHT_VALID (0x01)"
.LASF241:
	.string	"__FLT64_HAS_INFINITY__ 1"
.LASF1180:
	.string	"VMC_AIR_VALID (0x20)"
.LASF530:
	.string	"__SEGGER_RTL_UINT_FAST16_T __UINT_FAST16_TYPE__"
.LASF1226:
	.string	"ALIGN_MODE_NONE (0x00)"
.LASF1381:
	.string	"wieb"
.LASF1059:
	.string	"STATE_GNSS_ALIGN (0x02)"
.LASF79:
	.string	"__PTRDIFF_MAX__ 0x7fffffff"
.LASF1183:
	.string	"COM_VMC_VALID (0x01)"
.LASF1173:
	.string	"VERTICAL_V_VALID (0x20)"
.LASF674:
	.string	"__SEGGER_RTL_BitcastToU32(X) __SEGGER_RTL_BitcastToU32_inline(X)"
.LASF91:
	.string	"__INTMAX_C(c) c ## LL"
.LASF237:
	.string	"__FLT64_MIN__ 1.1"
.LASF783:
	.string	"UINT_LEAST64_MAX __SEGGER_RTL_UINT_LEAST64_MAX"
.LASF90:
	.string	"__INTMAX_MAX__ 0x7fffffffffffffffLL"
.LASF1235:
	.string	"POSVEL_FIT (0x03)"
.LASF408:
	.string	"__SEGGER_RTL_STDC_VERSION_C11 201112L"
.LASF950:
	.string	"D2R (1.7453292519943295769236907684886e-2)"
.LASF722:
	.string	"SEEK_SET 0"
.LASF234:
	.string	"__FLT64_DECIMAL_DIG__ 17"
.LASF725:
	.string	"L_tmpnam 256"
.LASF1071:
	.string	"DENSE_1_CELL_NUM (4)"
.LASF868:
	.string	"M_LN2 0.693147180559945309417"
.LASF14:
	.string	"__ATOMIC_CONSUME 1"
.LASF810:
	.string	"INTPTR_MIN __SEGGER_RTL_INTPTR_MIN"
.LASF959:
	.string	"TEMP_TRANS_FACTOR (0.0625)"
.LASF379:
	.string	"HPMSOC_HAS_HPMSDK_OTP y"
.LASF837:
	.string	"__SEGGER_FPL_VERSION 20801"
.LASF277:
	.string	"__FLT64X_DIG__ 33"
.LASF259:
	.string	"__FLT128_IS_IEC_60559__ 1"
.LASF570:
	.string	"__SEGGER_RTL_INT_FAST8_MIN (-__SEGGER_RTL_INT_FAST8_MAX - 1)"
.LASF626:
	.string	"__SEGGER_RTL_INT_FAST128_WIDTH __SEGGER_RTL_INT128_WIDTH"
.LASF515:
	.string	"__SEGGER_RTL_I64_C(X) __SEGGER_RTL_INT64_C(X)"
.LASF1056:
	.string	"STATE_GPS_WORK (0x20)"
.LASF18:
	.string	"__SIZEOF_LONG_LONG__ 8"
.LASF703:
	.string	"__SEGGER_RTL_SUBNORMALS_FLUSH_TO_ZERO 1"
.LASF168:
	.string	"__DBL_MAX_10_EXP__ 308"
.LASF1134:
	.string	"CORRECT_PRE (0x01)"
.LASF1346:
	.string	"imu_sample_count"
.LASF1020:
	.string	"THRESHOLD_HEADING_RATE_LARGE (3.0)"
.LASF856:
	.string	"isless(x,y) (!isunordered(x, y) && (x) < (y))"
.LASF1318:
	.string	"GNSS_RAW_ST"
.LASF94:
	.string	"__INTMAX_WIDTH__ 64"
.LASF430:
	.string	"__SEGGER_RTL_CORE_HAS_ISA_RVE 0"
.LASF1022:
	.string	"THRESHOLD_GPS_VN_Horizontal (10.0)"
.LASF1354:
	.string	"sqrt"
.LASF34:
	.string	"__SIZE_TYPE__ unsigned int"
.LASF874:
	.string	"M_2_PI 0.63661977236758134308"
.LASF1054:
	.string	"STATE_GNSS_RTK_VALID (0x08)"
.LASF159:
	.string	"__FLT_HAS_DENORM__ 1"
.LASF43:
	.string	"__INT8_TYPE__ signed char"
.LASF1311:
	.string	"gnss_ps"
.LASF807:
	.string	"PTRDIFF_WIDTH __SEGGER_RTL_PTRDIFF_WIDTH"
.LASF926:
	.string	"Versionmain_3_4 (0x00)"
.LASF1125:
	.string	"Y_ACC_AXIS_REVERSE (0x10)"
.LASF555:
	.string	"__SEGGER_RTL_INT64_MIN (-__SEGGER_RTL_INT64_MAX - 1)"
.LASF218:
	.string	"__FLT32_DECIMAL_DIG__ 9"
.LASF282:
	.string	"__FLT64X_DECIMAL_DIG__ 36"
.LASF541:
	.string	"__SEGGER_RTL_PTRDIFF_T __PTRDIFF_TYPE__"
.LASF635:
	.string	"__SEGGER_RTL_UINTMAX_WIDTH __INTMAX_WIDTH__"
.LASF2:
	.string	"__STDC_UTF_16__ 1"
.LASF913:
	.string	"VELN_OBS_IDX 0"
.LASF1247:
	.string	"NO_ALIGN (0x00)"
.LASF119:
	.string	"__UINT8_C(c) c"
.LASF1323:
	.string	"vd_last"
.LASF44:
	.string	"__INT16_TYPE__ short int"
.LASF258:
	.string	"__FLT128_HAS_QUIET_NAN__ 1"
.LASF402:
	.string	"__SEGGER_RTL_H "
.LASF914:
	.string	"VELE_OBS_IDX 1"
.LASF951:
	.string	"F (3.3528131778969144060323814696721e-3)"
.LASF1368:
	.string	"twoant_meas_yaw"
.LASF1305:
	.string	"init_cnt"
.LASF597:
	.string	"__SEGGER_RTL_FLT_MIN __FLT_MIN__"
.LASF647:
	.string	"__SEGGER_RTL_INT32_C(X) __INT32_C(X)"
.LASF547:
	.string	"__SEGGER_RTL_UINT8_MAX __UINT8_MAX__"
.LASF718:
	.string	"NULL 0"
.LASF1094:
	.string	"ID_GYRO_Y_ANNPARA (0x06)"
.LASF297:
	.string	"__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1 1"
.LASF185:
	.string	"__DECIMAL_DIG__ 36"
.LASF59:
	.string	"__INT_FAST8_TYPE__ int"
.LASF377:
	.string	"HPMSOC_HAS_HPMSDK_SYSCTL y"
.LASF4:
	.string	"__STDC_HOSTED__ 1"
.LASF867:
	.string	"M_LOG10E 0.43429448190325182765"
.LASF727:
	.string	"_IOLBF 1"
.LASF476:
	.string	"__SEGGER_RTL_FLOAT64_C_COMPLEX double _Complex"
.LASF461:
	.string	"__SEGGER_RTL_UMULL_X(x0,x1) ({ unsigned __thi, __tlo; __SEGGER_RTL_UMULL(__tlo, __thi, x0, x1); (__SEGGER_RTL_U64)(((__SEGGER_RTL_U64)__thi << 32) + __tlo); })"
.LASF62:
	.string	"__INT_FAST64_TYPE__ long long int"
.LASF465:
	.ascii	"__SEGGER_RTL_UMLAL(lo,hi,x0,x1) do { unsigned __tmp; __asm__"
	.ascii	"(\"mul   %0, %1, %2\" : \"=r\"(__tmp) : \"r\"((unsigned)(x0)"
	.ascii	"), \"r\"((unsigned)(x1)) ); __asm__(\"add   %0, %0, %1\" : \""
	.ascii	"+r\"(lo) : \"r\"(__tmp) ); __asm__(\"sltu  %0, %1, %2\" : \""
	.ascii	"=r\"(__tmp) : \"r"
	.string	"\"((unsigned)lo), \"r\"((unsigned)__tmp)); __asm__(\"add   %0, %0, %1\" : \"+r\"(hi) : \"r\"(__tmp) ); __asm__(\"mulhu %0, %1, %2\" : \"=r\"(__tmp) : \"r\"((unsigned)(x0)), \"r\"((unsigned)(x1)) ); __asm__(\"add   %0, %0, %1\" : \"+r\"(hi) : \"r\"((unsigned)__tmp) ); } while (0)"
.LASF873:
	.string	"M_1_PI 0.31830988618379067154"
.LASF717:
	.string	"__SEGGER_RTL_FILE_IMPL_DEFINED "
.LASF393:
	.string	"HPMSOC_HAS_HPMSDK_BKEY y"
.LASF844:
	.string	"FP_SUBNORMAL __SEGGER_RTL_FP_SUBNORMAL"
.LASF976:
	.string	"PHASE_INTEGRATED_NAVI (0x78)"
.LASF217:
	.string	"__FLT32_MAX_10_EXP__ 38"
.LASF1088:
	.string	"ID_WRITE_INFO (0x12)"
.LASF460:
	.string	"__SEGGER_RTL_SMULL_X(x0,x1) ({ unsigned __thi, __tlo; __SEGGER_RTL_SMULL(__tlo, __thi, x0, x1); (__SEGGER_RTL_I64)(((__SEGGER_RTL_U64)__thi << 32) + __tlo); })"
.LASF782:
	.string	"UINT_LEAST32_WIDTH __SEGGER_RTL_UINT_LEAST32_WIDTH"
.LASF641:
	.string	"__CONCAT1(X,S) __CONCAT(X, S)"
.LASF1130:
	.string	"TEST_MODE (0x04)"
.LASF1167:
	.string	"VMC_HEAD_CORR_VALID (0x08)"
.LASF586:
	.string	"__SEGGER_RTL_WINT_MIN __WINT_MIN__"
.LASF544:
	.string	"__SEGGER_RTL_SIG_ATOMIC_T __SIG_ATOMIC_TYPE__"
.LASF638:
	.string	"__SEGGER_RTL_WCHAR_WIDTH __WCHAR_WIDTH__"
.LASF214:
	.string	"__FLT32_MIN_EXP__ (-125)"
.LASF709:
	.string	"__SEGGER_RTL_SIGNAL_SIGFPE 1"
.LASF588:
	.string	"__SEGGER_RTL_WCHAR_MIN __WCHAR_MIN__"
.LASF741:
	.string	"INT8_MAX __SEGGER_RTL_INT8_MAX"
.LASF979:
	.string	"PHASE_OPEN_LOOP (0x81)"
.LASF565:
	.string	"__SEGGER_RTL_UINT_LEAST32_MAX __UINT_LEAST32_MAX__"
.LASF845:
	.string	"FP_NORMAL __SEGGER_RTL_FP_NORMAL"
.LASF610:
	.string	"__SEGGER_RTL_UINT64_WIDTH 64"
.LASF574:
	.string	"__SEGGER_RTL_UINT_FAST16_MAX __UINT_FAST16_MAX__"
.LASF209:
	.string	"__FLT16_HAS_INFINITY__ 1"
.LASF231:
	.string	"__FLT64_MIN_10_EXP__ (-307)"
.LASF32:
	.string	"__GNUC_EXECUTION_CHARSET_NAME \"UTF-8\""
.LASF908:
	.string	"GBY_STATE_IDX 10"
.LASF517:
	.string	"__SEGGER_RTL_U64 __SEGGER_RTL_UINT64_T"
.LASF133:
	.string	"__INT_FAST64_WIDTH__ 64"
.LASF1:
	.string	"__STDC_VERSION__ 199901L"
.LASF446:
	.string	"__SEGGER_RTL_FLOAT16 _Float16"
.LASF113:
	.string	"__INT32_C(c) c ## L"
.LASF27:
	.string	"__ORDER_BIG_ENDIAN__ 4321"
.LASF1364:
	.string	"sin_roll"
.LASF459:
	.ascii	"__SEGGER_RTL_SMULL(lo,h"
	.string	"i,x0,x1) do { unsigned __tmphi, __tmplo; __asm__(\"mulh %0, %1, %2\" : \"=r\"(__tmphi) : \"r\"((unsigned)(x0)), \"r\"((unsigned)(x1)) ); __asm__(\"mul  %0, %1, %2\" : \"=r\"(__tmplo) : \"r\"((unsigned)(x0)), \"r\"((unsigned)(x1)) ); hi = __tmphi; lo = __tmplo; } while (0)"
.LASF995:
	.string	"THRESHOLD_HDOP (5.0)"
.LASF1365:
	.string	"sin_pitch"
.LASF899:
	.string	"PHIE_STATE_IDX 1"
.LASF920:
	.string	"_CONST_H "
.LASF912:
	.string	"ABZ_STATE_IDX 14"
.LASF986:
	.string	"TIME_START_DELAY (1.0)"
.LASF573:
	.string	"__SEGGER_RTL_INT_FAST16_MIN (-__SEGGER_RTL_INT_FAST16_MAX - 1)"
.LASF1398:
	.string	"E:\\2014902\\HPM6750\\HPM6750_INVProject\\my_project\\app\\gpio\\src\\INAV\\ahrs.c"
.LASF507:
	.string	"__SEGGER_RTL_INT32_T __INT32_TYPE__"
.LASF478:
	.string	"__SEGGER_RTL_PUBLIC_API __attribute__((__weak__))"
.LASF230:
	.string	"__FLT64_MIN_EXP__ (-1021)"
.LASF1294:
	.string	"acc_pitch"
.LASF426:
	.string	"__SEGGER_RTL_RV_QNAN (1<<9)"
.LASF88:
	.string	"__PTRDIFF_WIDTH__ 32"
.LASF1165:
	.string	"WORK_MODE_INS_GNSS_DGNSS (0x18)"
.LASF270:
	.string	"__FLT32X_EPSILON__ 1.1"
.LASF711:
	.string	"__SEGGER_RTL_SIGNAL_SIGINT 3"
.LASF134:
	.string	"__UINT_FAST8_MAX__ 0xffffffffU"
.LASF595:
	.string	"__SEGGER_RTL_SIG_ATOMIC_MIN 0"
.LASF1135:
	.string	"CORRECT_ACT (0x02)"
.LASF1062:
	.string	"STATE_GYRO_TEMP_INVALID (0x01)"
.LASF399:
	.string	"DEBUG 1"
.LASF154:
	.string	"__FLT_MAX__ 1.1"
.LASF54:
	.string	"__INT_LEAST64_TYPE__ long long int"
.LASF569:
	.string	"__SEGGER_RTL_INT_FAST8_MAX __INT_FAST8_MAX__"
.LASF491:
	.string	"__SEGGER_RTL_INCLUDE_AEABI_API 0"
.LASF281:
	.string	"__FLT64X_MAX_10_EXP__ 4932"
.LASF1023:
	.string	"THRESHOLD_HEADING_CORRECT (0.3 * G0)"
.LASF342:
	.string	"HPMSOC_HAS_HPMSDK_PLICSW y"
.LASF129:
	.string	"__INT_FAST16_WIDTH__ 32"
.LASF1014:
	.string	"GNSS_Valid_2S_BUFFER_COUNT (5)"
.LASF1182:
	.string	"VMC_HEAD_CORR_STATE (0x80)"
.LASF511:
	.string	"__SEGGER_RTL_U32 __SEGGER_RTL_UINT32_T"
.LASF759:
	.string	"UINT64_WIDTH __SEGGER_RTL_UINT64_WIDTH"
.LASF415:
	.string	"__SEGGER_RTL_CONF_H "
.LASF138:
	.string	"__INTPTR_MAX__ 0x7fffffff"
.LASF1243:
	.string	"STATIC_SELF_ALIGN (0x06)"
.LASF1228:
	.string	"ALIGN_MODE_BOARD (0x20)"
.LASF135:
	.string	"__UINT_FAST16_MAX__ 0xffffffffU"
.LASF1212:
	.string	"INS_VALID (0x03)"
.LASF1206:
	.string	"ACC_X_FAIL (0x20)"
.LASF1044:
	.string	"ATTITUDE_CORRECT_MAX_VAL (2.5)"
.LASF208:
	.string	"__FLT16_HAS_DENORM__ 1"
.LASF522:
	.string	"__SEGGER_RTL_UINT_LEAST16_T __UINT_LEAST16_TYPE__"
.LASF1156:
	.string	"GNSS_HEAD_VALID (0x02)"
.LASF917:
	.string	"POSE_OBS_IDX 4"
.LASF591:
	.string	"__SEGGER_RTL_INTPTR_MAX __INTPTR_MAX__"
.LASF222:
	.string	"__FLT32_EPSILON__ 1.1"
.LASF512:
	.string	"__SEGGER_RTL_U32_C(X) __SEGGER_RTL_UINT32_C(X)"
.LASF96:
	.string	"__SIG_ATOMIC_MIN__ (-__SIG_ATOMIC_MAX__ - 1)"
.LASF273:
	.string	"__FLT32X_HAS_INFINITY__ 1"
.LASF813:
	.string	"UINTPTR_MAX __SEGGER_RTL_UINTPTR_MAX"
.LASF947:
	.string	"PIDIV2 (1.5707963267948966192313216916398)"
.LASF1055:
	.string	"STATE_GNSS_TIME_VALID (0x10)"
.LASF1146:
	.string	"GNSS_TIME_VALID (0x08)"
.LASF1329:
	.string	"yaw_alig_complete"
.LASF55:
	.string	"__UINT_LEAST8_TYPE__ unsigned char"
.LASF946:
	.string	"WIE (7.292115147e-5)"
.LASF205:
	.string	"__FLT16_MIN__ 1.1"
.LASF321:
	.string	"__riscv_muldiv 1"
.LASF1254:
	.string	"long int"
.LASF942:
	.string	"MAX_MAT_DIM (7)"
.LASF447:
	.string	"__SEGGER_RTL_INCLUDE_GNU_FP16_API 1"
.LASF269:
	.string	"__FLT32X_MIN__ 1.1"
.LASF601:
	.string	"__SEGGER_RTL_LDBL_MIN __LDBL_MIN__"
.LASF1388:
	.string	"q3q3"
.LASF1317:
	.string	"twoAntYaw_update"
.LASF1242:
	.string	"DYNAMIC_TRANSFER_ALIGN (0x07)"
.LASF274:
	.string	"__FLT32X_HAS_QUIET_NAN__ 1"
.LASF996:
	.string	"THRESHOLD_VALID_3S (30)"
.LASF918:
	.string	"POSD_OBS_IDX 5"
.LASF584:
	.string	"__SEGGER_RTL_SIZE_MAX __SIZE_MAX__"
.LASF831:
	.string	"WCHAR_WIDTH __SEGGER_RTL_WCHAR_WIDTH"
.LASF246:
	.string	"__FLT128_MIN_EXP__ (-16381)"
.LASF233:
	.string	"__FLT64_MAX_10_EXP__ 308"
.LASF93:
	.string	"__UINTMAX_C(c) c ## ULL"
.LASF862:
	.string	"math_errhandling 0"
.LASF31:
	.string	"__SIZEOF_POINTER__ 4"
.LASF51:
	.string	"__INT_LEAST8_TYPE__ signed char"
.LASF1114:
	.string	"ID_WRITE_INFO_SUCCESS (0x02)"
.LASF445:
	.string	"__SEGGER_RTL_TYPESET __riscv_xlen"
.LASF300:
	.string	"__GCC_ATOMIC_BOOL_LOCK_FREE 2"
.LASF374:
	.string	"HPMSOC_HAS_HPMSDK_I2C y"
.LASF1234:
	.string	"VEL_FIT (0x02)"
.LASF288:
	.string	"__FLT64X_HAS_DENORM__ 1"
.LASF708:
	.string	"__SEGGER_RTL_SIGNAL_SIGABRT 0"
.LASF1380:
	.string	"wie_ned"
.LASF1218:
	.string	"MIN_AIR_HEIGHT (-400.0)"
.LASF1221:
	.string	"THRESHOLD_KAL_COUNT_FOR_PREDICT (600)"
.LASF216:
	.string	"__FLT32_MAX_EXP__ 128"
.LASF1358:
	.string	"ahrs_update"
.LASF331:
	.string	"__riscv_zifencei 2000000"
.LASF770:
	.string	"INT_LEAST16_WIDTH __SEGGER_RTL_INT_LEAST16_WIDTH"
.LASF1102:
	.string	"CMD_WRITE_INFO (0x02)"
.LASF33:
	.string	"__GNUC_WIDE_EXECUTION_CHARSET_NAME \"UTF-32LE\""
.LASF263:
	.string	"__FLT32X_MIN_10_EXP__ (-307)"
.LASF652:
	.string	"__SEGGER_RTL_UINTMAX_C(X) __UINTMAX_C(X)"
.LASF41:
	.string	"__CHAR32_TYPE__ long unsigned int"
.LASF1375:
	.string	"quat_new"
.LASF1124:
	.string	"X_ACC_AXIS_REVERSE (0x08)"
.LASF737:
	.string	"__bool_true_false_are_defined 1"
.LASF178:
	.string	"__DBL_IS_IEC_60559__ 1"
.LASF692:
	.string	"__SEGGER_RTL_I64_H(X) ((__SEGGER_RTL_I32)((__SEGGER_RTL_I64)(X) >> 32))"
.LASF136:
	.string	"__UINT_FAST32_MAX__ 0xffffffffU"
.LASF891:
	.string	"fac (1.0/12.0)"
.LASF151:
	.string	"__FLT_MAX_EXP__ 128"
.LASF17:
	.string	"__SIZEOF_LONG__ 4"
.LASF1391:
	.string	"wnorm"
.LASF793:
	.string	"INT_FAST32_WIDTH __SEGGER_RTL_INT_FAST32_WIDTH"
.LASF723:
	.string	"SEEK_CUR 1"
.LASF1321:
	.string	"vn_last"
.LASF990:
	.string	"TIME_FINE_ALIGN (300.0)"
.LASF905:
	.string	"DPSE_STATE_IDX 7"
.LASF252:
	.string	"__FLT128_NORM_MAX__ 1.1"
.LASF21:
	.string	"__SIZEOF_DOUBLE__ 8"
.LASF114:
	.string	"__INT_LEAST32_WIDTH__ 32"
.LASF1034:
	.string	"HEADING_CORRECT_COUNT (5)"
.LASF640:
	.string	"__CONCAT(X,S) X ## S"
.LASF343:
	.string	"HPMSOC_HAS_HPMSDK_GPIOM y"
.LASF823:
	.string	"UINT8_C(x) __SEGGER_RTL_UINT8_C(x)"
.LASF861:
	.string	"MATH_ERREXCEPT 2"
.LASF449:
	.string	"__SEGGER_RTL_FP_ABI 0"
.LASF500:
	.string	"__SEGGER_RTL_I8 __SEGGER_RTL_INT8_T"
.LASF489:
	.string	"__SEGGER_RTL_SIDE_BY_SIDE 0"
.LASF806:
	.string	"PTRDIFF_MAX __SEGGER_RTL_PTRDIFF_MAX"
.LASF962:
	.string	"COLD_START_TEMP_DIFF (2.5)"
.LASF1048:
	.string	"HEADINGRATE_BUFFER_SIZE (200)"
.LASF416:
	.string	"__SEGGER_RTL_RISCV_CONF_H "
.LASF1280:
	.string	"Q4_ST"
.LASF1107:
	.string	"LEN_ANN (607)"
.LASF320:
	.string	"__riscv_div 1"
.LASF434:
	.string	"__SEGGER_RTL_CORE_HAS_DIV 1"
.LASF150:
	.string	"__FLT_MIN_10_EXP__ (-37)"
.LASF398:
	.string	"SD_FATFS_ENABLE 1"
.LASF514:
	.string	"__SEGGER_RTL_I64 __SEGGER_RTL_INT64_T"
.LASF1285:
	.string	"mag_const_bias"
.LASF153:
	.string	"__FLT_DECIMAL_DIG__ 9"
.LASF1392:
	.string	"dyaw"
.LASF1150:
	.string	"GNSS_NAVI (0x03)"
.LASF473:
	.string	"__SEGGER_RTL_DIVMOD_U32(Q,R,N,D) do { Q = (N) / (D); __SEGGER_RTL_KILL(Q); R = (N) - (Q)*(D); } while (0)"
.LASF1255:
	.string	"signed char"
.LASF740:
	.string	"INT8_MIN __SEGGER_RTL_INT8_MIN"
.LASF1298:
	.string	"AHRS_ST"
.LASF1258:
	.string	"uint8_t"
.LASF1372:
	.string	"mag_rot_err"
.LASF190:
	.string	"__LDBL_EPSILON__ 1.1"
.LASF982:
	.string	"TIME_NAVI (0.004)"
.LASF294:
	.string	"__GNUC_STDC_INLINE__ 1"
.LASF469:
	.string	"__SEGGER_RTL_REQUEST_INLINE __inline__"
.LASF1302:
	.string	"heading_cnt"
.LASF929:
	.string	"Versiontail (0x01)"
.LASF161:
	.string	"__FLT_HAS_QUIET_NAN__ 1"
.LASF7:
	.string	"__GNUC_PATCHLEVEL__ 0"
.LASF303:
	.string	"__GCC_ATOMIC_CHAR32_T_LOCK_FREE 2"
.LASF984:
	.string	"TIME_FILTER (0.2)"
.LASF1308:
	.string	"week"
.LASF120:
	.string	"__UINT_LEAST16_MAX__ 0xffff"
.LASF799:
	.string	"UINT_FAST16_MAX __SEGGER_RTL_UINT_FAST16_MAX"
.LASF1315:
	.string	"pvt_update"
.LASF702:
	.string	"__SEGGER_RTL_SUBNORMALS_READ_AS_ZERO 1"
.LASF1207:
	.string	"ACC_Y_FAIL (0x10)"
.LASF1191:
	.string	"ACC_X_VALID (0x20)"
.LASF833:
	.string	"SIG_ATOMIC_MAX __SEGGER_RTL_SIG_ATOMIC_MAX"
.LASF1274:
	.string	"run_time"
.LASF148:
	.string	"__FLT_DIG__ 6"
.LASF963:
	.string	"DEFAULT_LATI (39.898717)"
.LASF1172:
	.string	"TRUE_AIR_V_VALID (0x04)"
.LASF797:
	.string	"UINT_FAST8_MAX __SEGGER_RTL_UINT_FAST8_MAX"
.LASF131:
	.string	"__INT_FAST32_WIDTH__ 32"
.LASF768:
	.string	"INT_LEAST16_MIN __SEGGER_RTL_INT_LEAST16_MIN"
.LASF992:
	.string	"THRESHOLD_ACC (20.0 * G)"
.LASF672:
	.string	"__SEGGER_RTL_ATOMIC_SYNCHRONIZE() __SEGGER_RTL_X_atomic_synchronize()"
.LASF65:
	.string	"__UINT_FAST32_TYPE__ unsigned int"
.LASF1256:
	.string	"unsigned char"
.LASF3:
	.string	"__STDC_UTF_32__ 1"
.LASF1031:
	.string	"POSITION_CORRECT_COUNT (1)"
.LASF152:
	.string	"__FLT_MAX_10_EXP__ 38"
.LASF453:
	.string	"__SEGGER_RTL_FLT_SELECT(HEX,DEC) HEX"
.LASF1248:
	.string	"ALIGNING (0x01)"
.LASF456:
	.string	"__SEGGER_RTL_WORKAROUND_CLANG_AS_SYMBOL_BUG 0"
.LASF576:
	.string	"__SEGGER_RTL_INT_FAST32_MIN (-__SEGGER_RTL_INT_FAST32_MAX - 1)"
.LASF776:
	.string	"INT_LEAST64_WIDTH __SEGGER_RTL_INT_LEAST64_WIDTH"
.LASF130:
	.string	"__INT_FAST32_MAX__ 0x7fffffff"
.LASF10:
	.string	"__ATOMIC_SEQ_CST 5"
.LASF763:
	.string	"UINTMAX_MAX __SEGGER_RTL_UINTMAX_MAX"
.LASF1041:
	.string	"HEADING_VALID_COUNT_FOR_CORRECT (5)"
.LASF766:
	.string	"INT_LEAST8_MAX __SEGGER_RTL_INT_LEAST8_MAX"
.LASF775:
	.string	"INT_LEAST64_MAX __SEGGER_RTL_INT_LEAST64_MAX"
.LASF706:
	.string	"__SEGGER_RTL_CORE_HAS_UDIVM_X 0"
.LASF1296:
	.string	"tilt_complete"
.LASF898:
	.string	"PHIN_STATE_IDX 0"
.LASF1208:
	.string	"ACC_Z_FAIL (0x40)"
.LASF371:
	.string	"HPMSOC_HAS_HPMSDK_USB y"
.LASF847:
	.string	"FP_NAN __SEGGER_RTL_FP_NAN"
.LASF413:
	.string	"__SEGGER_RTL_STDC_VERSION_CPP17 201703L"
.LASF103:
	.string	"__UINT16_MAX__ 0xffff"
.LASF394:
	.string	"HPMSOC_HAS_HPMSDK_BMON y"
.LASF1310:
	.string	"gnss_vs"
.LASF414:
	.string	"__SEGGER_RTL_STDC_VERSION __STDC_VERSION__"
.LASF368:
	.string	"HPMSOC_HAS_HPMSDK_JPEG y"
.LASF633:
	.string	"__SEGGER_RTL_UINTPTR_WIDTH __INTPTR_WIDTH__"
.LASF412:
	.string	"__SEGGER_RTL_STDC_VERSION_CPP14 201402L"
.LASF174:
	.string	"__DBL_DENORM_MIN__ ((double)1.1)"
.LASF909:
	.string	"GBZ_STATE_IDX 11"
.LASF713:
	.string	"__SEGGER_RTL_SIGNAL_SIGTERM 5"
.LASF548:
	.string	"__SEGGER_RTL_INT16_MAX __INT16_MAX__"
.LASF183:
	.string	"__LDBL_MAX_EXP__ 16384"
.LASF1303:
	.string	"heading_have_get"
.LASF19:
	.string	"__SIZEOF_SHORT__ 2"
.LASF490:
	.string	"__SEGGER_RTL_FORCE_SOFT_FLOAT 0"
.LASF429:
	.string	"__SEGGER_RTL_UNREACHABLE() __builtin_unreachable()"
.LASF596:
	.string	"__SEGGER_RTL_FLT_MAX __FLT_MAX__"
.LASF1291:
	.string	"_Bool"
.LASF1142:
	.string	"GNSS_SINGLE_POINT (0x01)"
.LASF1171:
	.string	"INDICATED_AIR_V_VALID (0x02)"
.LASF651:
	.string	"__SEGGER_RTL_INTMAX_C(X) __INTMAX_C(X)"
.LASF981:
	.string	"PHASE_COMPASS (0x83)"
.LASF781:
	.string	"UINT_LEAST32_MAX __SEGGER_RTL_UINT_LEAST32_MAX"
.LASF599:
	.string	"__SEGGER_RTL_DBL_MIN __DBL_MIN__"
.LASF1144:
	.string	"GNSS_RTK (0x03)"
.LASF1319:
	.string	"ned_orig"
.LASF1161:
	.string	"WORK_MODE_INS_AIR (0x08)"
.LASF758:
	.string	"UINT64_MAX __SEGGER_RTL_UINT64_MAX"
.LASF1121:
	.string	"X_GYRO_AXIS_REVERSE (0x01)"
.LASF739:
	.string	"__SEGGER_RTL_STDINT_H "
.LASF750:
	.string	"INT64_MAX __SEGGER_RTL_INT64_MAX"
.LASF312:
	.string	"__PRAGMA_REDEFINE_EXTNAME 1"
.LASF552:
	.string	"__SEGGER_RTL_INT32_MIN (-__SEGGER_RTL_INT32_MAX - 1)"
.LASF901:
	.string	"DVEN_STATE_IDX 3"
.LASF70:
	.string	"__SCHAR_MAX__ 0x7f"
.LASF1397:
	.string	"GNU C99 13.2.0 -fmessage-length=0 -std=gnu99 -march=rv32imac_zicsr_zifencei -mabi=ilp32 -mrelax -gdwarf-4 -g3 -gpubnames -fomit-frame-pointer -ffunction-sections -fdata-sections -fshort-enums -fno-signed-char -fno-common"
.LASF796:
	.string	"INT_FAST64_WIDTH __SEGGER_RTL_INT_FAST64_WIDTH"
.LASF298:
	.string	"__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2 1"
.LASF769:
	.string	"INT_LEAST16_MAX __SEGGER_RTL_INT_LEAST16_MAX"
.LASF733:
	.string	"__SEGGER_RTL_STDBOOL_H "
.LASF678:
	.string	"__SEGGER_RTL_RODATA_IS_RW 0"
.LASF1281:
	.string	"gyro"
.LASF474:
	.string	"__SEGGER_RTL_DIVMOD_U64(Q,R,N,D) do { Q = (N) / (D); __SEGGER_RTL_KILL(Q); R = (N) - (Q)*(D); } while (0)"
.LASF66:
	.string	"__UINT_FAST64_TYPE__ long long unsigned int"
.LASF662:
	.string	"__SEGGER_RTL_FORMAT_INT_WIDTH"
.LASF525:
	.string	"__SEGGER_RTL_INT_LEAST64_T __INT_LEAST64_TYPE__"
.LASF1002:
	.string	"THRESHOLD_POS_ERROR (3.0)"
.LASF720:
	.string	"__SEGGER_RTL_VA_LIST_DEFINED "
.LASF1103:
	.string	"CMD_READ_IMUDATA (0x04)"
.LASF1340:
	.string	"ahrs_titl_alig"
.LASF925:
	.string	"Versionmain_5_7 (0x01)"
.LASF549:
	.string	"__SEGGER_RTL_INT16_MIN (-__SEGGER_RTL_INT16_MAX - 1)"
.LASF928:
	.string	"Versionmain (Versionmain_0_2 + Versionmain_3_4 + Versionmain_5_7)"
.LASF367:
	.string	"HPMSOC_HAS_HPMSDK_PDMA y"
.LASF227:
	.string	"__FLT32_IS_IEC_60559__ 1"
.LASF25:
	.string	"__BIGGEST_ALIGNMENT__ 16"
.LASF8:
	.string	"__VERSION__ \"13.2.0\""
.LASF623:
	.string	"__SEGGER_RTL_INT_FAST16_WIDTH __INT_FAST16_WIDTH__"
.LASF271:
	.string	"__FLT32X_DENORM_MIN__ 1.1"
.LASF896:
	.string	"MAX_STATE_DIM 15"
.LASF201:
	.string	"__FLT16_MAX_10_EXP__ 4"
.LASF1190:
	.string	"GYRO_Z_VALID (0x01)"
.LASF363:
	.string	"HPMSOC_HAS_HPMSDK_TRGM y"
.LASF455:
	.string	"__SEGGER_RTL_ATOMIC_IS_LOCK_FREE(S,P) __SEGGER_RTL_atomic_is_lock_free(S, P)"
.LASF900:
	.string	"PHID_STATE_IDX 2"
.LASF1278:
	.string	"delt_yaw"
.LASF854:
	.string	"isgreater(x,y) (!isunordered(x, y) && (x) > (y))"
.LASF670:
	.string	"__SEGGER_RTL_ATOMIC_LOCK() __SEGGER_RTL_X_atomic_lock()"
.LASF930:
	.string	"VerSionSoft (Versionmain * 0x100 + Versiontail)"
.LASF102:
	.string	"__UINT8_MAX__ 0xff"
.LASF1087:
	.string	"ID_WRITE_IMUDATA (0x10)"
.LASF598:
	.string	"__SEGGER_RTL_DBL_MAX __DBL_MAX__"
.LASF485:
	.string	"__WIDTH_LONG_LONG 2"
.LASF937:
	.string	"param_gyc_6089_AccX_fu "
.LASF1106:
	.string	"LEN_FULLTEMP (967)"
.LASF451:
	.string	"__SEGGER_RTL_SIZEOF_LDOUBLE 16"
.LASF520:
	.string	"__SEGGER_RTL_UINT_LEAST8_T __UINT_LEAST8_TYPE__"
.LASF175:
	.string	"__DBL_HAS_DENORM__ 1"
.LASF654:
	.string	"__SEGGER_RTL_ATOMIC_U16 __SEGGER_RTL_U16"
.LASF911:
	.string	"ABY_STATE_IDX 13"
.LASF818:
	.string	"WINT_WIDTH __SEGGER_RTL_WINT_WIDTH"
.LASF612:
	.string	"__SEGGER_RTL_INT_LEAST8_WIDTH __INT_LEAST8_WIDTH__"
.LASF1366:
	.string	"cos_roll"
.LASF1113:
	.string	"ID_WRITE_IMUDATA_SUCCESS (0x01)"
.LASF869:
	.string	"M_LN10 2.30258509299404568402"
.LASF69:
	.string	"__GXX_ABI_VERSION 1018"
.LASF109:
	.string	"__INT_LEAST16_MAX__ 0x7fff"
.LASF707:
	.string	"__SEGGER_RTL_CORE_HAS_IDIVM_X 0"
.LASF1133:
	.string	"NO_CORRECT_ACTION (0x00)"
.LASF248:
	.string	"__FLT128_MAX_EXP__ 16384"
.LASF691:
	.string	"__SEGGER_RTL_U64_MK(H,L) (((__SEGGER_RTL_U64)(__SEGGER_RTL_U32)(H) << 32) + (__SEGGER_RTL_U32)(L))"
.LASF1110:
	.string	"LEN_READ (7)"
.LASF1196:
	.string	"HEAD_VALID (0x02)"
.LASF687:
	.string	"__SEGGER_RTL_SCALED_INTEGER 0"
.LASF1333:
	.string	"need_adjust"
.LASF559:
	.string	"__SEGGER_RTL_UINT_LEAST8_MAX __UINT_LEAST8_MAX__"
.LASF954:
	.string	"RE (6378137.0)"
.LASF751:
	.string	"INT64_WIDTH __SEGGER_RTL_INT64_WIDTH"
.LASF452:
	.string	"__SEGGER_RTL_NAN_FORMAT __SEGGER_RTL_NAN_FORMAT_IEEE"
.LASF123:
	.string	"__UINT32_C(c) c ## UL"
.LASF382:
	.string	"HPMSOC_HAS_HPMSDK_PSEC y"
.LASF631:
	.string	"__SEGGER_RTL_UINT_FAST128_WIDTH __SEGGER_RTL_UINT128_WIDTH"
.LASF944:
	.string	"YES (0x55)"
.LASF1065:
	.string	"STATE_ACC_INVALID (0x08)"
.LASF1353:
	.string	"atan2"
.LASF731:
	.string	"__SEGGER_RTL_FILE_DEFINED "
.LASF904:
	.string	"DPSN_STATE_IDX 6"
.LASF236:
	.string	"__FLT64_NORM_MAX__ 1.1"
.LASF842:
	.string	"__SEGGER_RTL_FP_NAN 0x04"
.LASF202:
	.string	"__FLT16_DECIMAL_DIG__ 5"
.LASF1399:
	.string	"E:\\2014902\\HPM6750\\HPM6750_INVProject\\my_project\\app\\gpio\\hpm6750_flash_xip_debug\\segger_embedded_studio"
.LASF575:
	.string	"__SEGGER_RTL_INT_FAST32_MAX __INT_FAST32_MAX__"
.LASF1176:
	.string	"VMC_GNSS_FAIL (0x00)"
.LASF955:
	.string	"INVRE (1.5678559428873979972521756744955e-7)"
.LASF441:
	.string	"__SEGGER_RTL_PREFER_BRANCH_FREE_CODE 0"
.LASF715:
	.string	"__SEGGER_RTL_VERSION 42601"
.LASF1080:
	.string	"NUM_B (5)"
.LASF375:
	.string	"HPMSOC_HAS_HPMSDK_SDP y"
.LASF880:
	.string	"HUGE_VAL __SEGGER_RTL_HUGE_VAL"
.LASF989:
	.string	"TIME_FAST_ALIGN (30.0)"
.LASF463:
	.string	"__SEGGER_RTL_UMULL_HI(x0,x1) ({ unsigned __product; __asm__(\"mulhu %0, %1, %2\" : \"=r\"(__product) : \"r\"((unsigned)(x0)), \"r\"((unsigned)(x1))); __product; })"
.LASF696:
	.string	"__SEGGER_RTL_NAN __builtin_nanf(\"0x7fc00000\")"
.LASF424:
	.string	"__SEGGER_RTL_RV_POS_INF (1<<7)"
.LASF305:
	.string	"__GCC_ATOMIC_SHORT_LOCK_FREE 2"
.LASF934:
	.string	"DEVICE_GYRO_TYPE_FOG "
.LASF1369:
	.string	"twoant_yaw_delayed"
.LASF1349:
	.string	"dAng"
.LASF780:
	.string	"UINT_LEAST16_WIDTH __SEGGER_RTL_UINT_LEAST16_WIDTH"
.LASF1086:
	.string	"ID_READ (0x11)"
.LASF700:
	.string	"__SEGGER_RTL_HIDE(X) __c_ ##X"
.LASF859:
	.string	"isunordered(x,y) (isnan(x) || isnan(y))"
.LASF309:
	.string	"__GCC_ATOMIC_TEST_AND_SET_TRUEVAL 1"
.LASF1085:
	.string	"Install_ANGLE_1 (0.0)"
.LASF553:
	.string	"__SEGGER_RTL_UINT32_MAX __UINT32_MAX__"
.LASF332:
	.string	"__ELF__ 1"
.LASF397:
	.string	"HPMSOC_HAS_HPMSDK_PMP y"
.LASF1283:
	.string	"acc_const_bias"
.LASF76:
	.string	"__WCHAR_MIN__ (-__WCHAR_MAX__ - 1)"
.LASF771:
	.string	"INT_LEAST32_MIN __SEGGER_RTL_INT_LEAST32_MIN"
.LASF600:
	.string	"__SEGGER_RTL_LDBL_MAX __LDBL_MAX__"
.LASF1152:
	.string	"INS_STANDBY (0x01)"
.LASF193:
	.string	"__LDBL_HAS_INFINITY__ 1"
.LASF389:
	.string	"HPMSOC_HAS_HPMSDK_BUTN y"
.LASF345:
	.string	"HPMSOC_HAS_HPMSDK_ADC16 y"
.LASF1396:
	.string	"gyro_yaw_have_get"
.LASF958:
	.string	"NUM_TEMP_SAM (12)"
.LASF1018:
	.string	"TIME_NAVI_RESTORE_FROM_VALID (5.0)"
.LASF265:
	.string	"__FLT32X_MAX_10_EXP__ 308"
.LASF372:
	.string	"HPMSOC_HAS_HPMSDK_SDXC y"
.LASF106:
	.string	"__INT_LEAST8_MAX__ 0x7f"
.LASF482:
	.string	"__SEGGER_RTL_NAN_FORMAT_COMPACT 2"
.LASF425:
	.string	"__SEGGER_RTL_RV_SNAN (1<<8)"
.LASF1101:
	.string	"CMD_WRITE_IMUDATA (0x01)"
.LASF1314:
	.string	"track"
.LASF1096:
	.string	"ID_ACC_X_ANNPARA (0x08)"
.LASF339:
	.string	"HPMSOC_HAS_HPMSDK_GPIO y"
.LASF1210:
	.string	"ALIGN_FAIL (0x08)"
.LASF907:
	.string	"GBX_STATE_IDX 9"
.LASF266:
	.string	"__FLT32X_DECIMAL_DIG__ 17"
.LASF1275:
	.string	"VNED_ST"
.LASF676:
	.string	"__SEGGER_RTL_BitcastToU64(X) __SEGGER_RTL_BitcastToU64_inline(X)"
.LASF118:
	.string	"__UINT_LEAST8_MAX__ 0xff"
.LASF1338:
	.string	"gnss_ang_delayed"
.LASF968:
	.string	"DEFAULT_VE (0.