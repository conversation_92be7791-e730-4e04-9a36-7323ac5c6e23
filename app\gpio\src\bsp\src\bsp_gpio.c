#include "appmain.h"
#include "bsp_gpio.h"




void bsp_gpio_init(void)
{
	//////////////////////////////////////////////////////////////////
	//
	//////////////////////////////////////////////////////////////////
	//LED SRAM
	HPM_IOC->PAD[IOC_PAD_PA10].FUNC_CTL = IOC_PA10_FUNC_CTL_GPIO_A_10;
    HPM_IOC->PAD[IOC_PAD_PA10].PAD_CTL = IOC_PAD_PAD_CTL_DS_SET(1) | IOC_PAD_PAD_CTL_PE_SET(1) | 0x08 | IOC_PAD_PAD_CTL_PS_SET(0) | IOC_PAD_PAD_CTL_MS_SET(0);
    gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOA, LED_SRAM_IO_PIN, gpiom_soc_gpio0);
    gpio_set_pin_output(HPM_GPIO0, GPIO_OE_GPIOA, LED_SRAM_IO_PIN);
	
	//Wheel LED
	HPM_IOC->PAD[IOC_PAD_PA24].FUNC_CTL = IOC_PA24_FUNC_CTL_GPIO_A_24;
    HPM_IOC->PAD[IOC_PAD_PA24].PAD_CTL = IOC_PAD_PAD_CTL_DS_SET(1) | IOC_PAD_PAD_CTL_MS_SET(0) | IOC_PAD_PAD_CTL_PE_SET(1) | 0x08 | IOC_PAD_PAD_CTL_PS_SET(0);
    gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOA, LED_WHEEL_IO_PIN, gpiom_soc_gpio0);
    gpio_set_pin_output(HPM_GPIO0, GPIO_OE_GPIOA, LED_WHEEL_IO_PIN);
	
	//State LED
	HPM_IOC->PAD[IOC_PAD_PA23].FUNC_CTL = IOC_PA23_FUNC_CTL_GPIO_A_23;
    HPM_IOC->PAD[IOC_PAD_PA23].PAD_CTL = IOC_PAD_PAD_CTL_DS_SET(1) | IOC_PAD_PAD_CTL_MS_SET(0) | IOC_PAD_PAD_CTL_PE_SET(1) | 0x08 | IOC_PAD_PAD_CTL_PS_SET(0);
    gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOA, LED_STATE_IO_PIN, gpiom_soc_gpio0);
    gpio_set_pin_output(HPM_GPIO0, GPIO_OE_GPIOA, LED_STATE_IO_PIN);
	
	//CAN 1 
	HPM_IOC->PAD[IOC_PAD_PB18].FUNC_CTL = IOC_PB18_FUNC_CTL_GPIO_B_18;
    HPM_IOC->PAD[IOC_PAD_PB18].PAD_CTL = IOC_PAD_PAD_CTL_DS_SET(1) | IOC_PAD_PAD_CTL_MS_SET(0) | IOC_PAD_PAD_CTL_PE_SET(1) | 0x08 | IOC_PAD_PAD_CTL_PS_SET(0);
    gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOB, CAN1_IO_PIN, gpiom_soc_gpio0);
    gpio_set_pin_output(HPM_GPIO0, GPIO_OE_GPIOB, CAN1_IO_PIN);
	
	//CAN 2 
	//gpio_mode_set(CAN2_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,CAN2_IO_PIN);
	//gpio_output_options_set(CAN2_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,CAN2_IO_PIN);
	
	//ETH 
	HPM_IOC->PAD[IOC_PAD_PA11].FUNC_CTL = IOC_PA11_FUNC_CTL_GPIO_A_11;
    HPM_IOC->PAD[IOC_PAD_PA11].PAD_CTL = IOC_PAD_PAD_CTL_DS_SET(1) | IOC_PAD_PAD_CTL_MS_SET(0) | IOC_PAD_PAD_CTL_PE_SET(1) | 0x08 | IOC_PAD_PAD_CTL_PS_SET(0);
    gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOA, ETH_RST_IO_PIN, gpiom_soc_gpio0);
    gpio_set_pin_output(HPM_GPIO0, GPIO_OE_GPIOA, ETH_RST_IO_PIN);
	
	//CAN1 STB IO
	HPM_IOC->PAD[IOC_PAD_PB14].FUNC_CTL = IOC_PB14_FUNC_CTL_GPIO_B_14;
    HPM_IOC->PAD[IOC_PAD_PB14].PAD_CTL = IOC_PAD_PAD_CTL_DS_SET(1) | IOC_PAD_PAD_CTL_MS_SET(0) | IOC_PAD_PAD_CTL_PE_SET(1) | 0x08 | IOC_PAD_PAD_CTL_PS_SET(0);
    gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOB, CAN1_STB_IO_PIN, gpiom_soc_gpio0);
    gpio_set_pin_output(HPM_GPIO0, GPIO_OE_GPIOB, CAN1_STB_IO_PIN);

	
	//CAN2 STB IO
	HPM_IOC->PAD[IOC_PAD_PA14].FUNC_CTL = IOC_PA14_FUNC_CTL_GPIO_A_14;
    HPM_IOC->PAD[IOC_PAD_PA14].PAD_CTL = IOC_PAD_PAD_CTL_DS_SET(1) | IOC_PAD_PAD_CTL_MS_SET(0) | IOC_PAD_PAD_CTL_PE_SET(1) | 0x08 | IOC_PAD_PAD_CTL_PS_SET(0);
    gpiom_set_pin_controller(HPM_GPIOM, GPIOM_ASSIGN_GPIOA, CAN2_STB_IO_PIN, gpiom_soc_gpio0);
    gpio_set_pin_output(HPM_GPIO0, GPIO_OE_GPIOA, CAN2_STB_IO_PIN);
	
	
	//////////////////////////////////////////////////////////////////
	//PWM
	//////////////////////////////////////////////////////////////////
	HPM_IOC->PAD[IOC_PAD_PE27].FUNC_CTL = IOC_PE27_FUNC_CTL_GPTMR4_COMP_1;
	//gpio_mode_set(PWM_IO_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, PWM_IO_PIN);
	//gpio_output_options_set(PWM_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,PWM_IO_PIN);
	//gpio_af_set(PWM_IO_PORT, GPIO_AF_1, PWM_IO_PIN);
}

