[{"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o CMakeFiles\\app.dir\\src\\gpio.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\my_project\\app\\gpio\\src\\gpio.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\my_project\\app\\gpio\\src\\gpio.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\E_\\2014902\\HPM6750\\sdk_env_v1.6.0\\my_project\\boards\\hpm6750\\pinmux.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\my_project\\boards\\hpm6750\\pinmux.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\my_project\\boards\\hpm6750\\pinmux.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\E_\\2014902\\HPM6750\\sdk_env_v1.6.0\\my_project\\boards\\hpm6750\\board.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\my_project\\boards\\hpm6750\\board.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\my_project\\boards\\hpm6750\\board.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM6700\\HPM6750\\toolchains\\reset.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\toolchains\\reset.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\toolchains\\reset.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM6700\\HPM6750\\toolchains\\trap.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\toolchains\\trap.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\toolchains\\trap.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM6700\\HPM6750\\system.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\system.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\system.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM6700\\HPM6750\\hpm_sysctl_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\hpm_sysctl_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\hpm_sysctl_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM6700\\HPM6750\\hpm_l1c_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\hpm_l1c_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\hpm_l1c_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM6700\\HPM6750\\hpm_clock_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\hpm_clock_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\hpm_clock_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM6700\\HPM6750\\hpm_otp_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\hpm_otp_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\hpm_otp_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM6700\\HPM6750\\boot\\hpm_bootheader.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\boot\\hpm_bootheader.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\boot\\hpm_bootheader.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_uart_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_uart_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_uart_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_femc_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_femc_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_femc_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_sdp_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_sdp_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_sdp_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_lcdc_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_lcdc_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_lcdc_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_i2c_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_i2c_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_i2c_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_pmp_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pmp_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pmp_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_rng_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_rng_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_rng_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_gpio_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_gpio_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_gpio_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_spi_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_spi_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_spi_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_pdma_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pdma_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pdma_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_wdg_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_wdg_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_wdg_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_dma_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_dma_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_dma_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_gptmr_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_gptmr_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_gptmr_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_pwm_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pwm_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pwm_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_pllctl_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pllctl_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pllctl_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_usb_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_usb_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_usb_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_rtc_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_rtc_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_rtc_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_acmp_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_acmp_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_acmp_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_i2s_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_i2s_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_i2s_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_dao_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_dao_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_dao_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_pdm_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pdm_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pdm_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_vad_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_vad_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_vad_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_cam_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_cam_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_cam_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_can_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_can_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_can_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_jpeg_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_jpeg_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_jpeg_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_enet_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_enet_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_enet_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_sdxc_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_sdxc_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_sdxc_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_adc12_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_adc12_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_adc12_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_adc16_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_adc16_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_adc16_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_pcfg_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pcfg_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_pcfg_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_ptpc_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_ptpc_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_ptpc_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_mchtmr_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_mchtmr_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_mchtmr_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_tamp_drv.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_tamp_drv.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\drivers\\src\\hpm_tamp_drv.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\utils\\hpm_sbrk.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\utils\\hpm_sbrk.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\utils\\hpm_sbrk.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\utils\\hpm_swap.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\utils\\hpm_swap.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\utils\\hpm_swap.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\utils\\hpm_ffssi.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\utils\\hpm_ffssi.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\utils\\hpm_ffssi.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\utils\\hpm_crc32.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\utils\\hpm_crc32.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\utils\\hpm_crc32.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\components\\debug_console\\hpm_debug_console.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\components\\debug_console\\hpm_debug_console.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\components\\debug_console\\hpm_debug_console.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\middleware\\fatfs\\src\\common\\ff.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\fatfs\\src\\common\\ff.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\fatfs\\src\\common\\ff.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\middleware\\fatfs\\src\\common\\ffunicode.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\fatfs\\src\\common\\ffunicode.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\fatfs\\src\\common\\ffunicode.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\middleware\\fatfs\\src\\portable\\diskio.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\fatfs\\src\\portable\\diskio.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\fatfs\\src\\portable\\diskio.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\middleware\\fatfs\\src\\portable\\sdxc\\hpm_sdmmc_disk.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\fatfs\\src\\portable\\sdxc\\hpm_sdmmc_disk.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\fatfs\\src\\portable\\sdxc\\hpm_sdmmc_disk.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_host.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_host.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_host.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_common.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_common.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_common.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_sd.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_sd.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_sd.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_emmc.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_emmc.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\hpm_sdmmc\\lib\\hpm_sdmmc_emmc.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\middleware\\hpm_sdmmc\\port\\hpm_sdmmc_port.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\hpm_sdmmc\\port\\hpm_sdmmc_port.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\middleware\\hpm_sdmmc\\port\\hpm_sdmmc_port.c"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_gcc_lib.dir\\soc\\HPM6700\\HPM6750\\toolchains\\gcc\\start.S.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\toolchains\\gcc\\start.S", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\toolchains\\gcc\\start.S"}, {"directory": "E:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug", "command": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC12=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_BCFG=y -DHPMSOC_HAS_HPMSDK_BGPR=y -DHPMSOC_HAS_HPMSDK_BKEY=y -DHPMSOC_HAS_HPMSDK_BMON=y -DHPMSOC_HAS_HPMSDK_BPOR=y -DHPMSOC_HAS_HPMSDK_BSEC=y -DHPMSOC_HAS_HPMSDK_BUTN=y -DHPMSOC_HAS_HPMSDK_CAM=y -DHPMSOC_HAS_HPMSDK_CAN=y -DHPMSOC_HAS_HPMSDK_CONCTL=y -DHPMSOC_HAS_HPMSDK_DAO=y -DHPMSOC_HAS_HPMSDK_DMA=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_ENET=y -DHPMSOC_HAS_HPMSDK_FEMC=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_HALL=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_I2S=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_JPEG=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_LCDC=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MONO=y -DHPMSOC_HAS_HPMSDK_MULTICORE=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDM=y -DHPMSOC_HAS_HPMSDK_PDMA=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTL=y -DHPMSOC_HAS_HPMSDK_PMON=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PSEC=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEI=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_RTC=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SDXC=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TAMP=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -DHPMSOC_HAS_HPMSDK_VAD=y -DHPMSOC_HAS_HPMSDK_WDG=y -DSD_FATFS_ENABLE=1 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/arch/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/boards/hpm6750/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750 -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/ip -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/toolchains -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/soc/HPM6700/HPM6750/boot -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/drivers/inc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/utils/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/components/debug_console/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/common -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/fatfs/src/portable/sdxc -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/lib/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/hpm_sdk/middleware/hpm_sdmmc/port/. -IE:/2014902/HPM6750/sdk_env_v1.6.0/my_project/app/gpio/hpm6750_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM6750/sdk_env_v1.6.0/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei -o build_tmp\\CMakeFiles\\hpm_sdk_gcc_lib.dir\\soc\\HPM6700\\HPM6750\\toolchains\\gcc\\initfini.c.obj -c E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\toolchains\\gcc\\initfini.c", "file": "E:\\2014902\\HPM6750\\sdk_env_v1.6.0\\hpm_sdk\\soc\\HPM6700\\HPM6750\\toolchains\\gcc\\initfini.c"}]