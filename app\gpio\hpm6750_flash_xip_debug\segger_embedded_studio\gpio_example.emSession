<!DOCTYPE CrossStudio_Session_File>
<session>
 <Bookmarks>
  <BookmarkItem objectName="" line="55" column="-1" path="C:/Users/<USER>/Desktop/code/HPM6750_INS-370M-SD/HPM6750_INS-370M-SD/my_project/app/gpio/src/Source/inc/INS912AlgorithmEntry.h" number="5" extra="// short azimuth; //azimuth" color="5"/>
  <BookmarkItem objectName="" line="33" column="-1" path="C:/Users/<USER>/Desktop/code/HPM6750_INS-370M-SD/HPM6750_INS-370M-SD/my_project/app/gpio/src/Source/inc/INS912AlgorithmEntry.h" number="4" extra="float azimuth; //azimuth" color="5"/>
  <BookmarkItem objectName="" line="83" column="-1" path="C:/Users/<USER>/Desktop/code/HPM6750_INS-370M-SD/HPM6750_INS-370M-SD/my_project/app/gpio/src/Source/inc/INS912AlgorithmEntry.h" number="6" extra="short azimuth; //??? 7" color="5"/>
 </Bookmarks>
 <Breakpoints groups="Breakpoints" active_group="Breakpoints">
  <BreakpointListItem trigger="" line="180" counter="0" hardwareBreakpoint="" isFunctionBreakpoint="false" action="" expression="" stopAll="false" group="Breakpoints" type="Breakpoint" state="4" filename="../../src/Protocol/InsTestingEntry.c" useHWbreakpoint="false"/>
  <BreakpointListItem trigger="" line="2007" counter="0" hardwareBreakpoint="" isFunctionBreakpoint="false" action="" expression="" stopAll="false" group="Breakpoints" type="Breakpoint" state="2" filename="../../src/Source/src/SetParaBao.c" useHWbreakpoint="false"/>
 </Breakpoints>
 <ExecutionProfileWindow/>
 <FrameBuffer>
  <FrameBufferWindow width="1465605168" keepAspectRatio="0" zoomToFitWindow="0" showGrid="0" addressSpace="" format="0" height="293" autoEvaluate="0" scaleFactor="1" refreshPeriod="0" name="gpio_example - hpm6750_Debug" addressText="" accessByDisplayWidth="0"/>
 </FrameBuffer>
 <Memory1>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="gpio_example - hpm6750_Debug" sizeText="4" addressText="&amp;test_uart_tx_buf"/>
 </Memory1>
 <Memory2>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="gpio_example - hpm6750_Debug" sizeText="" addressText=""/>
 </Memory2>
 <Memory3>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="gpio_example - hpm6750_Debug" sizeText="" addressText=""/>
 </Memory3>
 <Memory4>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="gpio_example - hpm6750_Debug" sizeText="" addressText=""/>
 </Memory4>
 <Project>
  <ProjectSessionItem path="gpio_example"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;bsp"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;bsp;src"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;Common"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;Common;src"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;INAV"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;Protocol"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;Source"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;Source;src"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;application;src"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;soc"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;soc;HPM6700"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;soc;HPM6700;HPM6750"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;soc;HPM6700;HPM6750;toolchains"/>
  <ProjectSessionItem path="gpio_example;gpio_example - hpm6750;soc;HPM6700;HPM6750;toolchains;segger"/>
 </Project>
 <Register1>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="gpio_example - hpm6750_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register1>
 <Register2>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="gpio_example - hpm6750_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register2>
 <Register3>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="gpio_example - hpm6750_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register3>
 <Register4>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="gpio_example - hpm6750_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register4>
 <Threads>
  <ThreadsWindow showLists=""/>
 </Threads>
 <TraceWindow>
  <Trace enabled="Yes"/>
 </TraceWindow>
 <Watch1>
  <Watches active="1" update="Never">
   <Watchpoint expression="old_recvlen" name="old_recvlen" radix="10" linenumber="0" filename="../segger_embedded_studio"/>
   <Watchpoint expression="new_recvlen" name="new_recvlen" radix="10" linenumber="0" filename="../segger_embedded_studio"/>
   <Watchpoint expression="size" name="size" radix="10" linenumber="0" filename="../segger_embedded_studio"/>
  </Watches>
 </Watch1>
 <Watch2>
  <Watches active="0" update="Never"/>
 </Watch2>
 <Watch3>
  <Watches active="0" update="Never"/>
 </Watch3>
 <Watch4>
  <Watches active="0" update="Never"/>
 </Watch4>
 <Files>
  <SessionOpenFile windowGroup="DockEditLeft" x="19" y="180" useTextEdit="1" path="../../src/uart.c" left="0" top="160" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="1" y="127" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/portable/sdxc/hpm_sdmmc_disk.c" left="0" top="125" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="29" y="133" useTextEdit="1" path="../../src/INAV/adxl355.c" left="0" top="117" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="12" y="31" useTextEdit="1" path="../../src/uart_dma.h" left="0" top="4" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="103" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/portable/ff_queue.c" left="0" top="58" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="25" y="16" useTextEdit="1" path="../../src/uart.h" left="0" top="4" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="23" y="170" useTextEdit="1" path="../../src/main.c" left="0" top="149" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="20" y="436" useTextEdit="1" path="../../src/Protocol/protocol.c" left="0" top="416" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="278" useTextEdit="1" path="../../src/INAV/ins.h" left="0" top="254" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="17" y="121" useTextEdit="1" path="../../src/Source/src/INS_Init.c" left="0" top="121" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="33" y="40" useTextEdit="1" path="../../src/Source/inc/deviceconfig.h" left="0" top="33" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="182" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/toolchains/trap.c" left="0" top="137" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="8" y="48" useTextEdit="1" path="../../src/Source/src/systick.c" left="0" top="5" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="686" useTextEdit="1" path="../../src/Source/src/SetParaBao.c" left="0" top="686" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="20" y="33" useTextEdit="1" path="../../src/Source/src/FirmwareUpdateFile.c" left="0" top="10" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="17" y="162" useTextEdit="1" path="../../src/Source/inc/fpgad.h" left="0" top="131" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="285" useTextEdit="1" path="../../../../../hpm_sdk/drivers/inc/hpm_dma_drv.h" left="0" top="267" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="62" useTextEdit="1" path="../../../../../hpm_sdk/drivers/inc/hpm_csr_drv.h" left="0" top="40" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="238" useTextEdit="1" path="../../src/Source/src/gd32f4xx_it.c" left="0" top="193" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="31" y="1032" useTextEdit="1" path="../../src/Source/inc/SetParaBao.h" left="0" top="1024" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="163" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_plic_drv.h" left="0" top="135" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="12" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_ppor_drv.h" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="8" y="595" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_soc.h" left="0" top="577" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="27" y="60" useTextEdit="1" path="../../src/Source/src/fpgad.c" left="0" top="44" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="6" y="196" useTextEdit="1" path="../../src/sd_fatfs.c" left="0" top="171" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="15" y="124" useTextEdit="1" path="../../src/Source/src/INS_Output.c" left="0" top="104" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="8" y="171" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_interrupt.h" left="0" top="154" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="46" y="129" useTextEdit="1" path="../../src/flash.c" left="0" top="102" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="71" y="26" useTextEdit="1" path="../../src/Source/src/INS_Data.c" left="0" top="1" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="169" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/toolchains/segger/startup.s" left="0" top="151" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="1388" useTextEdit="1" path="../../src/Protocol/InsTestingEntry.c" left="0" selected="1" top="1398" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="16" useTextEdit="1" path="../../src/Source/src/datado.c" left="0" top="45" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="22" y="466" useTextEdit="1" path="../../src/INAV/readpaoche.c" left="0" top="447" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="100" useTextEdit="1" path="../../../../../hpm_sdk/drivers/inc/hpm_gpio_drv.h" left="0" top="78" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="134" useTextEdit="1" path="../../src/Source/src/gdwatch.c" left="0" top="91" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="625" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_clock_drv.c" left="0" top="588" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="1076" useTextEdit="1" path="../../../../../hpm_sdk/drivers/src/hpm_sdxc_drv.c" left="0" top="1075" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="18" y="31" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/portable/ff_queue.h" left="0" top="8" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="11" y="563" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/common/ff.c" left="0" top="546" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="24" y="64" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/portable/diskio.c" left="0" top="35" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="191" useTextEdit="1" path="../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_host.c" left="0" top="171" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="14" y="25" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/portable/diskio.h" left="0" top="13" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="1" y="1286" useTextEdit="1" path="../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_sd.c" left="0" top="1258" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="14" y="97" useTextEdit="1" path="../../src/sdram.c" left="0" top="68" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="21" y="90" useTextEdit="1" path="../../../../../hpm_sdk/middleware/fatfs/src/common/ffconf.h" left="0" top="70" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="67" y="239" useTextEdit="1" path="../../src/bsp/src/bsp_fmc.c" left="0" top="236" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="152" useTextEdit="1" path="../../src/Protocol/InsTestingEntry.h" left="0" top="110" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="28" y="771" useTextEdit="1" path="../../src/Protocol/frame_analysis.c" left="0" top="751" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="24" y="963" useTextEdit="1" path="../../src/Protocol/computerFrameParse.c" left="0" top="947" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="193" useTextEdit="1" path="../../../../boards/hpm6750/board.c" left="0" top="193" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="54" y="356" useTextEdit="1" path="../../../../boards/hpm6750/board.h" left="0" top="345" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="0" useTextEdit="1" path="gpio_example.emProject" left="0" top="210" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="51" y="37" useTextEdit="1" path="../../../../../hpm_sdk/drivers/src/hpm_uart_drv.c" left="0" top="21" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="17" useTextEdit="1" path="../../../../boards/hpm6750/pinmux.c" left="0" top="17" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="8" y="78" useTextEdit="1" path="../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_soc_feature.h" left="0" top="61" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="4" y="98" useTextEdit="1" path="../../../../../hpm_sdk/drivers/inc/hpm_uart_drv.h" left="0" top="81" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="19" y="59" useTextEdit="1" path="../../../../../hpm_sdk/drivers/inc/hpm_dmamux_drv.h" left="0" top="21" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="122" useTextEdit="1" path="../../src/uart_dma.c" left="0" top="118" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="36" useTextEdit="1" path="../../src/Source/inc/appmain.h" left="0" top="5" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="25" y="15" useTextEdit="1" path="../../src/main.h" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="10" y="79" useTextEdit="1" path="../../src/INAV/CONST.h" left="0" top="67" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="10" y="55" useTextEdit="1" path="D:/Program Files/SEGGER/SEGGER Embedded Studio 8.16b/include/string.h" left="0" top="35" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="47" y="59" useTextEdit="1" path="../../src/Source/inc/INS912AlgorithmEntry.h" left="0" top="59" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="42" y="230" useTextEdit="1" path="../../src/INAV/DATASTRUCT.h" left="0" top="211" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="30" y="356" useTextEdit="1" path="../../src/INAV/navi.c" left="0" top="325" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="132" useTextEdit="1" path="../../src/INAV/matvecmath.c" left="0" top="128" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="15" y="67" useTextEdit="1" path="../../src/INAV/TYPEDEFINE.h" left="0" top="41" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="6" y="285" useTextEdit="1" path="../../src/bsp/src/Logger.c" left="0" top="265" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="58" y="134" useTextEdit="1" path="../../src/INAV/read_and_check_gnss_data.c" left="0" top="115" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="20" y="668" useTextEdit="1" path="../../src/INAV/kalman.c" left="0" top="1088" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="88" useTextEdit="1" path="../../src/INAV/align.c" left="0" top="60" codecName="Default"/>
 </Files>
 <EMStudioWindow activeProject="gpio_example - hpm6750" fileDialogDefaultFilter="*.c" autoConnectTarget="J-Link" buildConfiguration="Debug" sessionSettings="" debugSearchFileMap="" fileDialogInitialDirectory="D:/workspace/01_code/ae_workspace/HPM6750_INS-370M_SDXC_ASYNC_U2/HPM6750_INS-370M/my_project/app/gpio/src" debugSearchPath="" autoConnectCapabilities="3199"/>
</session>
