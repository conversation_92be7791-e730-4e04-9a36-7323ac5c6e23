/*!
    \file  appdefine.h
    \brief the header file of main 
*/
#ifndef __APPDEFINE_H
#define __APPDEFINE_H


#define	c_systemrunmode_cantooling	0


#define c_output_normal			1
#define	c_output_testfog		0
#define	c_output_enable_can		0



#define	c_outputmode_normal		0	//Output standard algorithm results
#define	c_outputmode_gdw		1	//Output observational data
#define	c_outputmdoe_fpga		2	//Output FPGA data
#define	c_outputmdoe_fpgatxt	3	//Output FPGA data in text format
#define	c_outputmode			c_outputmdoe_fpga



extern	int	gins912outputmode;


#endif /* __APPDEFINE_H */


