/*****************************************************文件说明******************************************************************************/
/*文件名称：ALIGN.C                                                                                                                        */
/*版本号：  Ver 0.1                                                                                                                        */
/*编写日期/时间：                                                                                                                          */
/*编写人：                                                                                                                                 */
/*包含文件：const.h、typedefine.h、math.h、DATASTRUCT.h、EXTERNGLOBALDATA.h、FUNCTION.h、memory.h                                          */
/*测试用例：                                                                                                                               */
/*说明：                                                                                                                                   */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include <math.h>
#include <string.h>
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include "CONST.h"



/*********************************************函数说明*******************************************************/
/*函数名称：InertialSysAlign_Init                                                                           */
/*函数功能描述：惯性凝固坐标系对准初始化                                                                    */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：                                                                                                */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void InertialSysAlign_Init(p_InitBind const lp_InitBind, p_InertialSysAlign lp_InertialSysAlign)
{
    //register IPARA i = 0;
    memset(lp_InertialSysAlign, 0, sizeof(InertialSysAlign));   //将g_Navi结构体成员变量全部清零
    //惯导位置初始化
    lp_InertialSysAlign -> r_Lati = lp_InitBind -> r_InitLati;  //纬度,单位：rad
    lp_InertialSysAlign -> r_Logi = lp_InitBind -> r_InitLogi;  //经度，单位：rad
    lp_InertialSysAlign -> Height = lp_InitBind -> InitHeight;  //高度，单位：m
    //Qbib0的初始化，Cbib0以及Cib0b根据四元数计算获得，无需初始化
    lp_InertialSysAlign -> Qbib0[0] = 1.0;
    lp_InertialSysAlign -> Qbib0[1] = 0.0;
    lp_InertialSysAlign -> Qbib0[2] = 0.0;
    lp_InertialSysAlign -> Qbib0[3] = 0.0;

    //计算对准中间时间点t1的时间
    lp_InertialSysAlign -> T1 = TIME_COARSE_ALIGN / 2.0;
    //T1时刻速度矢量记录标志为NO
    lp_InertialSysAlign -> isT1Record = NO;
    //静基座对准条件下Cen取决于经纬度，不随对准时间改变，直接初始化
    //计算Cen方向余弦矩阵
    ComputeCen(lp_InertialSysAlign -> r_Lati,lp_InertialSysAlign -> r_Logi,lp_InertialSysAlign -> Cen); 
}


/*********************************************函数说明*******************************************************/
/*函数名称：InertialSysAlignCompute                                                                         */
/*函数功能描述：计算惯性凝固坐标系对准的实时计算部分                                                        */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：                                                                                                */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void InertialSysAlignCompute(DPARA const Gyro[3],DPARA const LastGyro[3], DPARA const Acc[3],
                             DPARA const LastAcc[3] ,p_InertialSysAlign lp_InertialSysAlign) 
{
    register IPARA i;
    //BOOL isAlignNormal;
    //输入陀螺、加表数值的写入和转换
    for (i = 0 ; i < 3; i++)
    {
        lp_InertialSysAlign -> r_Wibb[0][i] = LastGyro[i];
        lp_InertialSysAlign -> r_Wibb[1][i] = Gyro[i];
        lp_InertialSysAlign -> Fibb[i] = Acc[i];
    }
    //对准计算
    ComputeCie(lp_InertialSysAlign -> AlignTime , lp_InertialSysAlign -> Cie);//计算Cie e系到i系的方向余弦矩阵

    ComputeDelSenbb(lp_InertialSysAlign -> r_Wibb,lp_InertialSysAlign -> r_DelSenbb_1,lp_InertialSysAlign -> r_DelSenbb_2,lp_InertialSysAlign -> r_DelSenbb);

    ComputeQ(lp_InertialSysAlign -> r_DelSenbb, lp_InertialSysAlign -> Qbib0);//复用navi.c的computeQ函数计算Qbib0 更新载体系到初始惯性凝固坐标系的四元数

    QToCnb(lp_InertialSysAlign -> Qbib0,lp_InertialSysAlign -> Cib0b);//复用navi.c的QToCnb计算Cib0b 四元数转为方向余弦阵

    Mat_Tr(lp_InertialSysAlign -> Cib0b, lp_InertialSysAlign -> Cbib0, 3, 3);//计算Cbib0

    ComputeG(lp_InertialSysAlign -> r_Lati,lp_InertialSysAlign -> Height, &lp_InertialSysAlign -> Gn);//复用navi.c的ComputeG计算重力值

    ComputeVi(lp_InertialSysAlign -> Gn,lp_InertialSysAlign -> Cie,lp_InertialSysAlign -> Cen,lp_InertialSysAlign -> Vi);//计算惯性坐标系下的理论速度积分

    ComputeVib0(lp_InertialSysAlign -> Fibb, lp_InertialSysAlign -> Cbib0,lp_InertialSysAlign -> Vib0);//计算载体惯性凝固坐标系下的速度积分

    lp_InertialSysAlign -> AlignTime += TIME_NAVI;
    lp_InertialSysAlign -> AlignCount ++;
    //记录t1时刻的Vi和Vib0
    if((lp_InertialSysAlign -> AlignTime >= lp_InertialSysAlign -> T1) && (lp_InertialSysAlign -> isT1Record == NO))
    {
        for(i = 0 ; i < 3 ; i++)
        {
            lp_InertialSysAlign -> Vi_T1[i] = lp_InertialSysAlign -> Vi[i];
            lp_InertialSysAlign -> Vib0_T1[i] = lp_InertialSysAlign -> Vib0[i];
        }
        lp_InertialSysAlign -> isT1Record = YES;
    }
    //return isAlignNormal;
}




/*********************************************函数说明*******************************************************/
/*函数名称：ComputeCen                                                                                      */
/*函数功能描述：计算Cen方向余弦矩阵                                                                         */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：                                                                                                */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeCen(LATI r_Lati, LOGI r_Logi, MATR Cen[9])
{
    register DPARA SINLATI = sin(r_Lati);
    register DPARA COSLATI = cos(r_Lati);
    register DPARA SINLOGI = sin(r_Logi);
    register DPARA COSLOGI = cos(r_Logi);

    Cen[0] = -1 * SINLATI * COSLOGI;
    Cen[1] = -1 * SINLATI * SINLOGI;
    Cen[2] = COSLATI;
    Cen[3] =  COSLATI * COSLOGI;
    Cen[4] = COSLATI * SINLOGI;
    Cen[5] = SINLATI;
    Cen[6] = -1 * SINLOGI;
    Cen[7] =  COSLOGI;
    Cen[8] = 0;
}





/*********************************************函数说明*******************************************************/
/*函数名称：ComputeCie                                                                                      */
/*函数功能描述：计算Cie方向余弦矩阵                                                                         */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：                                                                                                */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeCie(TIME AlignTime , MATR Cie[9])
{
    Cie[0] = cos(WIE * AlignTime);
    Cie[1] = sin(WIE * AlignTime);
    Cie[2] = 0.0;

    Cie[3] = -1 * sin(WIE * AlignTime);
    Cie[4] = cos(WIE * AlignTime);
    Cie[5] = 0.0;

    Cie[6] = 0.0;
    Cie[7] = 0.0;
    Cie[8] = 1.0;
}





/*********************************************函数说明*******************************************************/
/*函数名称：ComputeVi                                                                                       */
/*函数功能描述：计算i系比力积分累积量Vi                                                                     */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：                                                                                                */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeVi(ACCELER Gn,MATR Cie[9],MATR Cen[9],VEL Vi[3])
{
    IPARA i;
    MATR Cei[9];
    MATR Cne[9];
    ACCELER Fn[3];
    ACCELER Fe[3];
    ACCELER Fi[3];

    Fn[0] = 0.0;
    Fn[1] = Gn;
    Fn[2] = 0.0;    //北天东配置

    Mat_Tr(Cie, Cei, 3, 3);  //转置
    Mat_Tr(Cen, Cne, 3, 3);  //转置
    Mat_Mul(Cne, Fn, Fe, 3, 3, 1);
    Mat_Mul(Cei, Fe, Fi, 3, 3, 1);

    for(i = 0; i < 3; i++)
    {
        Vi[i] += Fi[i] * TIME_NAVI;
    } 
}






/*********************************************函数说明*******************************************************/
/*函数名称：ComputeVib0                                                                                     */
/*函数功能描述：计算i系比力积分累积量Vib0                                                                   */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：                                                                                                */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeVib0(ACCELER Fibb[3],MATR Cbib0[9],VEL Vib0[3])
{
    IPARA i;

    ACCELER Fib0[3];

    Mat_Mul(Cbib0, Fibb, Fib0, 3, 3, 1);

    for(i = 0; i < 3; i++)
    {
        Vib0[i] += Fib0[i] * TIME_NAVI;
    } 
}





/*********************************************函数说明*******************************************************/
/*函数名称：ComputeCib0i                                                                                    */
/*函数功能描述：计算i系比力积分累积量Vib0                                                                   */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：                                                                                                */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeCib0i(VEL Vi[3],VEL Vib0[3], VEL Vi_T1[3],VEL Vib0_T1[3],MATR Cib0i[9])         
{
    //IPARA i;
    VEC Vector_1[3];
    VEC Vector_2[3];

    VEC Vector_3[3];
    VEC Vector_4[3];

    MATR Matr_1[9];
    MATR Matr_2[9];

    MATR InvMatr_1[9]; //Matr_1的逆矩阵

    Vec_Cross(Vi_T1, Vi, Vector_1);
    Vec_Cross(Vib0_T1, Vib0, Vector_3);

    Vec_Cross(Vector_1, Vi_T1, Vector_2);
    Vec_Cross(Vector_3, Vib0_T1, Vector_4);

    Matr_1[0] = Vi_T1[0];
    Matr_1[1] = Vi_T1[1];
    Matr_1[2] = Vi_T1[2];

    Matr_1[3] = Vector_1[0];
    Matr_1[4] = Vector_1[1];
    Matr_1[5] = Vector_1[2];

    Matr_1[6] = Vector_2[0];
    Matr_1[7] = Vector_2[1];
    Matr_1[8] = Vector_2[2];

    Matr_2[0] = Vib0_T1[0];
    Matr_2[1] = Vib0_T1[1];
    Matr_2[2] = Vib0_T1[2];

    Matr_2[3] = Vector_3[0];
    Matr_2[4] = Vector_3[1];
    Matr_2[5] = Vector_3[2];

    Matr_2[6] = Vector_4[0];
    Matr_2[7] = Vector_4[1];
    Matr_2[8] = Vector_4[2];

    //计算出Cib0i
    Mat_Inv(Matr_1, InvMatr_1,3);
    Mat_Mul(InvMatr_1, Matr_2, Cib0i,3, 3, 3);  
}




/*********************************************函数说明*******************************************************/
/*函数名称：FinishInertialSysAlign                                                                          */
/*函数功能描述：计算i系比力积分累积量Vib0                                                                   */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：                                                                                                */
/*备注1：                                                                                                   */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void FinishInertialSysAlign(p_InertialSysAlign lp_InertialSysAlign)  //在粗对准时间结束后被调用，用于计算最终的对准结果，确定载体的初始姿态矩阵和姿态角。       
{
    MATR Cbi[9];
    MATR Cbe[9];
    MATR Cbn[9];

    ComputeCib0i(lp_InertialSysAlign -> Vi,lp_InertialSysAlign -> Vib0,lp_InertialSysAlign -> Vi_T1,lp_InertialSysAlign -> Vib0_T1,lp_InertialSysAlign -> Cib0i);//求解两个坐标系之间的转换矩阵
    //多个矩阵连续乘积获得Cbn
    Mat_Mul(lp_InertialSysAlign -> Cib0i, lp_InertialSysAlign -> Cbib0, Cbi,3, 3, 3);  
    Mat_Mul(lp_InertialSysAlign -> Cie, Cbi, Cbe,3, 3, 3); 
    Mat_Mul(lp_InertialSysAlign -> Cen, Cbe, Cbn,3, 3, 3);
    //转置获得Cnb
    Mat_Tr(Cbn, lp_InertialSysAlign -> AlignCnb, 3, 3);
    CnbToAtti(lp_InertialSysAlign -> AlignCnb, lp_InertialSysAlign -> r_AlignAtti);  //复用navi.c的CnbToAtti函数 方向余弦矩阵转换为姿态角
    AttiToCnb(lp_InertialSysAlign -> r_AlignAtti, lp_InertialSysAlign -> AlignCnb);  //根据姿态重构一个正交化的Cnb，复用navi.c的AttiToCnb函数
    CnbToQ(lp_InertialSysAlign -> AlignCnb,lp_InertialSysAlign -> AlignQ);    //复用navi.c的CnbToQ函数
    lp_InertialSysAlign ->isAlign_Finish = YES;
}




/*****************************************************************************************************************************
// 函数名称： ComputeVG
// 功能描述： 垂直陀螺，计算VG航姿
// 输入参数： lp_IMUSmoothAverage,p_IMUSmoothAverage
// 输出参数： 无。
// 全局变量： 无
//-----------------------------------------------------------------------------------------------------------------------------
//编     写：
//版本/日期：
//-----------------------------------------------------------------------------------------------------------------------------
//修     改：
//版本/日期：
*****************************************************************************************************************************/
void ComputeVG(p_IMUSmoothAverage lp_IMUSmoothAverage)
{
    ATTI r_Atti[3];
    DPARA g1 = sqrt(lp_IMUSmoothAverage -> AccSmoothMean_100Hz[0] * lp_IMUSmoothAverage -> AccSmoothMean_100Hz[0] + lp_IMUSmoothAverage -> AccSmoothMean_100Hz[1] * lp_IMUSmoothAverage -> AccSmoothMean_100Hz[1] + lp_IMUSmoothAverage -> AccSmoothMean_100Hz[2] * lp_IMUSmoothAverage -> AccSmoothMean_100Hz[2]); 
    lp_IMUSmoothAverage -> r_VG_Pitch = asin(lp_IMUSmoothAverage -> AccSmoothMean_100Hz[1] / g1);
    lp_IMUSmoothAverage -> r_VG_Roll = asin(-lp_IMUSmoothAverage -> AccSmoothMean_100Hz[0] / sqrt(g1 * g1 - lp_IMUSmoothAverage -> AccSmoothMean_100Hz[1] * lp_IMUSmoothAverage -> AccSmoothMean_100Hz[1]));
    if((lp_IMUSmoothAverage -> AccSmoothMean_100Hz[2] <= 0.0) && (lp_IMUSmoothAverage -> AccSmoothMean_100Hz[0] <= 0.0))
    {
        lp_IMUSmoothAverage -> r_VG_Roll = PI - lp_IMUSmoothAverage -> r_VG_Roll;
    }
    if((lp_IMUSmoothAverage -> AccSmoothMean_100Hz[2] <= 0.0) && (lp_IMUSmoothAverage -> AccSmoothMean_100Hz[0] >= 0.0))
    {
        lp_IMUSmoothAverage -> r_VG_Roll = -lp_IMUSmoothAverage -> r_VG_Roll - PI;
    }
    r_Atti[0] = 0.0;
    r_Atti[1] = lp_IMUSmoothAverage -> r_VG_Pitch;
    r_Atti[2] = lp_IMUSmoothAverage -> r_VG_Roll;
    AttiToCnb(r_Atti,lp_IMUSmoothAverage -> Cnb);
    CnbToQ(lp_IMUSmoothAverage -> Cnb,lp_IMUSmoothAverage -> Q);
}




/*****************************************************************************************************************************
// 函数名称： ComputeVGDelSenbb
// 功能描述： //
// 输入参数： //
// 输出参数： 无。
// 全局变量： 无
//-----------------------------------------------------------------------------------------------------------------------------
//编     写：
//版本/日期：
//-----------------------------------------------------------------------------------------------------------------------------
//修     改：
//版本/日期：
*****************************************************************************************************************************/
void ComputeVGDelSenbb(ANGRATE r_Wnbb[2][3],DELANG r_DelSenbb_1[3],DELANG r_DelSenbb_2[3],DELANG r_DelSenbb[3])
{
    register IPARA i;
    DELANG r_DelSenbb_Compen[3] = {0.0};

    for(i = 0; i < 3; i++)
    {
        r_DelSenbb_1[i] = (3 * r_Wnbb[0][i] + r_Wnbb[1][i]) * TIME_VG * 0.125;     //  QXF，20230927移植，由0.005修改为TIME_VG（0.002，500Hz）
    }                                                            //0.125是啥意思？？？
    for(i = 0; i < 3; i++)
    {
        r_DelSenbb_2[i] = (r_Wnbb[0][i] + 3 * r_Wnbb[1][i]) * TIME_VG * 0.125;     //  QXF，20230927移植，由0.005修改为TIME_VG（0.002，500Hz）
    }
    Vec_Cross(r_DelSenbb_1, r_DelSenbb_2, r_DelSenbb_Compen);

    for(i = 0; i < 3; i++)
    {
        r_DelSenbb[i] = r_DelSenbb_1[i] + r_DelSenbb_2[i] + 2 / 3 * r_DelSenbb_Compen[i];
    }
}





/*****************************************************************************************************************************
// 函数名称： ComputeVGSmooth
// 功能描述： //
// 输入参数： //
// 输出参数： 无。
// 全局变量： 无
//-----------------------------------------------------------------------------------------------------------------------------
//编     写：
//版本/日期：
//-----------------------------------------------------------------------------------------------------------------------------
//修     改：
//版本/日期：
*****************************************************************************************************************************/
void ComputeVGSmooth(p_IMUSmoothAverage lp_IMUSmoothAverage)
{
    ATTI r_Atti[3];
    ANGRATE r_Wibb[2][3];
    DPARA K_P = 0.004;     //qxf,0.004是啥意思？
    DPARA K_I = 0.0001;    //qxf,0.0001是啥意思？
    DPARA Gn[3] = {0,-1,0};
    DPARA Gb[3]; 
    DPARA Err[3];
    DPARA Fibb,VG_Fibb[3];
    DPARA U[3];
    DELANG r_DelSenbb_1[3];
    DELANG r_DelSenbb_2[3];
    DELANG r_DelSenbb[3];

    Mat_Mul(lp_IMUSmoothAverage -> Cnb, Gn,Gb, 3, 3, 1);
    Fibb = sqrt(lp_IMUSmoothAverage -> AccSmoothMean_200Hz [0] * lp_IMUSmoothAverage -> AccSmoothMean_200Hz [0] + lp_IMUSmoothAverage -> AccSmoothMean_200Hz [1] * lp_IMUSmoothAverage -> AccSmoothMean_200Hz [1]+lp_IMUSmoothAverage -> AccSmoothMean_200Hz[2] * lp_IMUSmoothAverage -> AccSmoothMean_200Hz[2]);

    VG_Fibb[0] = lp_IMUSmoothAverage -> AccSmoothMean_200Hz [1] / Fibb;
    VG_Fibb[1] = lp_IMUSmoothAverage -> AccSmoothMean_200Hz [2] / Fibb;
    VG_Fibb[2] = lp_IMUSmoothAverage -> AccSmoothMean_200Hz [0] / Fibb;

    Vec_Cross(Gb, VG_Fibb, Err);
    for(int i = 0; i<3;i++)
    {
        lp_IMUSmoothAverage -> Err_I[i] += Err[i] * TIME_VG;    //qxf,0.005是啥意思？     //  QXF，20230927移植，由0.005修改为TIME_VG（0.002，500Hz）
        U[i] = K_P * Err[i] + K_I * lp_IMUSmoothAverage -> Err_I[i];
    }

    r_Wibb[0][0] = lp_IMUSmoothAverage->GyroSmoothMean_200Hz[1] * D2R + U[0];
    r_Wibb[0][1] = lp_IMUSmoothAverage->GyroSmoothMean_200Hz[2] * D2R + U[1];
    r_Wibb[0][2] = lp_IMUSmoothAverage->GyroSmoothMean_200Hz[0] * D2R + U[2];

    r_Wibb[1][0] = lp_IMUSmoothAverage->GyroSmoothMean_200Hz[1] * D2R + U[0];
    r_Wibb[1][1] = lp_IMUSmoothAverage->GyroSmoothMean_200Hz[2] * D2R + U[1];
    r_Wibb[1][2] = lp_IMUSmoothAverage->GyroSmoothMean_200Hz[0] * D2R + U[2];


    ComputeVGDelSenbb(r_Wibb,r_DelSenbb_1,r_DelSenbb_2,r_DelSenbb);
    ComputeQ(r_DelSenbb, lp_IMUSmoothAverage -> Q);    //计算姿态四元数
    QToCnb(lp_IMUSmoothAverage -> Q, lp_IMUSmoothAverage -> Cnb);    //根据姿态四元数计算姿态矩阵
    CnbToAtti(lp_IMUSmoothAverage -> Cnb, r_Atti);    //根据姿态矩阵计算姿态角
    lp_IMUSmoothAverage -> r_VG_Pitch = r_Atti[1];
    lp_IMUSmoothAverage -> r_VG_Roll  = r_Atti[2];
}

