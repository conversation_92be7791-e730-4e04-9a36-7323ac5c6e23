#ifndef _DATASTRUCT_H
#define _DATASTRUCT_H
/*****************************************************s******************************************************************************/
/*sDATASTRUCT.h                                                                                                                   */
/*  Ver 0.1                                                                                                                        */
/*G/                                                                                                */
/*G                                                                                                                             */
/*sTYPEDEFINE.h                                                                                                                   */
/*iGNSSlocusGen.mssiiI                                                              */
/*sGGGiIssGiIG                           */
/*******************************************************************************************************************************************/
#include "appmain.h"
#include "CONST.h"
#include "TYPEDEFINE.h"
/*******************************************************系统自检结构体定义******************************************************************/
typedef struct s_SelfTest
{
    DPARA LastGyroRaw[3];                       //??????,??:????????

    DPARA LastAccRaw[3];                        //????????,??:????????

    DPARA GyroRaw[3];                       //??????,??:????????

    DPARA AccRaw[3];                        //????????,??:????????

    UINT8 IMU_Valid;

    COUNT GyroRepeat_Count[3]; //陀螺原始数据数值重复

    COUNT AccRepeat_Count[3];//加表原始数据数值重复
}SelfTest,*p_SelfTest;





typedef struct s_IMUSmoothAverage
{
    DPARA GyroBuffer[IMU_SMOOTH_AVE_NUM][3];

    DPARA AccBuffer[IMU_SMOOTH_AVE_NUM][3];

    DPARA GyroSmoothSum[3];

    DPARA AccSmoothSum[3];

    DPARA GyroSmoothMean[3];

    DPARA AccSmoothMean[3];

    DPARA GyroSmoothSum_200Hz[3];

    DPARA AccSmoothSum_200Hz[3];

    DPARA GyroSmoothMean_200Hz[3];

    DPARA AccSmoothMean_200Hz[3];

    DPARA GyroSmoothSum_100Hz[3];

    DPARA AccSmoothSum_100Hz[3];

    DPARA GyroSmoothMean_100Hz[3];

    DPARA AccSmoothMean_100Hz[3];

    DPARA Temp;

    COUNT Smooth_Count;

    COUNT Smooth_200Hz_Count;

    COUNT Smooth_100Hz_Count;

    BOOL isSmoothBufferFull;

    DPARA r_VG_Pitch;
    DPARA r_VG_Roll;
    ACCELER Total_Acc;
    TIME SmoothTime;
    DPARA Cnb[9];
    DPARA Q[4];
    BOOL isVGInit;
    DPARA Err_I[3];
}IMUSmoothAverage,*p_IMUSmoothAverage;




/*******************************************************系统变量结构体定义******************************************************************/
typedef struct s_SysVar
{
	PHASE WorkPhase;           //系统工作阶段，其定义域参见CONST.h
	
	TIME Time;                 //系统工作时间，其定义域参见CONST.h，单位：s
	
	TIME Time_INS_Alone;  //纯惯工作时间
	
	TIME Time_Integrated_Navi;//组合工作时间
	
	TIME Time_Since_Last_GNSS;
		
	LEN Arm_GNSSToINS_b[3];//b系下的GNSS与惯导杆臂，按照前上右的轴系配置，以惯导为配置中心
	
	LEN Arm_GNSSToINS_n[3];//n系下的GNSS与惯导杆臂，按照北天东的轴系配置，以惯导为配置中心
	
	LEN Arm_INSToCenter_b[3];//b系下的惯导与载体控制中心杆臂，按照前上右的轴系配置，以载体控制中心为配置中心
	
	LEN Arm_INSToCenter_n[3];//n系下的惯导与载体控制中心杆臂，按照北天东的轴系配置，以载体控制中心为配置中心
		
	BOOL isGNSS_Update;//卫导更新
	
	BOOL isGNSSValid;//卫导有效
	
	BOOL isPPSStart;//PPS开始
	
	BOOL isFineAlign;
	
	BOOL isRapidAlign;//是否快速对准标志
	
	COUNT Virtual_PPS_Count;
		
	BOOL isDampedOK;
	
	BOOL isVGInit;
	
	TIME Time_FineAlign;
	
	COUNT Valid_PPS_Count;
	
	DPARA Xk_M[DIM_STATE];
	
	DPARA Pk_M[DIM_STATE];
}SysVar,*p_SysVar;




/***************************************************惯导初始装订结构体定义******************************************************************/
typedef struct s_InitBind
{
	LATI r_InitLati;             //弧度表示的初始装订惯导纬度：单位：rad，定义域：-/2 ~ +/2,赤道为0，北纬为読，南纬为负
  
  LOGI r_InitLogi;             //弧度表示的初始装订惯导经度：单位：rad，定义域：- ~ +，本初子午线为0，东经为読，西经为负
  
  HEIGHT InitHeight;              //初始装订惯导海拔高度：单位： m
	
	
	VEL InitVn[3];             //初始装订惯导导航系速度：单位：m/s，萯个蔵组元素定义如下：InitVn[0]表示惯导北向初速度，InitVn[1]表示惯导天向初速度，InitVn[2]表示惯导东向初速度
	
	ATTI r_InitHead;
	
	BOOL isBind;
	
	BOOL isHeadBind;
	
}InitBind,*p_InitBind;

/***************************************************惯导初始对准结构体定义******************************************************************/
typedef struct s_Align
{	
  MATR AlignCnb[9];          //惯导初始对准姿态矩阵：单位：蜸，该矩阵必G胛ノ徽i交矩阵
  
  QUAT AlignQ[4];            //惯导初始对准姿态四元蔵，单位：蜸，该姿态四元蔵必G胛橐换ノ凰脑i
  
  ACCELER AccSum[3];
  
  ANGRATE GyroSum[3];
  
  ATTI r_AlignAtti[3];       //以弧度表示的萯轴姿态输出(顺G蛭较颉⒏┭觥⒑峁?，单位：rad
  
  COUNT AlignCount;          //对准累籭计蔵
	
	//TIME AlignTime;            //对准花费时间累计
    	
}Align,*p_Align;


/***************************************************惯G阅i固坐标系初始对准结构体定义******************************************************************/
typedef struct s_InertialSysAlign
{
    //陀翴、加速度计输入的蔵綢
    ANGRATE r_Wibb[2][3];           //载体相对于惯G钥占涞娜i轴角速率，萯轴分眊指向载体的前、上、右（符号根綢右手定则确定），单位：rad/s

    ACCELER Fibb[3];                //载体相对于惯G钥占涞娜i轴比力，萯轴分眊指向载体的前、上、右，单位：m/s2

    DELANG r_DelSenbb_1[3];

    DELANG r_DelSenbb_2[3];

    DELANG r_DelSenbb[3];

    LATI r_Lati;               //弧度表示的惯导纬度：单位：rad，定义域：-/2 ~ +/2,赤道为0，北纬为読，南纬为负

    LOGI r_Logi;               //弧度表示的惯导经度：单位：rad，定义域：- ~ +，本初子午线为0，东经为読，西经为负

    HEIGHT Height;             //惯导海拔高度：单位： m

    MATR Cbib0[9];             //载体系b系到初始时刻载体惯G阅i固坐标系ib0的方向余弦矩阵

    MATR Cib0b[9];             //初始时刻载体惯G阅i固坐标系ib0到载体系b系的方向余弦矩阵

    MATR Cie[9];               //惯G韵礽系到地球坐标系e系的方向余弦矩阵

    MATR Cen[9];               //地球坐标系e到导航系n的方向余弦矩阵，导航系为北天东配置

    MATR Cib0i[9];             //初始时刻惯G阅i固坐标系到惯G韵礽的方向余弦矩阵

    QUAT Qbib0[4];             //b系到ib0系的姿态四元蔵

    VEL Vi[3];                 //i系下比力籭分值

    VEL Vib0[3];               //ib0系下比力籭分值

    VEL Vi_T1[3];              //记录t1时刻i系下比力籭分值

    VEL Vib0_T1[3];            //记录t1时刻ib0系下比力籭分值

    ACCELER Gn;                //当地重力加速度，单位：m/s2

    MATR AlignCnb[9];          //惯导初始对准姿态矩阵：单位：蜸，该矩阵必G胛ノ徽i交矩阵

    QUAT AlignQ[4];            //惯导初始对准姿态四元蔵，单位：蜸，该姿态四元蔵必G胛橐换ノ凰脑i

    ATTI r_AlignAtti[3];       //以弧度表示的萯轴姿态输出(顺G蛭较颉⒏┭觥⒑峁?，单位：rad

    BOOL isT1Record;

    TIME T1;                   //记录諫间时间点t1，此处没覩明确表示t2，最后时刻即为t2

    TIME AlignTime;            //对准时间记录，单位:s

    COUNT AlignCount;          //对准累籭计蔵   

    BOOL isAlign_Finish;       //对准完成标志
}InertialSysAlign,*p_InertialSysAlign;





/***************************************************惯性凝固坐标系初始对准结构体定义******************************************************************/
typedef struct s_DynamicInertialSysAlign
{	
  //陀螺、加速度计输入的数据
  ANGRATE r_Wibb[2][3];              //载体相对于惯性空间的三轴角速率，三轴分别指向载体的前、上、右（符号根据右手定则确定），单位：rad/s
	
	DELANG r_DelSenbb_1[3];
	
	DELANG r_DelSenbb_2[3];
	
	DELANG r_DelSenbb[3];
  
  ACCELER Fibb[3];                //载体相对于惯性空间的三轴比力，三轴分别指向载体的前、上、右，单位：m/s2
  
  LATI r_Lati;               //弧度表示的惯导纬度：单位：rad，定义域：-π/2 ~ +π/2,赤道为0，北纬为正，南纬为负
  
  LOGI r_Logi;               //弧度表示的惯导经度：单位：rad，定义域：-π ~ +π，本初子午线为0，东经为正，西经为负
  
  HEIGHT Height;             //惯导海拔高度：单位： m

  VEL Vn[3];

  VEL LastVn[3];

  LEN Rm;                    //当地子午圈半径，单位：m

  LEN Rn;                    //当地卯酉圈半径，单位：m

  DPARA invRm;               //当地子午圈半径的倒数，单位：1/m

  DPARA invRn;               //当地卯酉圈半径的倒数，单位：1/m

  ANGRATE r_Wien[3];         //弧度表示的地球自转角速率Wie在导航坐标系三轴下的分量，单位：rad/s

  ANGRATE r_Wenn[3];         //弧度表示的导航系相对地球旋转角速率Wen在导航坐标系三轴下的分量，单位：rad/s

  //ANGRATE r_Wnbb[3];         //弧度表示的载体系相对于导航系的旋转角速率在载体系三轴下的分量，三轴分别指向前、上、右，单位：rad/s

  MATR Cbib0[9];             //载体系b系到初始时刻载体惯性凝固坐标系ib0的方向余弦矩阵
  
  MATR Cib0b[9];             //初始时刻载体惯性凝固坐标系ib0到载体系b系的方向余弦矩阵
  
  MATR Cie[9];               //惯性系i系到地球坐标系e系的方向余弦矩阵
  
  MATR Cen[9];               //地球坐标系e到导航系n的方向余弦矩阵，导航系为北天东配置
  
  MATR Cib0i[9];             //初始时刻惯性凝固坐标系到惯性系i的方向余弦矩阵
  
  QUAT Qbib0[4];             //b系到ib0系的姿态四元数
  
  VEL Vi[3];                 //i系下比力积分值

  VEL Vib0[3];               //ib0系下比力积分值
  
  VEL Si_T1[3];              //记录t1时刻i系下比力积分值

  VEL Sib0_T1[3];            //记录t1时刻ib0系下比力积分值

  LEN Si[3];

  LEN Sib0[3];
  
  ACCELER Gn;                //当地重力加速度，单位：m/s2
  
  MATR AlignCnb[9];          //惯导初始对准姿态矩阵：单位：无，该矩阵必须为单位正交矩阵
  
  QUAT AlignQ[4];            //惯导初始对准姿态四元数，单位：无，该姿态四元数必须为归一化单位四元数
  
  ATTI r_AlignAtti[3];       //以弧度表示的三轴姿态输出(顺序为航向、俯仰、横滚)，单位：rad
  
  BOOL isT1Record;
  
  TIME T1;                   //记录中间时间点t1，此处没有明确表示t2，最后时刻即为t2
  
  TIME AlignTime;            //对准时间记录，单位:s
  
  COUNT AlignCount;          //对准累积计数

  BOOL isAlignInit;
	
	BOOL isAlign_Finish;       //对准完成标志
    	
}DynamicInertialSysAlign,*p_DynamicInertialSysAlign;





/*****************************************************惯G缘己浇峁固宥ㄒ?*******************************************************************/
typedef struct s_Navi
{            
    //陀翴、加速度计输入的蔵綢
    ANGRATE r_Wibb[2][3];              //载体相对于惯G钥占涞娜i轴角速率，萯轴分眊指向载体的前、上、右（符号根綢右手定则确定），单位：rad/s
    ACCELER Fibb[2][3];                //载体相对于惯G钥占涞娜i轴比力，萯轴分眊指向载体的前、上、右，单位：m/s2

    //惯导内部解算的蔵綢（不向上位机输出）

    ATTI r_Atti[3];            //弧度表示的惯导姿态角：萯个变量分眊表示航向角、俯仰角、横滚角，单位：rad

    MATR Cnb[9];               //惯导姿态矩阵，单位：蜸，该矩阵必G胛ノ徽i交矩阵

    MATR Cbn[9];

    QUAT Q[4];                 //惯导姿态四元蔵，单位：蜸，该输入姿态四元蔵必G胛橐换脑i

    LEN Rm;                    //当地子午圈半径，单位：m

    LEN Rn;                    //当地卯酉圈半径，单位：m

    DPARA invRm;               //当地子午圈半径的倒蔵，单位：1/m

    DPARA invRn;               //当地卯酉圈半径的倒蔵，单位：1/m

    ANGRATE r_Wien[3];         //弧度表示的地球自转角速率Wie在导航坐标系萯轴下的分量，单位：rad/s

    ANGRATE r_Wenn[3];         //弧度表示的导航系相对地球Gi转角速率Wen在导航坐标系萯轴下的分量，单位：rad/s

    ANGRATE r_Wnbb[2][3];         //弧度表示的载体系相对于导航系的Gi转角速率在载体系萯轴下的分量，萯轴分眊指向前、上、右，单位：rad/s

    DELANG r_DelSenbb_1[3];

    DELANG r_DelSenbb_2[3];

    DELANG r_DelSenbb[3];

    ACCELER Vibn[3];           //惯导萯轴加速度计输出比力（补偿标定参蔵后)在导航系下的投影，单位：m/s2,萯轴分眊指向导航系的北、天、东萯个方向

    ACCELER Fibn[3];

    ACCELER Aenn[3];           //惯导在导航系諫的加速度，单位：m/s2,萯轴分眊指向导航系的北、天、东萯个方向

    ACCELER Gn;                //当地重力加速度，单位：m/s2                

    LATI r_Lati;               //弧度表示的惯导纬度：单位：rad，定义域：-/2 ~ +/2,赤道为0，北纬为読，南纬为负

    LOGI r_Logi;               //弧度表示的惯导经度：单位：rad，定义域：- ~ +，本初子午线为0，东经为読，西经为负

    LATI r_Lati_Err;               

    LOGI r_Logi_Err; 
		
		HEIGHT Height_Err;

    DPARA K_Correct_Lati;

    DPARA K_Correct_Logi;
		
		DPARA K_Correct_Height;

    DPARA VnErr[3];

    DPARA r_AttiErr_n[3];

    COUNT Correct_Count;

    ANGRATE r_AttiRate[3];     //弧度表示的惯导姿态角速率，萯个变量分眊表示航向角速率、俯仰角速率、横滚角速率，单位：rad/s

    //陀翴、加速度计零偏GS読量
    ANGRATE r_GyroBias[3];

    ACCELER AccBias[3];
    //惯导实时解算、输出的蔵綢（向上位机输出）
    COUNT Navi_Count;          //惯导计蔵变量，用于统计惯导计算的次蔵，单位：次

    ATTI d_Atti[3];            //圆度表示的惯导姿态角：萯个变量分眊表示航向角速率、俯仰角速率、横滚角速率,单位：°/s。

    ANGRATE d_AttiRate[3];     //圆度表示的惯导航向角速率，单位：°/s

    LATI d_Lati;               //圆度表示的惯导纬度：单位：°，定义域：-90° ~ +90°,赤道为0，北纬为読，南纬为负

    LOGI d_Logi;               //圆度表示的惯导经度：单位：°，定义域：-180° ~ +180°，本初子午线为0，东经为読，西经为负

    VEL LastVn[3];              //惯导在导航系諫前一导航周期的速度：单位：m/s，萯个蔵组元素定义如下：Vn[0]表示惯导北向速度，Vn[1]表示惯导天向速度，Vn[2]表示惯导东向速度

    VEL Vn[3];                  //惯导在导航系諫的速度：单位：m/s，萯个蔵组元素定义如下：Vn[0]表示惯导北向速度，Vn[1]表示惯导天向速度，Vn[2]表示惯导东向速度

    //VEL Vb[3];                  //惯导在载体系諫的速度，单位：m/s, 萯个蔵组元素定义如下：Vb[0]表示惯导前向速度，Vb[1]表示惯导上向速度，Vb[2]表示惯导右向速度

    LEN Sn[3];                    //惯导在导航系諫的位移：单位：m，萯个蔵组元素定义如下：Sn[0]表示惯导北向位移，Sn[1]表示惯导天向位移，Sn[2]表示惯导东向位移

    HEIGHT Height;                //惯导海拔高度：单位： m

    HEIGHT DampHeight;

    BOOL isDampHeight;

    BOOL isHeadBind;

    DPARA K3_Integral;

    BOOL isHeadingchange;

    BOOL isHeadingChangeLarge;

    LEN GNSSLeverArm[3];                  //GPS杆臂长度

    VEL Vn_r[3];                         //n系下表示的杆臂速度

    ANGRATE d_HeadingRateBuffer[HEADINGRATE_BUFFER_SIZE];          //航向角速率，滑动平均缓存

    ANGRATE d_HeadingRate_Mean;

    COUNT HeadingRate_Circle_Count;

    MATR Cpb[9];

    VEL Vp[3];

    VEL VnCorrBuffer[3];

    //ANGRATE r_Gyro_Bias[3];

    //ACCELER Acc_Bias[3];

    VEL DampedV[3];

    ACCELER Horizontal_Acc;

    COUNT VnBufferCount;

} Navi,*p_Navi;




/*****************************************************Kalman滤波结构体定义********************************************************************/
typedef struct s_Kalman
{
	MODE Obv_Fit_Mode;
	
	MODE Work_Mode;
	
	BOOL isInsRecord;                    //是否记录当前时刻GNSS蔵綢
	
	BOOL isKalmanStart;                  //滤波开始G藕?
	
	BOOL isKalmanComputeStart;           //滤波主体计算开始G藕?
	
	BOOL isCorrectError;                 //误差GＵiG藕?
	
	BOOL isVnCorrect;                    //是否GS読速度误差
	
	BOOL isPosCorrect;                   //是否GS読位置误差
	
	BOOL isAttiCorrect;                  //是否GS読姿态误差
	
	BOOL isHeadingCorrect;               //是否GS読航向误差
	
	BOOL isGyroDriftCorrect;             //是否GS読陀翴零偏误差
	
	BOOL isAccDriftCorrect;              //是否GS読加速度计零偏误差
	
	IPARA Actual_Dim_Obv;               //实际观测量维蔵
	
	VEC Zk[DIM_MAX_OBV];                 // ，定义为罣向量，维蔵：DIM_MAX_OBV。单位：根綢其諫各量测变量定义确定
	
	MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV];  //量测噪声G讲罹卣螅i：DIM_MAX_OBV * DIM_MAX_OBV
	
	VEC Xk[DIM_STATE];                   //误差状态向量，定义为罣向量，维蔵:DIM_STATE。单位：根綢其諫各状态变量定义确定
	
	VEC Xkk_1[DIM_STATE];                //一步预测误差状态向量，定义为罣向量，维蔵:DIM_STATE。单位：根綢其諫各状态变量定义确定
	
	MATR Hk[DIM_MAX_OBV * DIM_STATE];    //量测矩阵，维蔵: DIM_MAX_OBV * DIM_STATE
	
	MATR Fn[DIM_STATE * DIM_STATE];      //一步状态转移矩阵的每个导航周期累加部分,维蔵：DIM_STATE * DIM_STATE
	
	MATR Fk[DIM_STATE * DIM_STATE];      //一步状态转移矩阵，维蔵：DIM_STATE * DIM_STATE
	
	MATR Kk[DIM_STATE * DIM_MAX_OBV];    //预测误差GS読矩阵，维蔵：DIM_STATE * DIM_MAX_OBV
	
  MATR Pk[DIM_STATE * DIM_STATE];      //状态误差G讲罹卣螅i：DIM_STATE * DIM_STATE
	
  MATR Pkk_1[DIM_STATE * DIM_STATE];   //一步预测状态误差G讲罹卣螅i：DIM_STATE * DIM_STATE
	
  MATR Qk[DIM_STATE * DIM_STATE];      //系统噪声G讲罹卣螅i：DIM_STATE * DIM_STATE
	
	MATR TrFk[DIM_STATE * DIM_STATE];
	
	MATR Fk_Pk[DIM_STATE * DIM_STATE];
	
  VEC InsVn[3];                        //惯导速度，单位：m/s
  
  LEN GPSLeverArm[3];                  //GPS杆臂长度
  
  VEL Vn_r[3];                         //n系下表示的杆臂速度
  
  ATTI r_InsFai;
	
	LATI r_InsLati; 
	
	LOGI r_InsLogi;
	
	HEIGHT InsHeight;
	
	ACCELER Acc_Horizontal;
	
	ACCELER Acc_All;
	
	//COUNT HeadingValidCount;
	
	COUNT Kal_Count;                     //Kalman滤波计蔵变量，单位：次
	
	COUNT ComputeFn_Count;
	
	COUNT Kal_Predict_Count;
	
	//BOOL isZeroSpeedInsRecord;
	
	//BOOL isDRInsRecord;
	
	BOOL isInsPosRecord;
	
	BOOL isHeightRecord;
	
	COUNT State;
	
} Kalman, *p_Kalman;

/*****************************************************GNSS蔵綢结构体定义********************************************************************/
typedef struct s_GNSSData 
{
    DPARA Hdop;                          //GNSS蔵綢的GDOP值
	
	  DPARA Vdop;

    LATI r_GNSSLati;                     //GNSS纬度，单位：rad

    LOGI r_GNSSLogi;                     //GNSS经度，单位：rad

    HEIGHT GNSSHeight;                   //GNSS高度，单位：m

    //HEIGHT GNSSDamped_Height;

    ATTI r_GPSHead;	//双天线航向

    ATTI r_TrackAtti;//GNSS航迹角

    VEL GNSSVn[3];     //导航坐标系下萯轴GNSS速度，萯轴分眊为GNSS北速、GNSS天速、GNSS东速，单位：m/s

    VEL GNSS_V;//水平速度

    //VEL MAX_GNSS_V;
	
	  COUNT UseSatNum;

    BOOL GNSS_State;
		
		INT8 GNSS_POS_State;
		
		UINT16 GNSS_V_State;
		
		UINT8 GNSS_Head_State;

    BOOL isVelEn;

    BOOL isPosEn;

    BOOL isHeadingEn;

    BOOL isHeightEn; 

    COUNT GNSS_Valid_Count;
		
		BOOL isGNSS_Valid_3s;

    //BOOL isGNSS_V_Static;
}GNSSData,*p_GNSSData;


/*****************************************************陀翴加速度计及温度传窯器原始蔵綢收取结构体定义********************************************************************/
typedef struct s_SenorRaw
{
		INT32 GyroRaw[3];
		
		INT32 GyroTempRaw[3];
		
		INT32 AccRaw[3];
		
		INT32 AccTempRaw[3];
		
		INT32 BoardtempRaw;//电路板温度		
}SenorRaw,*p_SenorRaw;
/*****************************************************???????????********************************************************************/
typedef struct s_Compen
{
	//?????????????????
	DPARA GyroRaw[3];                       //??????,??:????????
	DPARA AccRaw[3];                        //????????,??:????????
	
	DRAW RTGyroBias[3];
	
	MATR RTGyroScalFac[3];
	
	IPARA GyroTempRangeNum[3];
	
	
	MATR AccLeverArm[9];  //???????????????,??:m 
	
	DRAW RTAccBias[3];
	
	MATR RTAccScalFac[3];
	
	//MATR RTAccFacNonLiner[3];
	
	IPARA AccTempRangeNum[3];
	          
	//??????????????????
	DPARA Gyro[3];                            //???????,??:????????:rad?rad/s,?s,?
	DPARA Acc[3];                             //?????????,??:????????:g?m/s2?
	
	DPARA LastGyro[3];                            
	DPARA LastAcc[3];
	
	DPARA GyroTemp[3];                        //?????????,??:癈
	DPARA AccTemp[3];    	//???????????,??:癈
	
	//????????
	DPARA GyroTemp_1s_Sum[3];
	DPARA GyroTemp_1s_Mean[3];
	DPARA AccTemp_1s_Sum[3];
	DPARA AccTemp_1s_Mean[3];
	COUNT GyroCount_1s;
	COUNT AccCount_1s;

	//
	DPARA GyroTemp_Move_Mean_Buffer[3][NUM_TEMP_DIFF_BUFFER];
	DPARA AccTemp_Move_Mean_Buffer[3][NUM_TEMP_DIFF_BUFFER];

	DPARA GyroTemp_Diff[3];
	DPARA AccTemp_Diff[3];

	COUNT AccCircle_Count;
	COUNT GyroCircle_Count;
	BOOL isGyroMove_Mean_BufferInit;
	BOOL isAccMove_Mean_BufferInit;

	DPARA Boardtemp;
	
	COUNT Count_GyroErr[3];
	
	COUNT Count_AccErr[3];
	
	//BOOL isIMUFail;	
	
	UINT8 IMU_Valid;
	
	COUNT IMU_Count;
}Compen,*p_Compen;

/*****************************************************?????????????????********************************************************************/
typedef struct s_ANNCompen
{
	//?????????
	DPARA Dense1_Bias[DENSE_1_CELL_NUM];
	DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM];
	
	DPARA Dense2_Bias[DENSE_2_CELL_NUM];
	DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM];
	
	DPARA Dense3_Bias;
	DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM];
	
	
	DPARA Normalized_Temp_Max;
	DPARA Normalized_Temp_Diff_Max;
	DPARA Normalized_Output_Max;
	
	DPARA Normalized_Temp_Min;
	DPARA Normalized_Temp_Diff_Min;
	DPARA Normalized_Output_Min;
	
	DPARA Normalized_Temp_Mean;
	DPARA Normalized_Temp_Diff_Mean;
	DPARA Normalized_Output_Mean;
	//??????
	DPARA Correct_Value;
}ANNCompen,*p_ANNCompen;
#endif
