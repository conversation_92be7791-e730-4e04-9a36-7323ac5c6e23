Output/Debug/Obj/gpio_example\ -\ hpm6750/SEGGER_RTT_printf.o: \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\my_project\app\gpio\src\RTT\SEGGER_RTT_printf.c \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\my_project\app\gpio\src\RTT\SEGGER_RTT.h \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\my_project\app\gpio\src\RTT\SEGGER_RTT_Conf.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/stdlib.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_ConfDefaults.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_Conf.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_RISCV_Conf.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/stdarg.h
