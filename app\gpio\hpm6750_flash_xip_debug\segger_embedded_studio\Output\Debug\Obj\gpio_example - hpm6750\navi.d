Output/Debug/Obj/gpio_example\ -\ hpm6750/navi.o: \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\my_project\app\gpio\src\INAV\navi.c \
 ../../src/Source/inc/appmain.h ../../src/Source/inc/systick.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/stdint.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_ConfDefaults.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_Conf.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_RISCV_Conf.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/stdio.h \
 ../../src/main.h ../../../../boards/hpm6750/board.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_common.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/assert.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/stdbool.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/string.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/stdlib.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_clock_drv.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_sysctl_drv.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_sysctl_regs.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_csr_drv.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_csr_regs.h \
 ../../../../../hpm_sdk/arch/riscv/riscv_core.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_soc.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_soc_irq.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_gpio_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_plic_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_mchtmr_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_plic_sw_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_gpiom_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_adc12_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_adc16_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_acmp_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_spi_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_uart_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_can_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_wdg_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_mbx_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_ptpc_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_dmamux_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_dma_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_rng_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_keym_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_i2s_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_dao_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_pdm_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_pwm_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_hall_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_qei_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_trgm_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_synt_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_lcdc_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_cam_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_pdma_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_jpeg_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_enet_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_gptmr_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_usb_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_sdxc_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_conctl_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_i2c_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_sdp_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_femc_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_ioc_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_otp_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_ppor_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_pcfg_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_psec_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_pmon_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_pgpr_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_vad_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_pllctl_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_bpor_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_bcfg_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_butn_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_bgpr_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_rtc_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_bsec_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_bkey_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_bmon_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_tamp_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/ip/hpm_mono_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_csr_regs.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_interrupt.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_plic_drv.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_misc.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_otp_table.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_dmamux_src.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_trgmmux_src.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_iomux.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_pmic_iomux.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_batt_iomux.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_soc_feature.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_soc.h \
 ../../../../boards/hpm6750/pinmux.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_lcdc_drv.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_display_common.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_common.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_trgm_drv.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_soc_ip_feature.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_trgmmux_src.h \
 ../../src/Source/inc/deviceconfig.h \
 ../../../../../hpm_sdk/components/debug_console/hpm_debug_console.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_gpio_drv.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_mchtmr_drv.h \
 ../../../../boards/hpm6750/pinmux.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_gpiom_drv.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_gpiom_soc_drv.h \
 ../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_sd.h \
 ../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_common.h \
 ../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_card.h \
 ../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_host.h \
 ../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_osal.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_sdxc_drv.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_sdxc_soc_drv.h \
 ../../../../../hpm_sdk/middleware/hpm_sdmmc/port/hpm_sdmmc_port.h \
 ../../../../../hpm_sdk/middleware/hpm_sdmmc/lib/hpm_sdmmc_host.h \
 ../../../../../hpm_sdk/middleware/fatfs/src/common/ff.h \
 ../../../../../hpm_sdk/middleware/fatfs/src/common/ffconf.h \
 ../../../../../hpm_sdk/middleware/fatfs/src/portable/diskio.h \
 ../../src/Source/inc/INS_Data.h ../../src/Source/inc/gnss.h \
 ../../src/Common/inc/data_convert.h ../../src/Source/inc/tlhtype.h \
 ../../src/Source/inc/can_data.h ../../src/Source/inc/imu_data.h \
 ../../src/Source/inc/INS_sys.h ../../src/Source/inc/appmain.h \
 ../../src/Source/inc/INS912AlgorithmEntry.h \
 ../../src/Source/inc/deviceconfig.h ../../src/Source/inc/INS_Sys.h \
 ../../src/bsp/inc/bsp_fmc.h \
 ../../../../../hpm_sdk/arch/riscv/l1c/hpm_l1c_drv.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_romapi.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_otp_drv.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_romapi_xpi_def.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_romapi_xpi_soc_def.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_romapi_xpi_nor_def.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_romapi_xpi_def.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_romapi_xpi_ram_def.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_sdp_drv.h \
 ../../src/Protocol/UartDefine.h ../../src/bsp/inc/logger.h \
 ../../src/Source/inc/time_unify.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/time.h \
 ../../src/bsp/inc/bsp_tim.h ../../src/Source/inc/fpgad.h \
 ../../../../../hpm_sdk/soc/HPM6700/HPM6750/hpm_sysctl_drv.h \
 ../../../../../hpm_sdk/drivers/inc/hpm_gptmr_drv.h \
 ../../src/Source/inc/appdefine.h ../../src/Protocol/config.h \
 ../../src/Protocol/computerFrameParse.h ../../src/Protocol/config.h \
 ../../src/Source/inc/tlhtype.h ../../src/Source/inc/INS_Data.h \
 ../../src/Source/inc/gdtypedefine.h ../../src/Protocol/frame_analysis.h \
 ../../src/Protocol/protocol.h ../../src/Protocol/frame_analysis.h \
 ../../src/Protocol/insdef.h ../../src/Protocol/InsTestingEntry.h \
 ../../src/Source/inc/gdtypedefine.h \
 ../../src/Source/inc/INS912AlgorithmEntry.h \
 ../../src/Source/inc/datado.h ../../src/Source/inc/SetParaBao.h \
 ../../src/Source/inc/FirmwareUpdateFile.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/math.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_FP.h \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\my_project\app\gpio\src\INAV\DATASTRUCT.h \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\my_project\app\gpio\src\INAV\CONST.h \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\my_project\app\gpio\src\INAV\TYPEDEFINE.h \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\my_project\app\gpio\src\INAV\FUNCTION.h \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\my_project\app\gpio\src\INAV\ins.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/ctype.h \
 ../../src/INAV/EXTERNGLOBALDATA.h ../../src/INAV/DATASTRUCT.h
