# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.24

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: gpio_example
# Configurations: debug
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling C files.

rule C_COMPILER__app_debug
  depfile = $DEP_FILE
  deps = gcc
  command = E:\2014902\HPM6750\sdk_env_v1.6.0\toolchains\rv32imac_zicsr_zifencei_multilib_b_ext-win\bin\riscv32-unknown-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C executable.

rule C_EXECUTABLE_LINKER__demo.2eelf_debug
  command = cmd.exe /C "$PRE_LINK && E:\2014902\HPM6750\sdk_env_v1.6.0\toolchains\rv32imac_zicsr_zifencei_multilib_b_ext-win\bin\riscv32-unknown-elf-gcc.exe $FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking C executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling C files.

rule C_COMPILER__hpm_sdk_lib_debug
  depfile = $DEP_FILE
  deps = gcc
  command = E:\2014902\HPM6750\sdk_env_v1.6.0\toolchains\rv32imac_zicsr_zifencei_multilib_b_ext-win\bin\riscv32-unknown-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__hpm_sdk_lib_debug
  command = cmd.exe /C "$PRE_LINK && E:\2014902\HPM6750\sdk_env_v1.6.0\toolchains\rv32imac_zicsr_zifencei_multilib_b_ext-win\bin\riscv32-unknown-elf-ar.exe crD $TARGET_FILE $LINK_FLAGS $in && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER__hpm_sdk_gcc_lib_debug
  depfile = $DEP_FILE
  deps = gcc
  command = E:\2014902\HPM6750\sdk_env_v1.6.0\toolchains\rv32imac_zicsr_zifencei_multilib_b_ext-win\bin\riscv32-unknown-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__hpm_sdk_gcc_lib_debug
  depfile = $DEP_FILE
  deps = gcc
  command = E:\2014902\HPM6750\sdk_env_v1.6.0\toolchains\rv32imac_zicsr_zifencei_multilib_b_ext-win\bin\riscv32-unknown-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__hpm_sdk_gcc_lib_debug
  command = cmd.exe /C "$PRE_LINK && E:\2014902\HPM6750\sdk_env_v1.6.0\toolchains\rv32imac_zicsr_zifencei_multilib_b_ext-win\bin\riscv32-unknown-elf-ar.exe crD $TARGET_FILE $LINK_FLAGS $in && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = E:\2014902\HPM6750\sdk_env_v1.6.0\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM6750\sdk_env_v1.6.0\my_project\app\gpio -BE:\2014902\HPM6750\sdk_env_v1.6.0\my_project\app\gpio\hpm6750_flash_xip_debug
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = E:\2014902\HPM6750\sdk_env_v1.6.0\tools\ninja\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = E:\2014902\HPM6750\sdk_env_v1.6.0\tools\ninja\ninja.exe -t targets
  description = All primary targets available:

