Output/Debug/Obj/gpio_example\ -\ hpm6750/middleware/fatfs/src/common/ffunicode.c.o: \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\hpm_sdk\middleware\fatfs\src\common\ffunicode.c \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\hpm_sdk\middleware\fatfs\src\common\ff.h \
 C:\Users\<USER>\Desktop\code\HPM6750_INS-370M-SD\HPM6750_INS-370M-SD\hpm_sdk\middleware\fatfs\src\common\ffconf.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/stdint.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_ConfDefaults.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_Conf.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_RISCV_Conf.h
