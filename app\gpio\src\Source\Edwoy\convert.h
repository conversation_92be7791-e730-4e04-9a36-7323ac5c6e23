/**
  ******************************************************************************
  * @file    convert.h
  * <AUTHOR>
  * @brief   
  ******************************************************************************
  * @attention
  *
  ******************************************************************************
  */
  
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __CONVERT_H__
#define __CONVERT_H__

///=Peripherals==========================================================================================================
///--Timer-------------------------------------------------------------------------------------------------
#if  defined(MECH_STM32) && defined(USE_HAL_DRIVER)
#define  TIM_HDR           TIM_HandleTypeDef
#define  TIM_INITstt       TIM_Base_InitTypeDef
#define  TIM_IntFlag_Get(x,y)   __HAL_TIM_GET_IT_SOURCE(&x,y)

#elif defined(MECH_GD32) && defined(USE_STDPERIPH_DRIVER)
#define  TIM_INITstt        timer_parameter_struct
#define  TIM_PscDiv(x)      TIMER_IC_PSC_DIV##x
#define  TIM_IntFlag_Get(x,y)    timer_interrupt_flag_get(x,y)
#endif //<#if  defined(MECH_STM32) && defined(USE_HAL_DRIVER) ---else--->
///--END SPI-----------------------------------------------------------------------------------END Timer---

/*******
TIM_HandleTypeDef
*******/
///--SPI---------------------------------------------------------------------------------------------------
#if  defined(MECH_STM32) && defined(USE_HAL_DRIVER)
#define  SPI_HDR            SPI_HandleTypeDef
#define  SPI_INITstt        SPI_InitTypeDef
#define  SPI_PSC(x)         SPI_BAUDRATEPRESCALER_##x

#elif defined(MECH_GD32) && defined(USE_STDPERIPH_DRIVER)
#define  SPI_INITstt        spi_parameter_struct
#define  SPI_PSC(x)         SPI_PSC_##x

#endif //<#if  defined(MECH_STM32) && defined(USE_HAL_DRIVER) ---else--->
///--END SPI-------------------------------------------------------------------------------------END SPI---

///--GPIO--------------------------------------------------------------------------------------------------
#if  defined(MECH_STM32) && defined(USE_HAL_DRIVER)
#define  GPIO_Output              HAL_GPIO_WritePin
#define  GPIO_PinLevel_High       GPIO_PIN_SET
#define  GPIO_PinLevel_Low        GPIO_PIN_RESET
#define  GPIO_Output_Set(x,y)     HAL_GPIO_WritePin(x,y,GPIO_PIN_SET)
#define  GPIO_Output_Rst(x,y)     HAL_GPIO_WritePin(x,y,GPIO_PIN_RESET)

#elif defined(MECH_GD32) && defined(USE_STDPERIPH_DRIVER)
#define  GPIO_Output        gpio_bit_write
#define  GPIO_Output_Set    gpio_bit_set
#define  GPIO_Output_Rst    gpio_bit_reset

#define  GPIO_PinLevel_High  SET
#define  GPIO_PinLevel_Low   RESET

#endif //<#if  defined(MECH_STM32) && defined(USE_HAL_DRIVER) ---else--->
///--END GPIO-----------------------------------------------------------------------------------END GPIO---

///=End Peripherals===================================================================================End Peripherals====

///=HeadFile=============================================================================================================
#if  defined(MECH_STM32)
#define DLY_HEAD      "delay.h"
#elif defined(MECH_GD32) 
#define DLY_HEAD      "systick.h" 
#endif //<#if  defined(MECH_STM32) && defined(USE_HAL_DRIVER) ---else--->
///=End HeadFile========================================================================================End HeadFiles====

#endif // <__CONVERT_H__>

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
