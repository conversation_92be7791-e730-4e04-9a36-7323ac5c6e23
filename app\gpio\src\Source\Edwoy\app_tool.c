#include "app_tool.h"
#include "appmain.h"
#include <stdio.h>
#include <stdarg.h> 



/*****************************************************************
 * \brief 
 * \param 
 * \param 
 * \return 
******************************************************************/

/*****************************************************************
 * \brief Accumulated value verification
 * \param srcbuf,
 * \param srcqty,
 * \return Accumulated value `s low 16bit
******************************************************************/
uint16_t  app_accum_verify_8bit(uc8_t* srcbuf,u32_t srcqty)
{
	u32_t tmpvlu = 0;
	for(u32_t i = 0; i < srcqty; i++)
	{
		tmpvlu += srcbuf[i];
	}
	return (u16_t)tmpvlu;
}

/*****************************************************************
 * \brief Accumulated value verification
 * \param srcbuf,
 * \param srcqty,
 * \return Accumulated value `s low 16bit
******************************************************************/
uint8_t  crc_verify_8bit(uc8_t* srcbuf,u32_t srcqty)
{
	u32_t tmpvlu = 0;
	for(u32_t i = 0; i < srcqty; i++)
	{
		tmpvlu += srcbuf[i];
	}
	return (uint8_t)tmpvlu;
}

/*****************************************************************
 * \brief Accumulated value verification by 16bit mode
 * \param srcbuf,
 * \param srcqty,
 * \return Accumulated value `s low 16bit
 * \update
       v1.1, 2021.2.21,verified
            tmpvlu += *srcbuf++;
       v1.0, 2024.1.29, verified
            tmpvlu += srcbuf[i];
******************************************************************/
uint16_t  app_accum_verify_16bit(uc16_t* srcbuf,u32_t srcqty)
{
	u32_t tmpvlu = 0;
	for(u32_t i = 0; i < srcqty; i++)
	{	
		tmpvlu += *srcbuf++;		
	}
	return (u16_t)tmpvlu;
}


/*****************************************************************
 * \brief Accumulated value verification v2 by 16bit mode
 * \param srcbuf,
 * \param srcqty,
 * \return Accumulated value `s low 16bit
 * \update
       v1.0 ,2024.2.21, validating....
 ******************************************
	u32_t tmpvlu = 0;
	u16_t* tmpbuf = (u16_t*)srcbuf;
	for(u32_t i = 0; i < srcqty; i++)
	{
		tmpvlu += tmpbuf[i];
	}
	return (u16_t)tmpvlu;
******************************************************************/
uint16_t  app_accum_verify_16bit_v2(uvc16_t* srcbuf,u32_t srcqty)
{
	u32_t tmpvlu = 0;
	for(u32_t i = 0; i < srcqty; i++)
	{
		tmpvlu += *srcbuf++;		
	}
	return (u16_t)tmpvlu;
}

//-- USE_FULL_ASSERT ---------------------------------------------------------------------------------

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  app_ins422_printf("AsstErr at line%d in file\"%s\"!!\r\n",line,file);
}

#endif //< USE_FULL_ASSERT >
//-- END USE_FULL_ASSERT -------------------------------------------------- END USE_FULL_ASSERT ------	