***********************************************************************************************
***                                                                                         ***
***                                    LINK INFORMATION                                     ***
***                                                                                         ***
***********************************************************************************************

Linker version:

  SEGGER RISC-V Linker 4.38.14 compiled Sep 10 2024 17:37:42
  Copyright (c) 2017-2024 SEGGER Microcontroller GmbH    www.segger.com


***********************************************************************************************
***                                                                                         ***
***                                     MODULE SUMMARY                                      ***
***                                                                                         ***
***********************************************************************************************

Memory use by input file:

  Object File                                       RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  align.o                                             1 692          96                        
  app_tool.o                                            112                                    
  board.c.o                                           4 126       1 378                        
  bsp_fmc.o                                              44                                    
  bsp_gpio.o                                            452                                    
  bsp_tim.o                                              16                                  20
  computerFrameParse.o                                  124                                 596
  datado.o                                              196          28           4           4
  diskio.c.o                                            626                                    
  dynamic_align.o                                     1 736          24                        
  ff.c.o                                             28 934         285                   1 163
  ff_queue.o                                            844                                   1
  ffunicode.c.o                                         568      87 894                        
  FirmwareUpdateFile.o                                  170                                    
  flash.o                                               552          31                     256
  fpgad.o                                               596          40                     762
  frame_analysis.o                                    4 122         360                     260
  gd32f4xx_it.o                                         212                                   2
  gdwatch.o                                             744          68                  59 798
  hpm_bootheader.c.o                                                144                        
  hpm_clock_drv.c.o                                   1 662         160                       4
  hpm_debug_console.c.o                                 256                       4           8
  hpm_dma_drv.c.o                                     1 076                                    
  hpm_femc_drv.c.o                                    1 126                                    
  hpm_gpio_drv.c.o                                      234                                    
  hpm_gptmr_drv.c.o                                     436                                    
  hpm_l1c_drv.o                                         482         191                        
  hpm_pcfg_drv.c.o                                       72                                    
  hpm_pllctl_drv.c.o                                  1 100          24                        
  hpm_pmp_drv.c.o                                       954         128                        
  hpm_sdmmc_common.c.o                                  858                                    
  hpm_sdmmc_disk.c.o                                  1 854                     216      50 713
  hpm_sdmmc_host.c.o                                  4 380                                    
  hpm_sdmmc_osal.o                                      480                                    
  hpm_sdmmc_port.c.o                                    506                                    
  hpm_sdmmc_sd.c.o                                    7 670         253                        
  hpm_sdxc_drv.c.o                                    4 966                                    
  hpm_sysctl_drv.c.o                                    546                                    
  hpm_uart_drv.c.o                                    1 030          24                        
  INS_Data.o                                            230                                 353
  INS_Init.o                                            370          44                      17
  INS_Output.o                                        6 172          59                   2 054
  InsTestingEntry.o                                  15 870         240                   2 798
  kalman.o                                           12 370         376                        
  main.o                                              1 480         344           1      30 826
  matvecmath.o                                        1 398           8                        
  navi.o                                              6 736         224                        
  pinmux.c.o                                          1 256                                    
  read_and_check_gnss_data.o                          1 328          64                        
  readpaoche.o                                        7 348          88           1           1
  reset.c.o                                             204                                    
  sd_fatfs.o                                          4 114       2 147           8       4 367
  SetParaBao.o                                       15 828         256           5         604
  startup.s.o                                           152         512                        
  system.c.o                                            134                                    
  systick.o                                              24                                    
  timer.o                                             1 076                                   4
  trap.c.o                                              560          64                        
  uart.o                                              1 396         125         136      42 260
  uart_dma.o                                          1 708         258           1            
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (60 objects)                             155 308      95 937         376     196 871
  ---------------------------------------------  ----------  ----------  ----------  ----------
  heapops_basic_rv32gc_d_balanced.a                     102                                   4
  heapops_disable_interrupts_locking_rv32gc_d_balanced.a
                                                         70                                   4
  libc_rv32gc_d_balanced.a                           10 834       1 958                      28
  mbops_timeops_rv32gc_d_balanced.a                     268         549          20           4
  SEGGER_RV32_crtinit_rv32gc_d_balanced.a               238                                    
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (5 archives)                              11 512       2 507          20          40
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Linker created (shared data, fills, blocks):                   78 356                  32 768
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            166 820     176 800         396     229 679
  =============================================  ==========  ==========  ==========  ==========

Memory use by archive member:

  Archive member                                    RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
                                                      3 278         248                        
  convops.o (libc_rv32gc_d_balanced.a)                  562                                    
  errno.o (libc_rv32gc_d_balanced.a)                     14                                   4
  execops.o (libc_rv32gc_d_balanced.a)                  258          30                      24
  fileops.o (libc_rv32gc_d_balanced.a)                  170                                    
  floatasmops_rv.o (libc_rv32gc_d_balanced.a)           288          24                        
  floatops.o (libc_rv32gc_d_balanced.a)               2 416         368                        
  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
                                                         70                                   4
  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
                                                        102                                   4
  intasmops_rv.o (libc_rv32gc_d_balanced.a)              76                                    
  intops.o (libc_rv32gc_d_balanced.a)                 2 150       1 024                        
  mbops.o (mbops_timeops_rv32gc_d_balanced.a)           268         549          20           4
  prinops.o (libc_rv32gc_d_balanced.a)                  586         192                        
  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
                                                        238                                    
  strasmops_rv.o (libc_rv32gc_d_balanced.a)             528                                    
  strops.o (libc_rv32gc_d_balanced.a)                   448                                    
  utilops.o (libc_rv32gc_d_balanced.a)                   60          72                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (17 members from 5 archives)              11 512       2 507          20          40
  Objects (60 files)                                155 308      95 937         376     196 871
  Linker created (shared data, fills, blocks):                   78 356                  32 768
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            166 820     176 800         396     229 679
  =============================================  ==========  ==========  ==========  ==========

Memory use by linker:

  Description                                       RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Initialization table                                           78 140                        
  Memory for block 'heap'                                                                16 384
  Memory for block 'stack'                                                               16 384
  Merged section data (64-bit)                                      216                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (linker created):                                     78 356                  32 768
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Objects (60 files)                                155 308      95 937         376     196 871
  Archives (5 files)                                 11 512       2 507          20          40
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            166 820     176 800         396     229 679
  =============================================  ==========  ==========  ==========  ==========


***********************************************************************************************
***                                                                                         ***
***                                     SECTION DETAIL                                      ***
***                                                                                         ***
***********************************************************************************************

Sections by address:

  Range              Symbol or [section] Name         Size  Al  Init  Ac  Object File
  -----------------  -------------------------  ----------  --  ----  --  -----------
  00000000-000001ff  __vector_table                    512  512
                                                                Init  RO  startup.s.o
  00000200-00000311  isr_gpio                          274   4  Init  RX  main.o
  00000312-00000313  ( ALIGN .=.+2 )                     2   -  ----  -   -
  00000314-00000435  tick_ms_isr                       290   4  Init  RX  timer.o
  00000436-00000437  ( ALIGN .=.+2 )                     2   -  ----  -   -
  00000438-00000549  sdcard_isr                        274   4  Init  RX  hpm_sdmmc_disk.c.o
  0000054a-0000054b  ( ALIGN .=.+2 )                     2   -  ----  -   -
  0000054c-00000551  nmi_handler                         6   4  Init  RX  startup.s.o
  00000552-00000553  ( ALIGN .=.+2 )                     2   -  ----  -   -
  00000554-0000073d  irq_handler_trap                  490   4  Init  RX  trap.c.o
  0000073e-000022f7  fmc2sinsraw                     7 098   2  Init  RX  readpaoche.o
  000022f8-00003b9d  AnalyticCoordinateAxis          6 310   2  Init  RX  InsTestingEntry.o
  00003b9e-00004dbf  f_mkfs                          4 642   2  Init  RX  ff.c.o
  00004dc0-00005d8f  frame_pack_and_send             4 048   2  Init  RX  frame_analysis.o
  00005d90-00006ba3  output_gdw_do                   3 604   2  Init  RX  INS_Output.o
  00006ba4-00007447  AlgorithmAct                    2 212   2  Init  RX  InsTestingEntry.o
  00007448-00007cbd  ComputeFn                       2 166   2  Init  RX  kalman.o
  00007cbe-00008409  mount_volume                    1 868   2  Init  RX  ff.c.o
  0000840a-0000898d  FPGATo422_00BB_send             1 412   2  Init  RX  InsTestingEntry.o
  0000898e-00008ee9  ReadFileToSd                    1 372   2  Init  RX  sd_fatfs.o
  00008eea-000094d3  ErrStore_1s                     1 514   2  Init  RX  kalman.o
  000094d4-00009a05  Kalman_StartUp                  1 330   2  Init  RX  kalman.o
  00009a06-00009f4b  ComputeZk                       1 350   2  Init  RX  kalman.o
  00009f4c-0000a407  sd_decode_csd                   1 212   2  Init  RX  hpm_sdmmc_sd.c.o
  0000a408-0000a917  create_name                     1 296   2  Init  RX  ff.c.o
  0000a918-0000adf5  f_lseek                         1 246   2  Init  RX  ff.c.o
  0000adf6-0000b287  f_open                          1 170   2  Init  RX  ff.c.o
  0000b288-0000b731  ErrCorrect_1_Navi_Time          1 194   2  Init  RX  kalman.o
  0000b732-0000bb93  INS912_Output                   1 122   2  Init  RX  INS_Output.o
  0000bb94-0000bfe7  f_printf                        1 108   2  Init  RX  ff.c.o
  0000bfe8-0000c44b  SetParaAll                      1 124   2  Init  RX  SetParaBao.o
  0000c44c-0000c883  output_fpga_void                1 080   2  Init  RX  INS_Output.o
  0000c884-0000cc5f  dir_register                      988   2  Init  RX  ff.c.o
  0000cc60-0000d009  UartDmaRecSetPara                 938   2  Init  RX  SetParaBao.o
  0000d00a-0000d3bd  f_write                           948   2  Init  RX  ff.c.o
  0000d3be-0000d771  f_async_write                     948   2  Init  RX  ff.c.o
  0000d772-0000db3f  Rk_Init                           974   2  Init  RX  kalman.o
  0000db40-0000df07  fpgadata_Predo_chen_SetAlgParm_setRParm_gyro
                                                       968   2  Init  RX  InsTestingEntry.o
  0000df08-0000e2cf  fpgadata_Predo_chen_SetAlgParm_setRParm_acc
                                                       968   2  Init  RX  InsTestingEntry.o
  0000e2d0-0000e5f9  analysisRxdata                    810   2  Init  RX  uart.o
  0000e5fa-0000e91f  NaviCompute                       806   2  Init  RX  navi.o
  0000e920-0000ec63  f_read                            836   2  Init  RX  ff.c.o
  0000ec64-0000ef61  InitParaToAlgorithm               766   2  Init  RX  SetParaBao.o
  0000ef62-0000f211  sd_card_init                      688   2  Init  RX  hpm_sdmmc_sd.c.o
  0000f212-0000f50f  SaveGNSSData                      766   2  Init  RX  read_and_check_gnss_data.o
  0000f510-0000f7b7  dir_find                          680   2  Init  RX  ff.c.o
  0000f7b8-0000fa6f  ReadParaFromFlash                 696   2  Init  RX  SetParaBao.o
  0000fa70-0000fd2b  Mat_Inv                           700   2  Init  RX  matvecmath.o
  0000fd2c-0000ffd7  sdmmchost_init_io                 684   2  Init  RX  hpm_sdmmc_host.c.o
  0000ffd8-00010275  fpgadata_Predo_chen_SetAlgParm_acc
                                                       670   2  Init  RX  InsTestingEntry.o
  00010276-000104cf  fpgadata_Predo_chen_OutDataSet
                                                       602   2  Init  RX  InsTestingEntry.o
  000104d0-00010755  create_chain                      646   2  Init  RX  ff.c.o
  00010756-000109ab  f_sync                            598   2  Init  RX  ff.c.o
  000109ac-00010c33  create_partition                  648   2  Init  RX  ff.c.o
  00010c34-00010eb9  gnss_check_bind                   646   2  Init  RX  InsTestingEntry.o
  00010eba-0001112b  get_fat                           626   2  Init  RX  ff.c.o
  0001112c-00011393  fpgadata_Predo_chen_SetAlgParm_gyro
                                                       616   2  Init  RX  InsTestingEntry.o
  00011394-00011613  CnbToQ                            640   2  Init  RX  navi.o
  00011614-0001183b  DynamicInertialSysAlignCompute
                                                       552   2  Init  RX  dynamic_align.o
  0001183c-00011a73  RestoreFactory                    568   2  Init  RX  SetParaBao.o
  00011a74-00011ca5  ComputeSi                         562   2  Init  RX  dynamic_align.o
  00011ca6-00011ec7  put_fat                           546   2  Init  RX  ff.c.o
  00011ec8-000120f3  sdxc_set_adma2_desc               556   2  Init  RX  hpm_sdxc_drv.c.o
  000120f4-000122ff  follow_path                       524   2  Init  RX  ff.c.o
  00012300-00012523  dma_setup_channel                 548   2  Init  RX  hpm_dma_drv.c.o
  00012524-00012717  remove_chain                      500   2  Init  RX  ff.c.o
  00012718-0001291d  QToCnb                            518   2  Init  RX  navi.o
  0001291e-00012b05  load_xdir                         488   2  Init  RX  ff.c.o
  00012b06-00012cef  sd_write_blocks                   490   2  Init  RX  hpm_sdmmc_sd.c.o
  00012cf0-00012ed3  dir_read                          484   2  Init  RX  ff.c.o
  00012ed4-000130b1  sd_read_blocks                    478   2  Init  RX  hpm_sdmmc_sd.c.o
  000130b2-00013277  InertialSysAlignCompute           454   2  Init  RX  align.o
  00013278-00013447  ComputeKk                         464   2  Init  RX  kalman.o
  00013448-00013603  check_fs                          444   2  Init  RX  ff.c.o
  00013604-000137c3  sd_start_write_blocks             448   2  Init  RX  hpm_sdmmc_sd.c.o
  000137c4-00013997  dma_config_linked_descriptor
                                                       468   2  Init  RX  hpm_dma_drv.c.o
  00013998-00013b6b  GNSSAndHeadDataTest               468   2  Init  RX  read_and_check_gnss_data.o
  00013b6c-00013d39  CorrectAtti                       462   2  Init  RX  kalman.o
  00013d3a-00013edb  uart_dma_tx_send                  418   2  Init  RX  uart_dma.o
  00013edc-00014099  putc_bfd                          446   2  Init  RX  ff.c.o
  0001409a-0001424d  mcusendtopcdriversdata            436   2  Init  RX  gdwatch.o
  0001424e-000143fd  SetParaGnssInitValue              432   2  Init  RX  SetParaBao.o
  000143fe-000145b9  sd_decode_status                  444   2  Init  RX  hpm_sdmmc_sd.c.o
  000145ba-0001476f  sdxc_parse_interrupt_status
                                                       438   2  Init  RX  hpm_sdxc_drv.c.o
  00014770-00014923  ComputeQ                          436   2  Init  RX  navi.o
  00014924-00014adb  ComputeFk                         440   2  Init  RX  kalman.o
  00014adc-00014c85  sdxc_set_adma3_desc               426   2  Init  RX  hpm_sdxc_drv.c.o
  00014c86-00014e25  sdxc_transfer_nonblocking         416   2  Init  RX  hpm_sdxc_drv.c.o
  00014e26-00014fa3  f_unlink                          382   2  Init  RX  ff.c.o
  00014fa4-00015143  CnbToAtti                         416   2  Init  RX  navi.o
  00015144-000152dd  ComputeVn                         410   2  Init  RX  navi.o
  000152de-00015465  sdmmchost_power_control           392   2  Init  RX  hpm_sdmmc_host.c.o
  00015466-000155f1  SysInit                           396   2  Init  RX  navi.o
  000155f2-00015781  ld_qword                          400   2  Init  RX  ff.c.o
  00015782-000158f5  SetParaUpdateStart                372   2  Init  RX  SetParaBao.o
  000158f6-00015a35  ParaUpdateHandle                  320   2  Init  RX  SetParaBao.o
  00015a36-00015bb9  uart_init                         388   2  Init  RX  hpm_uart_drv.c.o
  00015bba-00015d31  SetParaFactorGyro                 376   2  Init  RX  SetParaBao.o
  00015d32-00015ea9  SetParaFactorAcc                  376   2  Init  RX  SetParaBao.o
  00015eaa-00016027  uart_dma_recv_polling             382   2  Init  RX  uart_dma.o
  00016028-00016193  uart_dma_init                     364   2  Init  RX  uart_dma.o
  00016194-000162c7  initializationdriversettings
                                                       308   2  Init  RX  gdwatch.o
  000162c8-0001641d  output_fpgatxt_do                 342   2  Init  RX  INS_Output.o
  0001641e-00016561  bsp_gpio_init                     324   2  Init  RX  bsp_gpio.o
  00016562-000166b5  WriteBB00FileToSd                 340   2  Init  RX  sd_fatfs.o
  000166b6-00016831  Hk_Init                           380   2  Init  RX  kalman.o
  00016832-000169a5  gen_numname                       372   2  Init  RX  ff.c.o
  000169a6-00016b17  ff_wtoupper                       370   2  Init  RX  ffunicode.c.o
  00016b18-00016c85  board_init_sd_host_params         366   2  Init  RX  hpm_sdmmc_port.c.o
  00016c86-00016dcd  sdmmchost_transfer                328   2  Init  RX  hpm_sdmmc_host.c.o
  00016dce-00016f2d  ReadPara_4                        352   2  Init  RX  SetParaBao.o
  00016f2e-00017079  KalCompute                        332   2  Init  RX  kalman.o
  0001707a-000171bf  sd_probe_bus_voltage              326   2  Init  RX  hpm_sdmmc_sd.c.o
  000171c0-00017321  uart_calculate_baudrate           354   2  Init  RX  hpm_uart_drv.c.o
  00017322-00017471  dir_next                          336   2  Init  RX  ff.c.o
  00017472-000175b7  SetParaBaud                       326   2  Init  RX  SetParaBao.o
  000175b8-000176b9  main                              258   2  Init  RX  main.o
  000176ba-000177eb  SetParaUpdateSend                 306   2  Init  RX  SetParaBao.o
  000177ec-00017935  sdxc_prepare_cmd_xfer             330   2  Init  RX  hpm_sdxc_drv.c.o
  00017936-00017a67  sdxc_error_recovery               306   2  Init  RX  hpm_sdxc_drv.c.o
  00017a68-00017b69  fpgadata_Predo_chen_preAlgParm_370
                                                       258   2  Init  RX  InsTestingEntry.o
  00017b6a-00017cad  Qua_Mul                           324   2  Init  RX  matvecmath.o
  00017cae-00017def  st_qword                          322   2  Init  RX  ff.c.o
  00017df0-00017f31  gptmr_channel_config              322   2  Init  RX  hpm_gptmr_drv.c.o
  00017f32-00018063  ReadPara_2                        306   2  Init  RX  SetParaBao.o
  00018064-00018197  sd_switch_function                308   2  Init  RX  hpm_sdmmc_sd.c.o
  00018198-000182c7  SetParaVector                     304   2  Init  RX  SetParaBao.o
  000182c8-000183f7  SetParaGnss                       304   2  Init  RX  SetParaBao.o
  000183f8-00018527  SetParaDeviation                  304   2  Init  RX  SetParaBao.o
  00018528-00018657  SetParaAngle                      304   2  Init  RX  SetParaBao.o
  00018658-00018795  AttiToCnb                         318   2  Init  RX  navi.o
  00018796-000188a7  SetParaUpdateEnd                  274   2  Init  RX  SetParaBao.o
  000188a8-000189c5  SaveParaToFlash                   286   2  Init  RX  SetParaBao.o
  000189c6-00018ae9  ComputeVibn                       292   2  Init  RX  navi.o
  00018aea-00018bfb  sd_disk_initialize                274   2  Init  RX  hpm_sdmmc_disk.c.o
  00018bfc-00018d29  ComputePos                        302   2  Init  RX  navi.o
  00018d2a-00018e31  sync_fs                           264   2  Init  RX  ff.c.o
  00018e32-00018f59  show_error_string                 296   2  Init  RX  sd_fatfs.o
  00018f5a-00019029  fpgadata_syn_count_do             208   2  Init  RX  fpgad.o
  0001902a-0001913f  sd_polling_card_status_busy
                                                       278   2  Init  RX  hpm_sdmmc_sd.c.o
  00019140-0001924d  SetParaCoord                      270   2  Init  RX  SetParaBao.o
  0001924e-0001935b  sd_set_bus_timing                 270   2  Init  RX  hpm_sdmmc_sd.c.o
  0001935c-00019469  create_xdir                       270   2  Init  RX  ff.c.o
  0001946a-00019583  rom_xpi_nor_erase_sector          282   2  Init  RX  flash.o
  00019584-0001969b  tchar2uni                         280   2  Init  RX  ff.c.o
  0001969c-0001977f  get_fpgadata_do                   228   2  Init  RX  fpgad.o
  00019780-0001988f  dir_sdi                           272   2  Init  RX  ff.c.o
  00019890-00019997  SetParaFrequency                  264   2  Init  RX  SetParaBao.o
  00019998-00019a9d  SetParaCalibration                262   2  Init  RX  SetParaBao.o
  00019a9e-00019b8d  SetParaSdHandle                   240   2  Init  RX  SetParaBao.o
  00019b8e-00019c91  ReadPara_1                        260   2  Init  RX  SetParaBao.o
  00019c92-00019d79  Fatfs_Init                        232   2  Init  RX  sd_fatfs.o
  00019d7a-00019e87  ComputeAttiRate                   270   2  Init  RX  navi.o
  00019e88-00019f93  get_boot_reason                   268   2  Init  RX  main.o
  00019f94-0001a085  ReadPara                          242   2  Init  RX  SetParaBao.o
  0001a086-0001a17f  ReadPara_3                        250   2  Init  RX  SetParaBao.o
  0001a180-0001a279  ReadPara_0                        250   2  Init  RX  SetParaBao.o
  0001a27a-0001a36f  DynamicNavi_Init                  246   2  Init  RX  navi.o
  0001a370-0001a467  cmp_lfn                           248   2  Init  RX  ff.c.o
  0001a468-0001a551  SetParaDataOutType                234   2  Init  RX  SetParaBao.o
  0001a552-0001a5f7  TIMER2_IRQHandler                 166   2  Init  RX  gd32f4xx_it.o
  0001a5f8-0001a6e7  SetParaTime                       240   2  Init  RX  SetParaBao.o
  0001a6e8-0001a7cd  caninfupdate                      230   2  Init  RX  INS_Data.o
  0001a7ce-0001a8c3  sdmmchost_vsel_pin_control
                                                       246   2  Init  RX  hpm_sdmmc_host.c.o
  0001a8c4-0001a9a5  sdmmchost_start_transfer          226   2  Init  RX  hpm_sdmmc_host.c.o
  0001a9a6-0001aa8f  sd_read_status                    234   2  Init  RX  hpm_sdmmc_sd.c.o
  0001aa90-0001ab79  SetParaKalmanR                    234   2  Init  RX  SetParaBao.o
  0001ab7a-0001ac63  SetParaKalmanQ                    234   2  Init  RX  SetParaBao.o
  0001ac64-0001ad4d  SetParaFilter                     234   2  Init  RX  SetParaBao.o
  0001ad4e-0001ae3d  put_lfn                           240   2  Init  RX  ff.c.o
  0001ae3e-0001aefd  INS912AlgorithmEntry              192   2  Init  RX  InsTestingEntry.o
  0001aefe-0001aff3  Qk_Init                           246   2  Init  RX  kalman.o
  0001aff4-0001b0e9  Pk_Init                           246   2  Init  RX  kalman.o
  0001b0ea-0001b1d1  pick_lfn                          232   2  Init  RX  ff.c.o
  0001b1d2-0001b2b1  SetParaGyroType                   224   2  Init  RX  SetParaBao.o
  0001b2b2-0001b391  SetParaGpsType                    224   2  Init  RX  SetParaBao.o
  0001b392-0001b471  SetParaDebugMode                  224   2  Init  RX  SetParaBao.o
  0001b472-0001b54f  sdxc_error_recovery_first_half
                                                       222   2  Init  RX  hpm_sdxc_drv.c.o
  0001b550-0001b611  sdmmchost_init                    194   2  Init  RX  hpm_sdmmc_host.c.o
  0001b612-0001b6eb  sdmmc_card_async_write            218   2  Init  RX  hpm_sdmmc_disk.c.o
  0001b6ec-0001b7c1  sdmmchost_irq_handler             214   2  Init  RX  hpm_sdmmc_host.c.o
  0001b7c2-0001b89d  timer_config                      220   2  Init  RX  timer.o
  0001b89e-0001b985  find_bitmap                       232   2  Init  RX  ff.c.o
  0001b986-0001ba5d  SetParaColliery_Operate           216   2  Init  RX  SetParaBao.o
  0001ba5e-0001bb29  FinishInertialSysAlign            204   2  Init  RX  align.o
  0001bb2a-0001bbf5  FinishDynamicInertialSysAlign
                                                       204   2  Init  RX  dynamic_align.o
  0001bbf6-0001bcb7  sdmmchost_switch_to_1v8           194   2  Init  RX  hpm_sdmmc_host.c.o
  0001bcb8-0001bd97  hpm_sdmmc_osal_event_wait         224   2  Init  RX  hpm_sdmmc_osal.o
  0001bd98-0001be6b  SetParaUpdateStop                 212   2  Init  RX  SetParaBao.o
  0001be6c-0001bf53  ComputeCie                        232   2  Init  RX  align.o
  0001bf54-0001c029  sd_send_scr                       214   2  Init  RX  hpm_sdmmc_sd.c.o
  0001c02a-0001c103  GNSS_Valid_PPSStart               218   2  Init  RX  InsTestingEntry.o
  0001c104-0001c1d3  store_xdir                        208   2  Init  RX  ff.c.o
  0001c1d4-0001c2a9  Virtual_PPS_insert_5hz            214   2  Init  RX  InsTestingEntry.o
  0001c2aa-0001c385  change_bitmap                     220   2  Init  RX  ff.c.o
  0001c386-0001c457  dir_alloc                         210   2  Init  RX  ff.c.o
  0001c458-0001c52b  sdmmc_card_write                  212   2  Init  RX  hpm_sdmmc_disk.c.o
  0001c52c-0001c5ff  sdmmc_card_read                   212   2  Init  RX  hpm_sdmmc_disk.c.o
  0001c600-0001c6cd  Navi_Init                         206   2  Init  RX  navi.o
  0001c6ce-0001c797  Kalman_Init                       202   2  Init  RX  kalman.o
  0001c798-0001c86f  sdxc_receive_cmd_response         216   2  Init  RX  hpm_sdxc_drv.c.o
  0001c870-0001c92f  sdxc_perform_tuning_flow_sequence
                                                       192   2  Init  RX  hpm_sdxc_drv.c.o
  0001c930-0001c9e7  WriteFileOpenFromSd               184   2  Init  RX  sd_fatfs.o
  0001c9e8-0001cab7  ComputePk                         208   2  Init  RX  kalman.o
  0001cab8-0001cb77  ComputeCib0i                      192   2  Init  RX  align.o
  0001cb78-0001cc4d  extract_csd_field                 214   2  Init  RX  hpm_sdmmc_common.c.o
  0001cc4e-0001cd17  dir_remove                        202   2  Init  RX  ff.c.o
  0001cd18-0001cdd5  uart_rx_dma_autorun               190   2  Init  RX  uart_dma.o
  0001cdd6-0001ce89  sdmmchost_send_command            180   2  Init  RX  hpm_sdmmc_host.c.o
  0001ce8a-0001cf55  ComputeDelSenbb                   204   2  Init  RX  navi.o
  0001cf56-0001d017  test_gpio_input_interrupt         194   2  Init  RX  main.o
  0001d018-0001d0d9  sdxc_init                         194   2  Init  RX  hpm_sdxc_drv.c.o
  0001d0da-0001d19b  find_volume                       194   2  Init  RX  ff.c.o
  0001d19c-0001d261  ff_queue_push                     198   2  Init  RX  ff_queue.o
  0001d262-0001d30d  LEDIndicator                      172   2  Init  RX  INS_Init.o
  0001d30e-0001d3d1  ComputeXk                         196   2  Init  RX  kalman.o
  0001d3d2-0001d497  ff_uni2oem                        198   2  Init  RX  ffunicode.c.o
  0001d498-0001d543  sd_init                           172   2  Init  RX  hpm_sdmmc_sd.c.o
  0001d544-0001d5e5  SdFileOperateTypeSet              162   2  Init  RX  sd_fatfs.o
  0001d5e6-0001d6a9  ComputeG                          196   2  Init  RX  navi.o
  0001d6aa-0001d765  ComputeWnbb                       188   2  Init  RX  navi.o
  0001d766-0001d821  ComputeCen                        188   2  Init  RX  align.o
  0001d822-0001d8c7  ComputeVi                         166   2  Init  RX  align.o
  0001d8c8-0001d979  pnavout_set                       178   2  Init  RX  InsTestingEntry.o
  0001d97a-0001da25  ComputeSib0                       172   2  Init  RX  dynamic_align.o
  0001da26-0001da9d  WriteFileToSd                     120   2  Init  RX  sd_fatfs.o
  0001da9e-0001db39  DeleteFileFromSd                  156   2  Init  RX  sd_fatfs.o
  0001db3a-0001dbdf  get_ldnumber                      166   2  Init  RX  ff.c.o
  0001dbe0-0001dc7d  sdxc_set_data_timeout             158   2  Init  RX  hpm_sdxc_drv.c.o
  0001dc7e-0001dd1b  WriteCOM3FileToSd                 158   2  Init  RX  sd_fatfs.o
  0001dd1c-0001ddb3  sd_transfer                       152   2  Init  RX  hpm_sdmmc_sd.c.o
  0001ddb4-0001de4b  dir_clear                         152   2  Init  RX  ff.c.o
  0001de4c-0001dedf  CloseFileToSd                     148   2  Init  RX  sd_fatfs.o
  0001dee0-0001df7d  sd_convert_data_endian            158   2  Init  RX  hpm_sdmmc_sd.c.o
  0001df7e-0001e01f  Mat_Mul                           162   2  Init  RX  matvecmath.o
  0001e020-0001e0bb  ComputePkk_1_Step2                156   2  Init  RX  kalman.o
  0001e0bc-0001e157  xname_sum                         156   2  Init  RX  ff.c.o
  0001e158-0001e1f3  sdmmchost_is_card_detected
                                                       156   2  Init  RX  hpm_sdmmc_host.c.o
  0001e1f4-0001e293  sd_disk_ioctl                     160   2  Init  RX  hpm_sdmmc_disk.c.o
  0001e294-0001e333  DynamicInertialSysAlign_Init
                                                       160   2  Init  RX  dynamic_align.o
  0001e334-0001e3c9  UartIrqSendMsg                    150   2  Init  RX  uart.o
  0001e3ca-0001e459  sdxc_set_adma_table_config
                                                       144   2  Init  RX  hpm_sdxc_drv.c.o
  0001e45a-0001e4f5  SysVarDefaultSet                  156   2  Init  RX  navi.o
  0001e4f6-0001e58d  InertialSysAlign_Init             152   2  Init  RX  align.o
  0001e58e-0001e629  ComputeRmRn                       156   2  Init  RX  navi.o
  0001e62a-0001e6c3  sd_decode_scr                     154   2  Init  RX  hpm_sdmmc_sd.c.o
  0001e6c4-0001e745  get_fpgadata_after_otherDataDo
                                                       130   2  Init  RX  fpgad.o
  0001e746-0001e7dd  ff_queue_get_buffer_by_sector
                                                       152   2  Init  RX  ff_queue.o
  0001e7de-0001e867  ReturnColliery_Operate            138   2  Init  RX  main.o
  0001e868-0001e8ef  sd_set_bus_width                  136   2  Init  RX  hpm_sdmmc_sd.c.o
  0001e8f0-0001e977  ff_queue_poll                     136   2  Init  RX  ff_queue.o
  0001e978-0001e9f7  f_mount                           128   2  Init  RX  ff.c.o
  0001e9f8-0001ea7d  sync_window                       134   2  Init  RX  ff.c.o
  0001ea7e-0001eaed  set_pwm_waveform_edge_aligned_frequency
                                                       112   2  Init  RX  timer.o
  0001eaee-0001eb79  UpdateStop_SetHead                140   2  Init  RX  SetParaBao.o
  0001eb7a-0001ec05  UpdateStart_SetHead               140   2  Init  RX  SetParaBao.o
  0001ec06-0001ec91  UpdateSend_SetHead                140   2  Init  RX  SetParaBao.o
  0001ec92-0001ed1d  UpdateEnd_SetHead                 140   2  Init  RX  SetParaBao.o
  0001ed1e-0001eda9  SendPara_SetHead                  140   2  Init  RX  SetParaBao.o
  0001edaa-0001ee35  ReadPara4_SetHead                 140   2  Init  RX  SetParaBao.o
  0001ee36-0001eec1  ReadPara3_SetHead                 140   2  Init  RX  SetParaBao.o
  0001eec2-0001ef4d  ReadPara2_SetHead                 140   2  Init  RX  SetParaBao.o
  0001ef4e-0001efd9  ReadPara1_SetHead                 140   2  Init  RX  SetParaBao.o
  0001efda-0001f065  ReadPara0_SetHead                 140   2  Init  RX  SetParaBao.o
  0001f066-0001f0ef  sdxc_transfer_cb                  138   2  Init  RX  ff_queue.o
  0001f0f0-0001f173  UartIrqInit                       132   2  Init  RX  uart.o
  0001f174-0001f1d3  INS_Init                           96   2  Init  RX  INS_Init.o
  0001f1d4-0001f24d  WriteCom3FileOpenFromSd           122   2  Init  RX  sd_fatfs.o
  0001f24e-0001f2c7  WriteBB00FileOpenFromSd           122   2  Init  RX  sd_fatfs.o
  0001f2c8-0001f34b  sdxc_set_transfer_config          132   2  Init  RX  hpm_sdxc_drv.c.o
  0001f34c-0001f3af  sduart_recv_polling               100   2  Init  RX  uart.o
  0001f3b0-0001f429  load_obj_xdir                     122   2  Init  RX  ff.c.o
  0001f42a-0001f4ab  Vec_Cross                         130   2  Init  RX  matvecmath.o
  0001f4ac-0001f521  SdFileReadOperate                 118   2  Init  RX  sd_fatfs.o
  0001f522-0001f58b  sdmmc_enable_auto_tuning          106   2  Init  RX  hpm_sdmmc_common.c.o
  0001f58c-0001f603  sd_send_csd                       120   2  Init  RX  hpm_sdmmc_sd.c.o
  0001f604-0001f677  sd_mount_fs                       116   2  Init  RX  sd_fatfs.o
  0001f678-0001f6eb  ReadCom3FileOpenFromSd            116   2  Init  RX  sd_fatfs.o
  0001f6ec-0001f75f  ReadBB00FileOpenFromSd            116   2  Init  RX  sd_fatfs.o
  0001f760-0001f7dd  sdxc_set_dma_config               126   2  Init  RX  hpm_sdxc_drv.c.o
  0001f7de-0001f853  sd_mkfs                           118   2  Init  RX  sd_fatfs.o
  0001f854-0001f8cf  ComputeDeg_Ex                     124   2  Init  RX  navi.o
  0001f8d0-0001f935  test_uart_recv_polling            102   2  Init  RX  uart.o
  0001f936-0001f993  sdmmchost_set_cardclk_delay_chain
                                                        94   2  Init  RX  hpm_sdmmc_host.c.o
  0001f994-0001fa0d  hpm_csr_get_core_mcycle           122   2  Init  RX  hpm_sdmmc_osal.o
  0001fa0e-0001fa87  hpm_csr_get_core_mcycle           122   2  Init  RX  hpm_sdmmc_sd.c.o
  0001fa88-0001fb01  SaveINSData                       122   2  Init  RX  kalman.o
  0001fb02-0001fb75  KalPredict                        116   2  Init  RX  kalman.o
  0001fb76-0001fbcf  sdxc_perform_auto_tuning           90   2  Init  RX  hpm_sdxc_drv.c.o
  0001fbd0-0001fc41  sd_all_send_cid                   114   2  Init  RX  hpm_sdmmc_sd.c.o
  0001fc42-0001fcaf  SendVersionInfo                   110   2  Init  RX  datado.o
  0001fcb0-0001fd1f  sdmmc_select_card                 112   2  Init  RX  hpm_sdmmc_common.c.o
  0001fd20-0001fd8f  sd_error_recovery                 112   2  Init  RX  hpm_sdmmc_sd.c.o
  0001fd90-0001fe03  ComputeXkk_1                      116   2  Init  RX  kalman.o
  0001fe04-0001fe75  gptmr_channel_get_default_config
                                                       114   2  Init  RX  hpm_gptmr_drv.c.o
  0001fe76-0001fee3  fill_last_frag                    110   2  Init  RX  ff.c.o
  0001fee4-0001ff53  sdxc_reset                        112   2  Init  RX  hpm_sdxc_drv.c.o
  0001ff54-0001ffbf  sd_send_if_cond                   108   2  Init  RX  hpm_sdmmc_sd.c.o
  0001ffc0-0002002b  UpdateStop_SetEnd                 108   2  Init  RX  SetParaBao.o
  0002002c-00020097  UpdateStart_SetEnd                108   2  Init  RX  SetParaBao.o
  00020098-00020103  UpdateSend_SetEnd                 108   2  Init  RX  SetParaBao.o
  00020104-0002016f  UpdateEnd_SetEnd                  108   2  Init  RX  SetParaBao.o
  00020170-000201db  SendPara_SetEnd                   108   2  Init  RX  SetParaBao.o
  000201dc-00020247  ReadPara4_SetEnd                  108   2  Init  RX  SetParaBao.o
  00020248-000202b3  ReadPara3_SetEnd                  108   2  Init  RX  SetParaBao.o
  000202b4-0002031f  ReadPara2_SetEnd                  108   2  Init  RX  SetParaBao.o
  00020320-0002038b  ReadPara1_SetEnd                  108   2  Init  RX  SetParaBao.o
  0002038c-000203f7  ReadPara0_SetEnd                  108   2  Init  RX  SetParaBao.o
  000203f8-00020465  xdir_sum                          110   2  Init  RX  ff.c.o
  00020466-000204cf  validate                          106   2  Init  RX  ff.c.o
  000204d0-00020533  sd_app_cmd_send_cond_op           100   2  Init  RX  hpm_sdmmc_sd.c.o
  00020534-0002058d  f_close                            90   2  Init  RX  ff.c.o
  0002058e-000205f5  disk_ioctl                        104   2  Init  RX  diskio.c.o
  000205f6-0002065d  ComputeVib0                       104   2  Init  RX  align.o
  0002065e-000206bd  sd_app_cmd_set_write_block_erase_count
                                                        96   2  Init  RX  hpm_sdmmc_sd.c.o
  000206be-00020723  SDUartIrqInit                     102   2  Init  RX  uart.o
  00020724-00020781  move_window                        94   2  Init  RX  ff.c.o
  00020782-000207e5  fill_first_frag                   100   2  Init  RX  ff.c.o
  000207e6-0002084b  st_dword                          102   2  Init  RX  ff.c.o
  0002084c-000208ad  sd_send_card_status                98   2  Init  RX  hpm_sdmmc_sd.c.o
  000208ae-00020913  myget_16bit_D64                   102   2  Init  RX  readpaoche.o
  00020914-00020975  disk_write                         98   2  Init  RX  diskio.c.o
  00020976-000209d5  disk_sync_read                     96   2  Init  RX  diskio.c.o
  000209d6-00020a37  disk_read                          98   2  Init  RX  diskio.c.o
  00020a38-00020a99  disk_async_write                   98   2  Init  RX  diskio.c.o
  00020a9a-00020af7  Read_And_Check_GNSS_Data           94   2  Init  RX  read_and_check_gnss_data.o
  00020af8-00020b5d  BindDefaultSet_by_GNSS            102   2  Init  RX  navi.o
  00020b5e-00020bb9  ComputePkk_1_Step1                 92   2  Init  RX  kalman.o
  00020bba-00020c13  sd_disk_status                     90   2  Init  RX  hpm_sdmmc_disk.c.o
  00020c14-00020c75  sd_be2le                           98   2  Init  RX  hpm_sdmmc_sd.c.o
  00020c76-00020ccf  init_alloc_info                    90   2  Init  RX  ff.c.o
  00020cd0-00020d2f  sdxc_select_voltage                96   2  Init  RX  hpm_sdxc_drv.c.o
  00020d30-00020d8b  sdmmc_send_application_command
                                                        92   2  Init  RX  hpm_sdmmc_common.c.o
  00020d8c-00020de5  sd_send_rca                        90   2  Init  RX  hpm_sdmmc_sd.c.o
  00020de6-00020e45  core_local_mem_to_sys_address
                                                        96   2  Init  RX  uart_dma.o
  00020e46-00020ea5  core_local_mem_to_sys_address
                                                        96   2  Init  RX  hpm_sdmmc_disk.c.o
  00020ea6-00020f05  core_local_mem_to_sys_address
                                                        96   2  Init  RX  hpm_sdmmc_port.c.o
  00020f06-00020f5d  sdxc_set_data_bus_width            88   2  Init  RX  hpm_sdxc_drv.c.o
  00020f5e-00020fad  sdmmchost_enable_sdio_interrupt
                                                        80   2  Init  RX  hpm_sdmmc_host.c.o
  00020fae-00021009  fpgadata_Predo_chen_algParmCash
                                                        92   2  Init  RX  InsTestingEntry.o
  0002100a-0002105b  sd_disk_sync_read                  82   2  Init  RX  hpm_sdmmc_disk.c.o
  0002105c-000210b1  sd_check_card_parameters           86   2  Init  RX  hpm_sdmmc_sd.c.o
  000210b2-0002110b  ComputeWenn                        90   2  Init  RX  navi.o
  0002110c-0002115f  ff_queue_init                      84   2  Init  RX  ff_queue.o
  00021160-000211b7  ComputeWien                        88   2  Init  RX  navi.o
  000211b8-0002120d  uart_default_config                86   2  Init  RX  hpm_uart_drv.c.o
  0002120e-0002125f  sd_host_init                       82   2  Init  RX  hpm_sdmmc_sd.c.o
  00021260-000212b5  UpdateAlignPosAndVn                86   2  Init  RX  dynamic_align.o
  000212b6-00021301  set_pwm_waveform_edge_aligned_duty
                                                        76   2  Init  RX  timer.o
  00021302-00021355  sdxc_set_speed_mode                84   2  Init  RX  hpm_sdxc_drv.c.o
  00021356-000213a5  sdmmc_set_block_size               80   2  Init  RX  hpm_sdmmc_common.c.o
  000213a6-000213f9  gpiom_set_pin_controller           84   2  Init  RX  bsp_gpio.o
  000213fa-0002144d  gpiom_set_pin_controller           84   2  Init  RX  main.o
  0002144e-00021491  ReadFileOpenFromSd                 68   2  Init  RX  sd_fatfs.o
  00021492-000214d1  AlgorithmDo                        64   2  Init  RX  InsTestingEntry.o
  000214d2-0002151f  sdmmc_go_idle_state                78   2  Init  RX  hpm_sdmmc_common.c.o
  00021520-00021569  sd_send_cmd                        74   2  Init  RX  hpm_sdmmc_sd.c.o
  0002156a-000215bb  gpio_write_pin                     82   2  Init  RX  INS_Init.o
  000215bc-0002160d  Mat_Tr                             82   2  Init  RX  matvecmath.o
  0002160e-0002165d  sdxc_enable_inverse_clock          80   2  Init  RX  hpm_sdxc_drv.c.o
  0002165e-000216ad  sdxc_enable_inverse_clock          80   2  Init  RX  hpm_sdmmc_host.c.o
  000216ae-000216f1  sdmmchost_wait_xfer_done           68   2  Init  RX  hpm_sdmmc_host.c.o
  000216f2-00021739  sdmmchost_wait_idle                72   2  Init  RX  hpm_sdmmc_host.c.o
  0002173a-0002177d  sdmmchost_wait_command_done
                                                        68   2  Init  RX  hpm_sdmmc_host.c.o
  0002177e-000217c1  sdmmchost_get_data_pin_level
                                                        68   2  Init  RX  hpm_sdmmc_host.c.o
  000217c2-0002180f  uart_send_byte                     78   2  Init  RX  hpm_uart_drv.c.o
  00021810-00021851  st_clust                           66   2  Init  RX  ff.c.o
  00021852-00021897  norflash_init                      70   2  Init  RX  flash.o
  00021898-000218e5  GNSS_Lost_Time                     78   2  Init  RX  InsTestingEntry.o
  000218e6-00021931  sum_sfn                            76   2  Init  RX  ff.c.o
  00021932-00021979  sdmmchost_select_voltage           72   2  Init  RX  hpm_sdmmc_host.c.o
  0002197a-000219bf  sd_switch_voltage                  70   2  Init  RX  hpm_sdmmc_sd.c.o
  000219c0-00021a09  xor_check                          74   2  Init  RX  frame_analysis.o
  00021a0a-00021a4b  uart_tx_dma                        66   2  Init  RX  uart_dma.o
  00021a4c-00021a91  putc_flush                         70   2  Init  RX  ff.c.o
  00021a92-00021adb  gptmr_channel_reset_count          74   2  Init  RX  timer.o
  00021adc-00021b03  fpgadata_Predo                     40   2  Init  RX  InsTestingEntry.o
  00021b04-00021b3f  ComputeLeverArmVn                  60   2  Init  RX  navi.o
  00021b40-00021b7f  ld_clust                           64   2  Init  RX  ff.c.o
  00021b80-00021bab  fpgadata_Predo_chen                44   2  Init  RX  InsTestingEntry.o
  00021bac-00021bed  disk_status                        66   2  Init  RX  diskio.c.o
  00021bee-00021c2f  disk_initialize                    66   2  Init  RX  diskio.c.o
  00021c30-00021c73  sdxc_set_cardclk_delay_chain
                                                        68   2  Init  RX  hpm_sdmmc_host.c.o
  00021c74-00021cb7  sdxc_enable_sd_clock               68   2  Init  RX  hpm_sdxc_drv.c.o
  00021cb8-00021cfb  sdxc_enable_sd_clock               68   2  Init  RX  hpm_sdmmc_host.c.o
  00021cfc-00021d3b  sd_select_card                     64   2  Init  RX  hpm_sdmmc_sd.c.o
  00021d3c-00021d7b  sd_disk_async_write                64   2  Init  RX  hpm_sdmmc_disk.c.o
  00021d7c-00021dbf  ld_dword                           68   2  Init  RX  ff.c.o
  00021dc0-00021e03  dbc_1st                            68   2  Init  RX  ff.c.o
  00021e04-00021e47  comm_param_setbits                 68   2  Init  RX  computerFrameParse.o
  00021e48-00021e8b  TransHeading0to360                 68   2  Init  RX  navi.o
  00021e8c-00021ecf  GNSS_Last_TIME                     68   2  Init  RX  InsTestingEntry.o
  00021ed0-00021f11  st_word                            66   2  Init  RX  ff.c.o
  00021f12-00021f47  sdxc_tuning_error_recovery
                                                        54   2  Init  RX  hpm_sdxc_drv.c.o
  00021f48-00021f89  gptmr_update_cmp                   66   2  Init  RX  timer.o
  00021f8a-00021fc9  uart_flush                         64   2  Init  RX  hpm_uart_drv.c.o
  00021fca-00022009  ff_queue_is_full                   64   2  Init  RX  ff_queue.o
  0002200a-00022049  CorrectVn                          64   2  Init  RX  kalman.o
  0002204a-0002207f  sdmmchost_set_card_bus_width
                                                        54   2  Init  RX  hpm_sdmmc_host.c.o
  00022080-000220b9  sdmmchost_check_host_availability
                                                        58   2  Init  RX  hpm_sdmmc_host.c.o
  000220ba-000220f1  Drv_FlashWrite                     56   2  Init  RX  FirmwareUpdateFile.o
  000220f2-0002212d  uart_modem_config                  60   2  Init  RX  hpm_uart_drv.c.o
  0002212e-00022169  sdxc_wait_card_active              60   2  Init  RX  hpm_sdmmc_host.c.o
  0002216a-000221a5  sdmmc_get_card_and_aligned_buf_info
                                                        60   2  Init  RX  hpm_sdmmc_disk.c.o
  000221a6-000221dd  sd_disk_write                      56   2  Init  RX  hpm_sdmmc_disk.c.o
  000221de-00022215  sd_disk_read                       56   2  Init  RX  hpm_sdmmc_disk.c.o
  00022216-00022251  rom_xpi_nor_read                   60   2  Init  RX  flash.o
  00022252-0002228d  dmamux_config                      60   2  Init  RX  uart_dma.o
  0002228e-000222c9  dma_default_channel_config
                                                        60   2  Init  RX  hpm_dma_drv.c.o
  000222ca-000222f5  DeviceInit                         44   2  Init  RX  datado.o
  000222f6-0002232f  sdxc_stop_clock_during_phase_code_change
                                                        58   2  Init  RX  hpm_sdmmc_common.c.o
  00022330-00022361  norflash_write                     50   2  Init  RX  flash.o
  00022362-00022391  norflash_read                      48   2  Init  RX  flash.o
  00022392-000223cb  ld_word                            58   2  Init  RX  ff.c.o
  000223cc-000223ff  uart_dma_output                    52   2  Init  RX  uart_dma.o
  00022400-0002242f  sdxc_send_command                  48   2  Init  RX  hpm_sdxc_drv.c.o
  00022430-00022467  sdxc_enable_auto_tuning            56   2  Init  RX  hpm_sdxc_drv.c.o
  00022468-0002249b  sdmmchost_set_speed_mode           52   2  Init  RX  hpm_sdmmc_host.c.o
  0002249c-000224d3  myget_16bit_D32                    56   2  Init  RX  readpaoche.o
  000224d4-0002250b  crc_verify_8bit                    56   2  Init  RX  app_tool.o
  0002250c-00022543  app_accum_verify_8bit              56   2  Init  RX  app_tool.o
  00022544-00022573  ComputeLeverArmSn                  48   2  Init  RX  navi.o
  00022574-000225a9  sdxc_set_post_change_delay
                                                        54   2  Init  RX  hpm_sdmmc_common.c.o
  000225aa-000225df  sdxc_is_inverse_clock_enabled
                                                        54   2  Init  RX  hpm_sdxc_drv.c.o
  000225e0-00022615  sdxc_is_inverse_clock_enabled
                                                        54   2  Init  RX  hpm_sdmmc_host.c.o
  00022616-0002264b  sdxc_enable_interrupt_status
                                                        54   2  Init  RX  hpm_sdxc_drv.c.o
  0002264c-00022681  sdxc_enable_interrupt_status
                                                        54   2  Init  RX  hpm_sdmmc_host.c.o
  00022682-000226b7  sdxc_enable_interrupt_signal
                                                        54   2  Init  RX  hpm_sdmmc_host.c.o
  000226b8-000226ed  myget_16bit_I32                    54   2  Init  RX  readpaoche.o
  000226ee-0002271b  f_chdrive                          46   2  Init  RX  ff.c.o
  0002271c-00022751  clst2sect                          54   2  Init  RX  ff.c.o
  00022752-00022781  Drv_FlashErase                     48   2  Init  RX  FirmwareUpdateFile.o
  00022782-000227b5  sdxc_get_data_bus_width            52   2  Init  RX  hpm_sdxc_drv.c.o
  000227b6-000227e9  sdmmchost_set_card_clock           52   2  Init  RX  hpm_sdmmc_host.c.o
  000227ea-00022813  norflash_erase_sector              42   2  Init  RX  flash.o
  00022814-0002283f  Uart_SendMsg                       44   2  Init  RX  bsp_fmc.o
  00022840-0002286f  sdxc_enable_tm_clock               48   2  Init  RX  hpm_sdxc_drv.c.o
  00022870-0002289f  sdxc_enable_power                  48   2  Init  RX  hpm_sdmmc_common.c.o
  000228a0-000228cf  sdxc_enable_power                  48   2  Init  RX  hpm_sdmmc_host.c.o
  000228d0-000228f5  Pwm_Init                           38   2  Init  RX  timer.o
  000228f6-00022921  gptmr_stop_counter                 44   2  Init  RX  timer.o
  00022922-0002294d  gptmr_start_counter                44   2  Init  RX  timer.o
  0002294e-00022979  gpio_set_pin_output                44   2  Init  RX  bsp_gpio.o
  0002297a-000229a5  gpio_set_pin_input                 44   2  Init  RX  main.o
  000229a6-000229d1  gpio_enable_pin_interrupt          44   2  Init  RX  main.o
  000229d2-000229fd  gpio_clear_pin_interrupt_flag
                                                        44   2  Init  RX  main.o
  000229fe-00022a17  get_fpgadata                       26   2  Init  RX  fpgad.o
  00022a18-00022a37  SdFileWriteOperate                 32   2  Init  RX  sd_fatfs.o
  00022a38-00022a59  Exti_Init                          34   2  Init  RX  main.o
  00022a5a-00022a7f  sd_set_max_current                 38   2  Init  RX  hpm_sdmmc_sd.c.o
  00022a80-00022aa9  gpio_toggle_pin                    42   2  Init  RX  main.o
  00022aaa-00022ad3  ff_queue_is_empty                  42   2  Init  RX  ff_queue.o
  00022ad4-00022af5  uart_get_current_recv_remaining_size
                                                        34   2  Init  RX  uart_dma.o
  00022af6-00022b13  sdmmchost_wait_card_active
                                                        30   2  Init  RX  hpm_sdmmc_host.c.o
  00022b14-00022b3b  hpm_sdmmc_osal_event_create
                                                        40   2  Init  RX  hpm_sdmmc_osal.o
  00022b3c-00022b61  putc_init                          38   2  Init  RX  ff.c.o
  00022b62-00022b87  myget_8bit_I16                     38   2  Init  RX  readpaoche.o
  00022b88-00022bad  hpm_sdmmc_osal_delay               38   2  Init  RX  hpm_sdmmc_osal.o
  00022bae-00022bcb  ff_handle_poll                     30   2  Init  RX  ff_queue.o
  00022bcc-00022bef  xsum32                             36   2  Init  RX  ff.c.o
  00022bf0-00022c13  sdxc_get_default_cardclk_delay_chain
                                                        36   2  Init  RX  hpm_sdmmc_host.c.o
  00022c14-00022c33  sdmmc_get_sys_addr                 32   2  Init  RX  hpm_sdmmc_port.c.o
  00022c34-00022c57  gptmr_check_status                 36   2  Init  RX  timer.o
  00022c58-00022c75  sdmmchost_switch_to_3v3_as_needed
                                                        30   2  Init  RX  hpm_sdmmc_host.c.o
  00022c76-00022c97  sdmmchost_register_xfer_complete_callback
                                                        34   2  Init  RX  hpm_sdmmc_host.c.o
  00022c98-00022cb5  sdmmchost_error_recovery           30   2  Init  RX  hpm_sdmmc_host.c.o
  00022cb6-00022ccd  output_normal_do                   24   2  Init  RX  INS_Output.o
  00022cce-00022cef  init_gpio                          34   2  Init  RX  main.o
  00022cf0-00022d05  EXTI3_IRQHandler                   22   2  Init  RX  gd32f4xx_it.o
  00022d06-00022d21  timer_Init                         28   2  Init  RX  timer.o
  00022d22-00022d3d  Drv_FlashRead                      28   2  Init  RX  FirmwareUpdateFile.o
  00022d3e-00022d5b  sdxc_is_bus_idle                   30   2  Init  RX  hpm_sdxc_drv.c.o
  00022d5c-00022d75  sd_is_card_present                 26   2  Init  RX  hpm_sdmmc_sd.c.o
  00022d76-00022d8b  loopDoOther                        22   2  Init  RX  datado.o
  00022d8c-00022da9  hpm_sdmmc_osal_event_clear
                                                        30   2  Init  RX  hpm_sdmmc_osal.o
  00022daa-00022dc1  uart4sendmsg                       24   2  Init  RX  gd32f4xx_it.o
  00022dc2-00022ddd  sdmmchost_is_voltage_switch_supported
                                                        28   2  Init  RX  hpm_sdmmc_host.c.o
  00022dde-00022df3  sdmmchost_delay_ms                 22   2  Init  RX  hpm_sdmmc_host.c.o
  00022df4-00022e0f  gptmr_enable_irq                   28   2  Init  RX  timer.o
  00022e10-00022e23  gd_eval_com_init                   20   2  Init  RX  INS_Init.o
  00022e24-00022e33  bsp_tim_init                       16   2  Init  RX  bsp_tim.o
  00022e34-00022e49  Led_Control                        22   2  Init  RX  main.o
  00022e4a-00022e63  sdxc_reset_tuning_engine           26   2  Init  RX  hpm_sdxc_drv.c.o
  00022e64-00022e7d  sdxc_is_card_inserted              26   2  Init  RX  hpm_sdmmc_host.c.o
  00022e7e-00022e97  hpm_sdmmc_osal_event_set           26   2  Init  RX  hpm_sdmmc_osal.o
  00022e98-00022eab  Drv_SystemReset                    20   2  Init  RX  FirmwareUpdateFile.o
  00022eac-00022ec3  sdxc_execute_tuning                24   2  Init  RX  hpm_sdxc_drv.c.o
  00022ec4-00022edb  sdmmchost_is_8bit_supported
                                                        24   2  Init  RX  hpm_sdmmc_host.c.o
  00022edc-00022eef  protocol_send                      20   2  Init  RX  computerFrameParse.o
  00022ef0-00022f07  dma_get_remaining_transfer_size
                                                        24   2  Init  RX  uart_dma.o
  00022f08-00022f1f  delay_ms                           24   2  Init  RX  systick.o
  00022f20-00022f33  Algorithm_before_otherDataDo
                                                        20   2  Init  RX  datado.o
  00022f34-00022f47  ACC_gyroreset_r_TAFEAG16_buf
                                                        20   2  Init  RX  InsTestingEntry.o
  00022f48-00022f5d  get_uart_tx_idle                   22   2  Init  RX  uart_dma.o
  00022f5e-00022f71  gptmr_clear_status                 20   2  Init  RX  timer.o
  00022f72-00022f83  sdxc_get_data7_4_level             18   2  Init  RX  hpm_sdmmc_host.c.o
  00022f84-00022f95  sdxc_get_data3_0_level             18   2  Init  RX  hpm_sdmmc_host.c.o
  00022f96-00022fa7  sdxc_clear_interrupt_status
                                                        18   2  Init  RX  hpm_sdxc_drv.c.o
  00022fa8-00022fb9  sdxc_clear_interrupt_status
                                                        18   2  Init  RX  hpm_sdmmc_host.c.o
  00022fba-00022fcb  ppor_sw_reset                      18   2  Init  RX  FirmwareUpdateFile.o
  00022fcc-00022fdd  mchtmr_get_count                   18   2  Init  RX  sd_fatfs.o
  00022fde-00022fed  sdxc_select_cardclk_delay_source
                                                        16   2  Init  RX  hpm_sdmmc_common.c.o
  00022fee-00022ffd  comm_read_currentFreq              16   2  Init  RX  computerFrameParse.o
  00022ffe-0002300d  comm_axis_read                     16   2  Init  RX  computerFrameParse.o
  0002300e-0002301b  sdxc_get_present_status            14   2  Init  RX  hpm_sdxc_drv.c.o
  0002301c-00023029  sdxc_get_interrupt_status          14   2  Init  RX  hpm_sdxc_drv.c.o
  0002302a-00023037  sdxc_get_interrupt_status          14   2  Init  RX  hpm_sdmmc_host.c.o
  00023038-00023045  sdxc_get_interrupt_signal          14   2  Init  RX  hpm_sdmmc_host.c.o
  00023046-00023051  sdxc_is_ddr50_supported            12   2  Init  RX  hpm_sdmmc_port.c.o
  00023052-0002305b  sd_deinit                          10   2  Init  RX  hpm_sdmmc_sd.c.o
  0002305c-0002305f  get_fpgadata_before                 4   2  Init  RX  fpgad.o
  00023060-00023063  comm_nav_para_syn                   4   2  Init  RX  computerFrameParse.o
  00023064-0007ffff  ( UNUSED .=.+380828 )         380 828   -  ----  -   -
  00080000-000800d7  s_sd                              216   8  Init  RW  hpm_sdmmc_disk.c.o
  000800d8-0008011b  test_uart                          68   4  Init  RW  uart.o
  0008011c-0008015f  sd_uart                            68   4  Init  RW  uart.o
  00080160-00080173  __SEGGER_RTL_global_locale
                                                        20   4  Init  RW  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  00080174-00080177  xgpsTrueTime                        4   4  Zero  ZI  frame_analysis.o
  00080178-0008017f  start_ticks                         8   8  Zero  ZI  sd_fatfs.o
  00080180-0008417f  s_sd_aligned_buf               16 384  64  Zero  ZI  hpm_sdmmc_disk.c.o
  00084180-00087c97  g_Kalman                       15 128   8  Zero  ZI  main.o
  00087c98-0008a46f  g_Compen                       10 200   8  Zero  ZI  main.o
  0008a470-0008af47  g_Navi                          2 776   8  Zero  ZI  main.o
  0008af48-0008b32f  g_DynamicInertialSysAlign       1 000   8  Zero  ZI  main.o
  0008b330-0008b667  g_InertialSysAlign                824   8  Zero  ZI  main.o
  0008b668-0008b8b7  s_fileCOM3                        592   8  Zero  ZI  sd_fatfs.o
  0008b8b8-0008bb07  s_fileBB00                        592   8  Zero  ZI  sd_fatfs.o
  0008bb08-0008bd2f  paochedata                        552   8  Zero  ZI  InsTestingEntry.o
  0008bd30-0008bec7  g_SysVar                          408   8  Zero  ZI  main.o
  0008bec8-0008bf7f  g_Align                           184   8  Zero  ZI  main.o
  0008bf80-0008bfff  g_SelfTest                        128   8  Zero  ZI  main.o
  0008c000-0008c06f  gnavout                           112   8  Zero  ZI  INS_Data.o
  0008c070-0008c0df  g_GNSSData_In_Use                 112   8  Zero  ZI  main.o
  0008c0e0-0008c127  Gw                                 72   8  Zero  ZI  InsTestingEntry.o
  0008c128-0008c16f  Aw                                 72   8  Zero  ZI  InsTestingEntry.o
  0008c170-0008c1af  g_InitBind                         64   8  Zero  ZI  main.o
  0008c1b0-0008c1c7  r_LastGyro                         24   8  Zero  ZI  InsTestingEntry.o
  0008c1c8-0008c1df  r_Gyro                             24   8  Zero  ZI  InsTestingEntry.o
  0008c1e0-0008c1f7  gw                                 24   8  Zero  ZI  InsTestingEntry.o
  0008c1f8-0008c20f  gWm                                24   8  Zero  ZI  InsTestingEntry.o
  0008c210-0008c227  gW_real                            24   8  Zero  ZI  InsTestingEntry.o
  0008c228-0008c23f  aw                                 24   8  Zero  ZI  InsTestingEntry.o
  0008c240-0008c257  aWm                                24   8  Zero  ZI  InsTestingEntry.o
  0008c258-0008c26f  aW_real                            24   8  Zero  ZI  InsTestingEntry.o
  0008c270-0008c287  LastAcc                            24   8  Zero  ZI  InsTestingEntry.o
  0008c288-0008c29f  Acc                                24   8  Zero  ZI  InsTestingEntry.o
  0008c2a0-0008c2a7  end_ticks                           8   8  Zero  ZI  sd_fatfs.o
  0008c2a8-0009ab7f  gdriverdatalist                59 608   4  Zero  ZI  gdwatch.o
  0009ab80-0009bb81  grxbuffer                       4 098   4  Zero  ZI  uart.o
  0009bb82-0009bb83  tCnt.0                              2   2  Zero  ZI  InsTestingEntry.o
  0009bb84-0009c383  hello_str                       2 048   4  Zero  ZI  sd_fatfs.o
  0009c384-0009cb83  fpgatesttxt                     2 048   4  Zero  ZI  INS_Output.o
  0009cb84-0009cf83  gframeParsebuf                  1 024   4  Zero  ZI  uart.o
  0009cf84-0009d1e3  DirBuf                            608   4  Zero  ZI  ff.c.o
  0009d1e4-0009d437  hSetting                          596   4  Zero  ZI  computerFrameParse.o
  0009d438-0009d683  s_sd_disk                         588   4  Zero  ZI  sd_fatfs.o
  0009d684-0009d8cd  stSetPara                         586   4  Zero  ZI  SetParaBao.o
  0009d8ce-0009d8cf  tCnt.0                              2   2  Zero  ZI  INS_Output.o
  0009d8d0-0009db11  r_TAFEAG8_buf                     578   4  Zero  ZI  InsTestingEntry.o
  0009db12-0009db13  tCnt.0                              2   2  Zero  ZI  sd_fatfs.o
  0009db14-0009dd13  work                              512   4  Zero  ZI  sd_fatfs.o
  0009dd14-0009df13  LfnBuf                            512   4  Zero  ZI  ff.c.o
  0009df14-0009e0a3  gfpgadata                         400   4  Zero  ZI  fpgad.o
  0009e0a4-0009e1f3  gfpgadataPredoSend                336   4  Zero  ZI  InsTestingEntry.o
  0009e1f4-0009e343  BB00SdData                        336   4  Zero  ZI  InsTestingEntry.o
  0009e344-0009e453  gpagedata                         272   4  Zero  ZI  fpgad.o
  0009e454-0009e561  gins912data                       270   4  Zero  ZI  InsTestingEntry.o
  0009e562-0009e563  SDCnt.0                             2   2  Zero  ZI  gd32f4xx_it.o
  0009e564-0009e671  ginputdata                        270   4  Zero  ZI  InsTestingEntry.o
  0009e672-0009e673  Fsid                                2   2  Zero  ZI  ff.c.o
  0009e674-0009e773  s_xpi_nor_config                  256   4  Zero  ZI  flash.o
  0009e774-0009e863  hINSData                          240   4  Zero  ZI  INS_Data.o
  0009e864-0009e8f7  ggpsorgdata                       148   4  Zero  ZI  frame_analysis.o
  0009e8f8-0009e981  gdriversettings                   138   4  Zero  ZI  gdwatch.o
  0009e982-0009e983  COM3Flag.3                          2   2  Zero  ZI  sd_fatfs.o
  0009e984-0009e9e7  rs422_frame                       100   4  Zero  ZI  frame_analysis.o
  0009e9e8-0009ea13  galgrithomresultTx                 44   4  Zero  ZI  gdwatch.o
  0009ea14-0009ea3b  FatFs                              40   4  Zero  ZI  ff.c.o
  0009ea3c-0009ea61  gcanInfo                           38   4  Zero  ZI  fpgad.o
  0009ea62-0009ea63  BB00Flag.2                          2   2  Zero  ZI  sd_fatfs.o
  0009ea64-0009ea83  infoArr                            32   4  Zero  ZI  fpgad.o
  0009ea84-0009ea9b  __SEGGER_RTL_aSigTab               24   4  Zero  ZI  execops.o (libc_rv32gc_d_balanced.a)
  0009ea9c-0009eab3  StrMiddleWare                      24   4  Zero  ZI  InsTestingEntry.o
  0009eab4-0009eac7  canin                              20   4  Zero  ZI  InsTestingEntry.o
  0009eac8-0009ead3  r_fog                              12   4  Zero  ZI  InsTestingEntry.o
  0009ead4-0009eadf  r_acc                              12   4  Zero  ZI  InsTestingEntry.o
  0009eae0-0009eae3  xgpsTime                            4   4  Zero  ZI  frame_analysis.o
  0009eae4-0009eae7  uiOffsetAddr.0                      4   4  Zero  ZI  SetParaBao.o
  0009eae8-0009eaeb  uiLen.0                             4   4  Zero  ZI  uart.o
  0009eaec-0009eaef  time_base_periodic_cnt              4   4  Zero  ZI  bsp_tim.o
  0009eaf0-0009eaf3  time_base_20ms_periodic_cnt
                                                         4   4  Zero  ZI  bsp_tim.o
  0009eaf4-0009eaf7  time_base_20ms_Flag                 4   4  Zero  ZI  bsp_tim.o
  0009eaf8-0009eafb  time_base_100ms_periodic_cnt
                                                         4   4  Zero  ZI  bsp_tim.o
  0009eafc-0009eaff  time_base_100ms_Flag                4   4  Zero  ZI  bsp_tim.o
  0009eb00-0009eb03  test_count                          4   4  Zero  ZI  sd_fatfs.o
  0009eb04-0009eb07  read_ms                             4   4  Zero  ZI  sd_fatfs.o
  0009eb08-0009eb0b  read_count                          4   4  Zero  ZI  sd_fatfs.o
  0009eb0c-0009eb0f  hpm_core_clock                      4   4  Zero  ZI  hpm_clock_drv.c.o
  0009eb10-0009eb13  grxst                               4   4  Zero  ZI  uart.o
  0009eb14-0009eb17  grxlen                              4   4  Zero  ZI  uart.o
  0009eb18-0009eb1b  gins912outputmode                   4   4  Zero  ZI  INS_Output.o
  0009eb1c-0009eb1f  ggdworgdata_packet                  4   4  Zero  ZI  gdwatch.o
  0009eb20-0009eb23  gframeindex                         4   4  Zero  ZI  fpgad.o
  0009eb24-0009eb27  gfpgasenddatalen                    4   4  Zero  ZI  fpgad.o
  0009eb28-0009eb2b  gdriverspacket                      4   4  Zero  ZI  gdwatch.o
  0009eb2c-0009eb2f  g_console_uart                      4   4  Zero  ZI  hpm_debug_console.c.o
  0009eb30-0009eb33  g_DataOutTime                       4   4  Zero  ZI  SetParaBao.o
  0009eb34-0009eb37  g_CAN_Timeout_Start_flag            4   4  Zero  ZI  INS_Init.o
  0009eb38-0009eb3b  g_CAN_Timeout_Cnt                   4   4  Zero  ZI  INS_Init.o
  0009eb3c-0009eb3f  fpga_syn_count                      4   4  Zero  ZI  INS_Init.o
  0009eb40-0009eb43  fpga_syn_btw_last                   4   4  Zero  ZI  fpgad.o
  0009eb44-0009eb47  fpga_syn_btw                        4   4  Zero  ZI  fpgad.o
  0009eb48-0009eb4b  fpga_syn_NAVI_btw                   4   4  Zero  ZI  fpgad.o
  0009eb4c-0009eb4f  fpga_loop_count                     4   4  Zero  ZI  INS_Init.o
  0009eb50-0009eb53  flag.1                              4   4  Zero  ZI  SetParaBao.o
  0009eb54-0009eb57  current_reload                      4   4  Zero  ZI  timer.o
  0009eb58-0009eb5b  axisInfo                            4   4  Zero  ZI  frame_analysis.o
  0009eb5c-0009eb5f  __SEGGER_RTL_stdout_file            4   4  Zero  ZI  hpm_debug_console.c.o
  0009eb60-0009eb63  NaviCompute_do_count                4   4  Zero  ZI  datado.o
  0009eb64-0009eb67  en                                  4   4  Zero  ZI  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  0009eb68-0009eb6b  __SEGGER_RTL_locale_ptr             4   4  Zero  ZI  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0009eb6c-0009eb6f  __SEGGER_RTL_heap_globals           4   4  Zero  ZI  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  0009eb70-0009eb70  inscanruning                        1   1  Zero  ZI  readpaoche.o
  0009eb71-0009eb71  init_done                           1   1  Zero  ZI  ff_queue.o
  0009eb72-0009eb72  has_card_initialized.0              1   1  Zero  ZI  hpm_sdmmc_disk.c.o
  0009eb73-0009eb73  g_ucSystemResetFlag                 1   1  Zero  ZI  SetParaBao.o
  0009eb74-0009eb74  g_UpdateSuccessful                  1   1  Zero  ZI  SetParaBao.o
  0009eb75-0009eb75  g_StartUpdateFirm                   1   1  Zero  ZI  main.o
  0009eb76-0009eb76  g_LEDIndicatorState                 1   1  Zero  ZI  INS_Data.o
  0009eb77-0009eb77  g_DataOutTypeFlag                   1   1  Zero  ZI  SetParaBao.o
  0009eb78-0009eb78  g_Com3WriteSdFlag                   1   1  Zero  ZI  uart.o
  0009eb79-0009eb79  g_BB00WriteSdFlag                   1   1  Zero  ZI  InsTestingEntry.o
  0009eb7a-0009eb7a  fpga_syn                            1   1  Zero  ZI  INS_Init.o
  0009eb7b-0009eb7b  fatfs_result                        1   1  Zero  ZI  sd_fatfs.o
  0009eb7c-0009eb7c  SetSdOperateType                    1   1  Zero  ZI  SetParaBao.o
  0009eb7d-0009eb7d  SetSdFlieType                       1   1  Zero  ZI  SetParaBao.o
  0009eb7e-0009eb7e  CurrVol                             1   1  Zero  ZI  ff.c.o
  0009eb7f-0009eb7f  Count1                              1   1  Zero  ZI  InsTestingEntry.o
  0009eb80-0009eb80  CollieryOperate_uartsend_Flag
                                                         1   1  Zero  ZI  main.o
  0009eb81-0009eb81  CollieryOperate_Flag                1   1  Zero  ZI  SetParaBao.o
  0009eb82-0009eb82  rcv_state                           1   1  Init  RW  readpaoche.o
  0009eb83-0009eb83  g_UpdateBackFlag                    1   1  Init  RW  SetParaBao.o
  0009eb84-000a7b88  test_uart_rx_buf               36 869   4  None  ZI  uart.o
  000a7b89-000a7b89  Return_Colliery_Operate_Flag
                                                         1   1  Init  RW  main.o
  000a7b8a-000a7b8b  ( UNUSED .=.+2 )                    2   -  ----  -   -
  000a7b8c-000a7b8c  dma_done                            1   4  None  RW  uart_dma.o
  000a7b8d-000a7b8f  ( ALIGN .=.+3 )                     3   -  ----  -   -
  000a7b90-000a7b93  uiLastBaoInDex.2                    4   4  Init  RW  SetParaBao.o
  000a7b94-000a7b97  stdout                              4   4  Init  RW  hpm_debug_console.c.o
  000a7b98-000a7b9b  gprotocol_send_baudrate             4   4  Init  RW  datado.o
  000a7b9c-000a7b9f  Com3FileName                        4   4  Init  RW  sd_fatfs.o
  000a7ba0-000a7ba3  BB00FileName                        4   4  Init  RW  sd_fatfs.o
  000a7ba4-000a7ba7  __SEGGER_RTL_errno                  4   4  Zero  ZI  errno.o (libc_rv32gc_d_balanced.a)
  000a7ba8-000afeab  g_queue                        33 540   4  Init  ZI  hpm_sdmmc_disk.c.o
  000afeac-000afeaf  ( UNUSED .=.+4 )                    4   -  ----  -   -
  000afeb0-000b3eaf  [.bss.block.heap]              16 384   8  None  ZI  [ Linker created ]
  000b3eb0-000bbfff  ( UNUSED .=.+33104 )           33 104   -  ----  -   -
  000bc000-000bffff  [.bss.block.stack]             16 384  16  None  ZI  [ Linker created ]
  01100000-01100313  s_sd_host                         788   4  Zero  ZI  hpm_sdmmc_disk.c.o
  01100314-01100317  ( UNUSED .=.+4 )                    4   -  ----  -   -
  01100318-01100417  test_tx_descriptors               256   8  Init  ZI  uart.o
  01100418-8000cfff  ( UNUSED .=.+2129710056 )  2 129 710 056
                                                             -  ----  -   -
  8000d000-8000d00f  option                             16   4  Cnst  RO  board.c.o
  8000d010-8000dfff  ( UNUSED .=.+4080 )             4 080   -  ----  -   -
  8000e000-8000e00f  header                             16   4  Cnst  RO  hpm_bootheader.c.o
  8000e010-8000e08f  fw_info                           128   4  Cnst  RO  hpm_bootheader.c.o
  8000e090-8000ffff  ( UNUSED .=.+8048 )             8 048   -  ----  -   -
  80010000-80010091  _start                            146   2  Code  RX  startup.s.o
  80010092-80010093  __SEGGER_RTL_SIGNAL_SIG_DFL
                                                         2   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  80010094-80010097  s_adc_clk_mux_node                  4   4  Cnst  RO  hpm_clock_drv.c.o
  80010098-800100f7  [.rodata]                          96   8  Cnst  RO  align.o
  800100f8-8001010f  [.rodata]                          24   8  Cnst  RO  dynamic_align.o
  80010110-80010287  [.rodata]                         376   8  Cnst  RO  kalman.o
  80010288-8001028f  [.rodata]                           8   8  Cnst  RO  matvecmath.o
  80010290-8001036f  [.rodata]                         224   8  Cnst  RO  navi.o
  80010370-800103af  [.rodata]                          64   8  Cnst  RO  read_and_check_gnss_data.o
  800103b0-80010407  [.rodata]                          88   8  Cnst  RO  readpaoche.o
  80010408-8001050b  [.rodata]                         260   8  Cnst  RO  frame_analysis.o
  8001050c-8001050f  s_i2s_clk_mux_node                  4   4  Cnst  RO  hpm_clock_drv.c.o
  80010510-8001059f  [.rodata]                         144   8  Cnst  RO  InsTestingEntry.o
  800105a0-800105c7  [.rodata]                          40   8  Cnst  RO  fpgad.o
  800105c8-800105f3  [.rodata]                          44   8  Cnst  RO  INS_Init.o
  800105f4-800105f5  __SEGGER_RTL_data_utf8_period
                                                         2   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  800105f6-800105f7  __SEGGER_RTL_SIGNAL_SIG_IGN
                                                         2   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  800105f8-8001074f  [.rodata]                         344   8  Cnst  RO  main.o
  80010750-80010767  [.rodata]                          24   8  Cnst  RO  hpm_pllctl_drv.c.o
  80010768-8001076f  __SEGGER_RTL_2pow64                 8   8  Cnst  RO  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  80010770-80010777  __SEGGER_RTL_2pow32                 8   8  Cnst  RO  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  80010778-8001077f  __SEGGER_RTL_2powNeg32              8   8  Cnst  RO  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  80010780-800107df  __SEGGER_RTL_float64_ATan          96   8  Cnst  RO  floatops.o (libc_rv32gc_d_balanced.a)
  800107e0-80010817  __SEGGER_RTL_float64_Tan           56   8  Cnst  RO  floatops.o (libc_rv32gc_d_balanced.a)
  80010818-8001083f  __SEGGER_RTL_float64_Log           40   8  Cnst  RO  floatops.o (libc_rv32gc_d_balanced.a)
  80010840-800108af  __SEGGER_RTL_float64_ASinACos
                                                       112   8  Cnst  RO  floatops.o (libc_rv32gc_d_balanced.a)
  800108b0-800108ef  __SEGGER_RTL_float64_SinCos
                                                        64   8  Cnst  RO  floatops.o (libc_rv32gc_d_balanced.a)
  800108f0-80010937  __SEGGER_RTL_aPower2               72   8  Cnst  RO  utilops.o (libc_rv32gc_d_balanced.a)
  80010938-800109d7  __SEGGER_RTL_ipow10               160   8  Cnst  RO  prinops.o (libc_rv32gc_d_balanced.a)
  800109d8-80010aaf  [.srodata.merged.cst8]            216   8  Cnst  RO  [ Linker created ]
  80010ab0-80010b13  axisTab                           100   4  Cnst  RO  frame_analysis.o
  80010b14-80010b73  SetDir                             96   4  Cnst  RO  InsTestingEntry.o
  80010b74-80010b8f  [.rodata]                          28   4  Cnst  RO  datado.o
  80010b90-80010bd3  [.rodata]                          68   4  Cnst  RO  gdwatch.o
  80010bd4-80010cd3  [.rodata]                         256   4  Cnst  RO  SetParaBao.o
  80010cd4-80011533  [.rodata]                       2 144   4  Cnst  RO  sd_fatfs.o
  80011534-80011573  [.rodata.write_pmp_addr]           64   4  Cnst  RO  hpm_pmp_drv.c.o
  80011574-800115b3  [.rodata.write_pma_addr]           64   4  Cnst  RO  hpm_pmp_drv.c.o
  800115b4-800115cb  [.rodata]                          24   4  Cnst  RO  hpm_uart_drv.c.o
  800115cc-800116af  [.rodata]                         228   4  Cnst  RO  ff.c.o
  800116b0-800116bf  defopt.2                           16   4  Cnst  RO  ff.c.o
  800116c0-80026b43  uni2oem936                     87 172   4  Cnst  RO  ffunicode.c.o
  80026b44-80026b67  [.rodata]                          36   4  Cnst  RO  ffunicode.c.o
  80026b68-80026c23  cvt2.0                            188   4  Cnst  RO  ffunicode.c.o
  80026c24-80026c63  [.rodata.exception_handler]
                                                        64   4  Cnst  RO  trap.c.o
  80026c64-80026c73  s_wdgs                             16   4  Cnst  RO  hpm_clock_drv.c.o
  80026c74-80026ca7  [.rodata.clock_get_frequency]
                                                        52   4  Cnst  RO  hpm_clock_drv.c.o
  80026ca8-80026cc7  [.rodata.get_frequency_for_source]
                                                        32   4  Cnst  RO  hpm_clock_drv.c.o
  80026cc8-80026cfb  [.rodata.clock_set_source_divider]
                                                        52   4  Cnst  RO  hpm_clock_drv.c.o
  80026cfc-800270fb  __SEGGER_RTL_Moeller_inverse_lut
                                                     1 024   4  Cnst  RO  intops.o (libc_rv32gc_d_balanced.a)
  800270fc-8002710b  __SEGGER_RTL_hex_lc                16   4  Cnst  RO  prinops.o (libc_rv32gc_d_balanced.a)
  8002710c-8002711b  __SEGGER_RTL_hex_uc                16   4  Cnst  RO  prinops.o (libc_rv32gc_d_balanced.a)
  8002711c-8002714b  [.rodata.libc.__SEGGER_RTL_vfprintf_float_long.str1.4]
                                                        48   4  Cnst  RO  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  8002714c-80027213  [.rodata.libc.__SEGGER_RTL_vfprintf_float_long]
                                                       200   4  Cnst  RO  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  80027214-8002721f  __SEGGER_RTL_c_locale              12   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80027220-80027277  __SEGGER_RTL_c_locale_data
                                                        88   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80027278-80027297  __SEGGER_RTL_codeset_ascii
                                                        32   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80027298-80027317  __SEGGER_RTL_ascii_ctype_map
                                                       128   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80027318-80027419  [.rodata]                         258   4  Cnst  RO  uart_dma.o
  8002741a-8002746b  l1c_op                             82   2  Code  RX  hpm_l1c_drv.o
  8002746c-800279bd  [.rodata]                       1 362   4  Cnst  RO  board.c.o
  800279be-800279f1  l1c_dc_enable                      52   2  Code  RX  hpm_l1c_drv.o
  800279f2-800279f3  __SEGGER_RTL_SIGNAL_SIG_ERR
                                                         2   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  800279f4-80027a01  cst32.1                            14   4  Cnst  RO  ff.c.o
  80027a02-80027a2f  l1c_ic_enable                      46   2  Code  RX  hpm_l1c_drv.o
  80027a30-80027a3d  cst.0                              14   4  Cnst  RO  ff.c.o
  80027a3e-80027a95  l1c_dc_invalidate                  88   2  Code  RX  hpm_l1c_drv.o
  80027a96-80027a97  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80027a98-80027c89  cvt1.1                            498   4  Cnst  RO  ffunicode.c.o
  80027c8a-80027ce3  l1c_dc_writeback                   90   2  Code  RX  hpm_l1c_drv.o
  80027ce4-80027d01  [.rodata.libc.__SEGGER_RTL_X_assert.str1.4]
                                                        30   4  Cnst  RO  execops.o (libc_rv32gc_d_balanced.a)
  80027d02-80027d5b  l1c_dc_flush                       90   2  Code  RX  hpm_l1c_drv.o
  80027d5c-80027d65  [.rodata.libc.__SEGGER_RTL_find_locale.str1.4]
                                                        10   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80027d66-80027d6f  l1c_dc_enable_writearound          10   2  Code  RX  hpm_l1c_drv.o
  80027d70-80027d95  pllctl_xtal_set_rampup_time
                                                        38   2  Code  RX  board.c.o
  80027d96-80027d97  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80027d98-80027dd1  __SEGGER_RTL_c_locale_day_names
                                                        58   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80027dd2-80027ea3  pcfg_dcdc_switch_to_dcm_mode
                                                       210   2  Code  RX  board.c.o
  80027ea4-80027f20  [.rodata]                         125   4  Cnst  RO  uart.o
  80027f21-80027f21  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80027f22-80027f83  board_init_console                 98   2  Code  RX  board.c.o
  80027f84-80027f90  LfnOfs                             13   4  Cnst  RO  ff.c.o
  80027f91-80027f91  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80027f92-8002818f  board_print_clock_freq            510   2  Code  RX  board.c.o
  80028190-8002828c  [.rodata]                         253   4  Cnst  RO  hpm_sdmmc_sd.c.o
  8002828d-8002828d  ( ALIGN .=.+1 )                     1   -  ----  -   -
  8002828e-800282a7  board_init_uart                    26   2  Code  RX  board.c.o
  800282a8-800282a8  __SEGGER_RTL_data_empty_string
                                                         1   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  800282a9-800282a9  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800282aa-80028307  board_print_banner                 94   2  Code  RX  board.c.o
  80028308-80028338  __SEGGER_RTL_c_locale_abbrev_month_names
                                                        49   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028339-80028339  ( ALIGN .=.+1 )                     1   -  ----  -   -
  8002833a-8002837b  board_turnoff_rgb_led              66   2  Code  RX  board.c.o
  8002837c-80028398  __SEGGER_RTL_c_locale_abbrev_day_names
                                                        29   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028399-80028399  ( ALIGN .=.+1 )                     1   -  ----  -   -
  8002839a-800283c3  board_init_femc_clock              42   2  Code  RX  board.c.o
  800283c4-800283d0  __SEGGER_RTL_ascii_ctype_mask
                                                        13   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  800283d1-800283d1  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800283d2-800286b7  board_init_pmp                    742   2  Code  RX  board.c.o
  800286b8-800286c0  __SEGGER_RTL_c_locale_time_format
                                                         9   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  800286c1-800286c1  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800286c2-80028b93  board_init_clock                1 234   2  Code  RX  board.c.o
  80028b94-80028b9c  __SEGGER_RTL_c_locale_date_format
                                                         9   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028b9d-80028b9d  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028b9e-80028c25  _init_ext_ram                     136   2  Code  RX  board.c.o
  80028c26-80028c27  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80028c28-80028c62  [.rodata]                          59   4  Cnst  RO  INS_Output.o
  80028c63-80028c63  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028c64-80028c82  [.rodata]                          31   4  Cnst  RO  flash.o
  80028c83-80028c83  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028c84-80028c86  driver_num_buf                      3   4  Cnst  RO  sd_fatfs.o
  80028c87-80028c87  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028c88-80028d46  [.rodata]                         191   4  Cnst  RO  hpm_l1c_drv.o
  80028d47-80028d47  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028d48-80028d56  __SEGGER_RTL_c_locale_date_time_format
                                                        15   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028d57-80028d57  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028d58-80028d5e  __SEGGER_RTL_c_locale_am_pm_indicator
                                                         7   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028d5f-80028d5f  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028d60-80028db6  __SEGGER_RTL_c_locale_month_names
                                                        87   4  Cnst  RO  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  80028db7-80028db7  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80028db8-80028eb9  board_sd_configure_clock          258   2  Code  RX  board.c.o
  80028eba-80028fdf  init_sram_pins                    294   2  Code  RX  pinmux.c.o
  80028fe0-80029089  init_gptmr_pins                   170   2  Code  RX  pinmux.c.o
  8002908a-800290c7  init_sdxc_cd_pin                   62   2  Code  RX  pinmux.c.o
  800290c8-80029169  init_sdxc_clk_data_pins           162   2  Code  RX  pinmux.c.o
  8002916a-8002917f  femc_enable                        22   2  Code  RX  hpm_femc_drv.c.o
  80029180-800291a1  femc_disable                       34   2  Code  RX  hpm_femc_drv.c.o
  800291a2-80029227  femc_default_config               134   2  Code  RX  hpm_femc_drv.c.o
  80029228-80029345  femc_init                         286   2  Code  RX  hpm_femc_drv.c.o
  80029346-80029397  femc_convert_actual_size_to_memory_size
                                                        82   2  Code  RX  hpm_femc_drv.c.o
  80029398-800293cd  ns2cycle                           54   2  Code  RX  hpm_femc_drv.c.o
  800293ce-80029447  femc_get_typical_sram_config
                                                       122   2  Code  RX  hpm_femc_drv.c.o
  80029448-800295b3  femc_config_sram                  364   2  Code  RX  hpm_femc_drv.c.o
  800295b4-8002969d  gpio_config_pin_interrupt         234   2  Code  RX  hpm_gpio_drv.c.o
  8002969e-8002974b  pllctl_pll_poweron                174   2  Code  RX  hpm_pllctl_drv.c.o
  8002974c-80029865  pllctl_get_pll_freq_in_hz         282   2  Code  RX  hpm_pllctl_drv.c.o
  80029866-800298d3  read_pmp_cfg                      110   2  Code  RX  hpm_pmp_drv.c.o
  800298d4-8002997d  write_pmp_addr                    170   2  Code  RX  hpm_pmp_drv.c.o
  8002997e-800299eb  read_pma_cfg                      110   2  Code  RX  hpm_pmp_drv.c.o
  800299ec-80029a95  write_pma_addr                    170   2  Code  RX  hpm_pmp_drv.c.o
  80029a96-80029b67  pmp_config                        210   2  Code  RX  hpm_pmp_drv.c.o
  80029b68-80029b79  syscall_handler                    18   2  Code  RX  trap.c.o
  80029b7a-80029bf3  hpm_csr_get_core_cycle            122   2  Code  RX  hpm_clock_drv.c.o
  80029bf4-80029c5d  pllctl_get_div                    106   2  Code  RX  hpm_clock_drv.c.o
  80029c5e-80029d25  clock_get_frequency               200   2  Code  RX  hpm_clock_drv.c.o
  80029d26-80029d93  get_frequency_for_ip_in_common_group
                                                       110   2  Code  RX  hpm_clock_drv.c.o
  80029d94-80029e6f  clock_set_source_divider          220   2  Code  RX  hpm_clock_drv.c.o
  80029e70-80029ea9  clock_add_to_group                 58   2  Code  RX  hpm_clock_drv.c.o
  80029eaa-80029f3b  clock_cpu_delay_ms                146   2  Code  RX  hpm_clock_drv.c.o
  80029f3c-80029f6f  clock_update_core_clock            52   2  Code  RX  hpm_clock_drv.c.o
  80029f70-80029f99  sysctl_resource_target_is_busy
                                                        42   2  Code  RX  hpm_sysctl_drv.c.o
  80029f9a-80029fc7  sysctl_clock_target_is_busy
                                                        46   2  Code  RX  hpm_sysctl_drv.c.o
  80029fc8-8002a069  sysctl_config_clock               162   2  Code  RX  hpm_sysctl_drv.c.o
  8002a06a-8002a0c3  system_init                        90   2  Code  RX  system.c.o
  8002a0c4-8002a12d  __SEGGER_RTL_xtoa                 106   2  Code  RX  convops.o (libc_rv32gc_d_balanced.a)
  8002a12e-8002a149  itoa                               28   2  Code  RX  convops.o (libc_rv32gc_d_balanced.a)
  8002a14a-8002a157  __SEGGER_RTL_X_errno_addr          14   2  Code  RX  errno.o (libc_rv32gc_d_balanced.a)
  8002a158-8002a19d  fwrite                             70   2  Code  RX  fileops.o (libc_rv32gc_d_balanced.a)
  8002a19e-8002a1c1  fputc                              36   2  Code  RX  fileops.o (libc_rv32gc_d_balanced.a)
  8002a1c2-8002a277  __floatundisf                     182   2  Code  RX  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002a278-8002a28d  __floatundidf                      22   2  Code  RX  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002a28e-8002a2c3  __SEGGER_RTL_float64_PolyEvalQ
                                                        54   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a2c4-8002a301  __SEGGER_RTL_float64_frexp_inline
                                                        62   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a302-8002a30b  __SEGGER_RTL_float64_isnan
                                                        10   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a30c-8002a315  __SEGGER_RTL_float64_isinf
                                                        10   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a316-8002a31f  __SEGGER_RTL_float64_isnormal
                                                        10   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a320-8002a3dd  floor                             190   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a3de-8002a4c7  tan                               234   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a4c8-8002a4cd  sqrt                                6   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a4ce-8002a5eb  __SEGGER_RTL_float64_asinacos_fpu
                                                       286   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a5ec-8002a5ef  asin                                4   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002a5f0-8002a615  __ashldi3                          38   2  Code  RX  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002a616-8002a63b  __lshrdi3                          38   2  Code  RX  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002a63c-8002aa5b  __udivdi3                       1 056   2  Code  RX  intops.o (libc_rv32gc_d_balanced.a)
  8002aa5c-8002ae97  __umoddi3                       1 084   2  Code  RX  intops.o (libc_rv32gc_d_balanced.a)
  8002ae98-8002aea1  abs                                10   2  Code  RX  intops.o (libc_rv32gc_d_balanced.a)
  8002aea2-8002af27  memcpy                            134   2  Code  RX  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002af28-8002af99  strchr                            114   2  Code  RX  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002af9a-8002afbb  __SEGGER_RTL_prin_flush            34   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002afbc-8002afd5  __SEGGER_RTL_pre_padding           26   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002afd6-8002aff7  __SEGGER_RTL_init_prin_l           34   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002aff8-8002b00f  __SEGGER_RTL_init_prin             24   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002b010-8002b035  vfprintf                           38   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002b036-8002b05d  printf                             40   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002b05e-8002bd2b  __SEGGER_RTL_vfprintf_float_long
                                                     3 278   2  Code  RX  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  8002bd2c-8002bd3f  __SEGGER_init_heap                 20   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  8002bd40-8002bd51  __SEGGER_RTL_init_heap             18   2  Code  RX  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  8002bd52-8002bd6f  malloc                             30   2  Code  RX  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  8002bd70-8002bd7d  __SEGGER_RTL_ascii_toupper
                                                        14   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002bd7e-8002bd8b  __SEGGER_RTL_ascii_towupper
                                                        14   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002bd8c-8002bdb5  __SEGGER_RTL_ascii_mbtowc          42   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002bdb6-8002bdbb  isdigit                             6   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002bdbc-8002bdc1  isspace                             6   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002bdc2-8002bdd9  l1c_dc_invalidate_all              24   2  Code  RX  hpm_l1c_drv.o
  8002bdda-8002be05  sysctl_clock_set_preset            44   2  Code  RX  board.c.o
  8002be06-8002be55  sdxc_enable_inverse_clock          80   2  Code  RX  board.c.o
  8002be56-8002be99  sdxc_enable_sd_clock               68   2  Code  RX  board.c.o
  8002be9a-8002bebd  board_init                         36   2  Code  RX  board.c.o
  8002bebe-8002becd  board_init_sram_pins               16   2  Code  RX  board.c.o
  8002bece-8002bee1  board_delay_ms                     20   2  Code  RX  board.c.o
  8002bee2-8002c079  board_init_uart_clock             408   2  Code  RX  board.c.o
  8002c07a-8002c095  trgm_enable_io_output              28   2  Code  RX  pinmux.c.o
  8002c096-8002c0e9  trgm_output_config                 84   2  Code  RX  pinmux.c.o
  8002c0ea-8002c241  init_uart_pins                    344   2  Code  RX  pinmux.c.o
  8002c242-8002c2a1  init_sdxc_cmd_pin                  96   2  Code  RX  pinmux.c.o
  8002c2a2-8002c2b1  init_sdxc_pwr_pin                  16   2  Code  RX  pinmux.c.o
  8002c2b2-8002c319  console_init                      104   2  Code  RX  hpm_debug_console.c.o
  8002c31a-8002c399  __SEGGER_RTL_X_file_write         128   2  Code  RX  hpm_debug_console.c.o
  8002c39a-8002c3a5  __SEGGER_RTL_X_file_stat           12   2  Code  RX  hpm_debug_console.c.o
  8002c3a6-8002c3b1  __SEGGER_RTL_X_file_bufsize
                                                        12   2  Code  RX  hpm_debug_console.c.o
  8002c3b2-8002c3cd  femc_sw_reset                      28   2  Code  RX  hpm_femc_drv.c.o
  8002c3ce-8002c415  pcfg_dcdc_set_voltage              72   2  Code  RX  hpm_pcfg_drv.c.o
  8002c416-8002c461  pllctl_pll_powerdown               76   2  Code  RX  hpm_pllctl_drv.c.o
  8002c462-8002c699  pllctl_init_int_pll_with_freq
                                                       568   2  Code  RX  hpm_pllctl_drv.c.o
  8002c69a-8002c6f5  write_pmp_cfg                      92   2  Code  RX  hpm_pmp_drv.c.o
  8002c6f6-8002c751  write_pma_cfg                      92   2  Code  RX  hpm_pmp_drv.c.o
  8002c752-8002c7fd  _clean_up                         172   2  Code  RX  reset.c.o
  8002c7fe-8002c819  reset_handler                      28   2  Code  RX  reset.c.o
  8002c81a-8002c81d  _init                               4   2  Code  RX  reset.c.o
  8002c81e-8002c821  mchtmr_isr                          4   2  Code  RX  trap.c.o
  8002c822-8002c825  swi_isr                             4   2  Code  RX  trap.c.o
  8002c826-8002c851  exception_handler                  44   2  Code  RX  trap.c.o
  8002c852-8002c951  get_frequency_for_source          256   2  Code  RX  hpm_clock_drv.c.o
  8002c952-8002ca29  get_frequency_for_i2s_or_adc
                                                       216   2  Code  RX  hpm_clock_drv.c.o
  8002ca2a-8002ca5d  get_frequency_for_wdg              52   2  Code  RX  hpm_clock_drv.c.o
  8002ca5e-8002ca81  get_frequency_for_pwdg             36   2  Code  RX  hpm_clock_drv.c.o
  8002ca82-8002caad  clock_connect_group_to_cpu
                                                        44   2  Code  RX  hpm_clock_drv.c.o
  8002caae-8002cad9  clock_get_core_clock_ticks_per_ms
                                                        44   2  Code  RX  hpm_clock_drv.c.o
  8002cada-8002cc01  sysctl_enable_group_resource
                                                       296   2  Code  RX  hpm_sysctl_drv.c.o
  8002cc02-8002cc2d  enable_plic_feature                44   2  Code  RX  system.c.o
  8002cc2e-8002cdd9  strtod                            428   2  Code  RX  convops.o (libc_rv32gc_d_balanced.a)
  8002cdda-8002cdfd  __SEGGER_RTL_puts_no_nl            36   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  8002cdfe-8002ce2d  signal                             48   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  8002ce2e-8002ce89  raise                              92   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  8002ce8a-8002ce93  abort                              10   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  8002ce94-8002ced5  __SEGGER_RTL_X_assert              66   2  Code  RX  execops.o (libc_rv32gc_d_balanced.a)
  8002ced6-8002cee1  putchar                            12   2  Code  RX  fileops.o (libc_rv32gc_d_balanced.a)
  8002cee2-8002cf15  puts                               52   2  Code  RX  fileops.o (libc_rv32gc_d_balanced.a)
  8002cf16-8002cf69  __fixunsdfdi                       84   2  Code  RX  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002cf6a-8002cfe5  __SEGGER_RTL_ldouble_to_double
                                                       124   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002cfe6-8002d01d  __SEGGER_RTL_float64_PolyEvalP
                                                        56   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d01e-8002d0b7  __SEGGER_RTL_float64_sin_inline
                                                       154   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d0b8-8002d15d  __SEGGER_RTL_float64_cos_inline
                                                       166   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d15e-8002d17b  __trunctfdf2                       30   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d17c-8002d187  __SEGGER_RTL_float64_signbit
                                                        12   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d188-8002d1e7  ldexp.localalias                   96   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d1e8-8002d1eb  frexp                               4   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d1ec-8002d35f  fmod                              372   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d360-8002d363  sin                                 4   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d364-8002d367  cos                                 4   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d368-8002d46d  atan                              262   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d46e-8002d577  log                               266   2  Code  RX  floatops.o (libc_rv32gc_d_balanced.a)
  8002d578-8002d5df  memset                            104   2  Code  RX  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002d5e0-8002d627  strcpy                             72   2  Code  RX  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002d628-8002d68f  strlen                            104   2  Code  RX  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  8002d690-8002d79b  memcmp                            268   2  Code  RX  strops.o (libc_rv32gc_d_balanced.a)
  8002d79c-8002d7b7  strcat                             28   2  Code  RX  strops.o (libc_rv32gc_d_balanced.a)
  8002d7b8-8002d84f  strnlen                           152   2  Code  RX  strops.o (libc_rv32gc_d_balanced.a)
  8002d850-8002d88b  __SEGGER_RTL_pow10                 60   2  Code  RX  utilops.o (libc_rv32gc_d_balanced.a)
  8002d88c-8002d897  __SEGGER_RTL_stream_write          12   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002d898-8002d933  __SEGGER_RTL_putc                 156   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002d934-8002d95d  __SEGGER_RTL_print_padding
                                                        42   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002d95e-8002d999  sprintf                            60   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002d99a-8002da11  vfprintf_l                        120   2  Code  RX  prinops.o (libc_rv32gc_d_balanced.a)
  8002da12-8002da65  __SEGGER_RTL_alloc                 84   2  Code  RX  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  8002da66-8002da79  __SEGGER_RTL_X_heap_lock           20   2  Code  RX  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  8002da7a-8002da8d  __SEGGER_RTL_X_heap_unlock
                                                        20   2  Code  RX  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  8002da8e-8002dab9  __SEGGER_RTL_ascii_isctype
                                                        44   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002daba-8002dac9  __SEGGER_RTL_ascii_tolower
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002daca-8002daf5  __SEGGER_RTL_ascii_iswctype
                                                        44   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002daf6-8002db05  __SEGGER_RTL_ascii_towlower
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002db06-8002db19  __SEGGER_RTL_ascii_wctomb          20   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002db1a-8002db29  __SEGGER_RTL_current_locale
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002db2a-8002db47  __SEGGER_RTL_isctype               30   2  Code  RX  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  8002db48-8002dc27  __SEGGER_init_table__             224   4  Cnst  RO  [ Linker created ]
  8002dc28-80040c83  __SEGGER_init_data__           77 916   4  Cnst  RO  [ Linker created ]
  80040c84-80040ccd  __SEGGER_init_pack                 74   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  80040cce-80040d2d  __SEGGER_init_lzss                 96   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  80040d2e-80040d41  __SEGGER_init_zero                 20   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  80040d42-80040d5d  __SEGGER_init_copy                 28   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)


***********************************************************************************************
***                                                                                         ***
***                                       SYMBOL LIST                                       ***
***                                                                                         ***
***********************************************************************************************

RAM function symbols by name:

  Symbol name                   Address        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ----------  -----  ----  --  -----------
  ACC_gyroreset_r_TAFEAG16_buf
                             0x00022F34          24      2  Init  Gb  InsTestingEntry.o
  AlgorithmAct               0x00006BA4       2 318      2  Init  Gb  InsTestingEntry.o
  AlgorithmDo                0x00021492          84      2  Init  Gb  InsTestingEntry.o
  Algorithm_before_otherDataDo
                             0x00022F20          24      2  Init  Gb  datado.o
  AnalyticCoordinateAxis     0x000022F8       6 598      2  Init  Gb  InsTestingEntry.o
  AttiToCnb                  0x00018658         318      2  Init  Gb  navi.o
  BindDefaultSet_by_GNSS     0x00020AF8         102      2  Init  Gb  navi.o
  CloseFileToSd              0x0001DE4C         164      2  Init  Gb  sd_fatfs.o
  CnbToAtti                  0x00014FA4         416      2  Init  Gb  navi.o
  CnbToQ                     0x00011394         640      2  Init  Gb  navi.o
  ComputeAttiRate            0x00019D7A         270      2  Init  Gb  navi.o
  ComputeCen                 0x0001D766         188      2  Init  Gb  align.o
  ComputeCib0i               0x0001CAB8         216      2  Init  Gb  align.o
  ComputeCie                 0x0001BE6C         232      2  Init  Gb  align.o
  ComputeDeg_Ex              0x0001F854         124      2  Init  Gb  navi.o
  ComputeDelSenbb            0x0001CE8A         208      2  Init  Gb  navi.o
  ComputeFk                  0x00014924         440      2  Init  Gb  kalman.o
  ComputeFn                  0x00007448       2 166      2  Init  Gb  kalman.o
  ComputeG                   0x0001D5E6         196      2  Init  Gb  navi.o
  ComputeKk                  0x00013278         484      2  Init  Gb  kalman.o
  ComputeLeverArmSn          0x00022544          56      2  Init  Gb  navi.o
  ComputeLeverArmVn          0x00021B04          74      2  Init  Gb  navi.o
  ComputePk                  0x0001C9E8         216      2  Init  Gb  kalman.o
  ComputePkk_1_Step1         0x00020B5E         100      2  Init  Gb  kalman.o
  ComputePkk_1_Step2         0x0001E020         162      2  Init  Gb  kalman.o
  ComputePos                 0x00018BFC         302      2  Init  Gb  navi.o
  ComputeQ                   0x00014770         440      2  Init  Gb  navi.o
  ComputeRmRn                0x0001E58E         156      2  Init  Gb  navi.o
  ComputeSi                  0x00011A74         582      2  Init  Gb  dynamic_align.o
  ComputeSib0                0x0001D97A         178      2  Init  Gb  dynamic_align.o
  ComputeVi                  0x0001D822         186      2  Init  Gb  align.o
  ComputeVib0                0x000205F6         108      2  Init  Gb  align.o
  ComputeVibn                0x000189C6         308      2  Init  Gb  navi.o
  ComputeVn                  0x00015144         410      2  Init  Gb  navi.o
  ComputeWenn                0x000210B2          90      2  Init  Gb  navi.o
  ComputeWien                0x00021160          88      2  Init  Gb  navi.o
  ComputeWnbb                0x0001D6AA         192      2  Init  Gb  navi.o
  ComputeXk                  0x0001D30E         204      2  Init  Gb  kalman.o
  ComputeXkk_1               0x0001FD90         116      2  Init  Gb  kalman.o
  ComputeZk                  0x00009A06       1 350      2  Init  Gb  kalman.o
  CorrectAtti                0x00013B6C         466      2  Init  Gb  kalman.o
  CorrectVn                  0x0002200A          64      2  Init  Gb  kalman.o
  DeleteFileFromSd           0x0001DA9E         172      2  Init  Gb  sd_fatfs.o
  DeviceInit                 0x000222CA          60      2  Init  Gb  datado.o
  Drv_FlashErase             0x00022752          54      2  Init  Gb  FirmwareUpdateFile.o
  Drv_FlashRead              0x00022D22          32      2  Init  Gb  FirmwareUpdateFile.o
  Drv_FlashWrite             0x000220BA          62      2  Init  Gb  FirmwareUpdateFile.o
  Drv_SystemReset            0x00022E98          26      2  Init  Gb  FirmwareUpdateFile.o
  DynamicInertialSysAlignCompute
                             0x00011614         606      2  Init  Gb  dynamic_align.o
  DynamicInertialSysAlign_Init
                             0x0001E294         160      2  Init  Gb  dynamic_align.o
  DynamicNavi_Init           0x0001A27A         266      2  Init  Gb  navi.o
  EXTI3_IRQHandler           0x00022CF0          34      2  Init  Gb  gd32f4xx_it.o
  ErrCorrect_1_Navi_Time     0x0000B288       1 222      2  Init  Gb  kalman.o
  ErrStore_1s                0x00008EEA       1 514      2  Init  Gb  kalman.o
  Exti_Init                  0x00022A38          44      2  Init  Gb  main.o
  FPGATo422_00BB_send        0x0000840A       1 612      2  Init  Gb  InsTestingEntry.o
  Fatfs_Init                 0x00019C92         276      2  Init  Gb  sd_fatfs.o
  FinishDynamicInertialSysAlign
                             0x0001BB2A         236      2  Init  Gb  dynamic_align.o
  FinishInertialSysAlign     0x0001BA5E         236      2  Init  Gb  align.o
  GNSSAndHeadDataTest        0x00013998         468      2  Init  Gb  read_and_check_gnss_data.o
  GNSS_Last_TIME             0x00021E8C          68      2  Init  Gb  InsTestingEntry.o
  GNSS_Lost_Time             0x00021898          78      2  Init  Gb  InsTestingEntry.o
  GNSS_Valid_PPSStart        0x0001C02A         230      2  Init  Gb  InsTestingEntry.o
  Hk_Init                    0x000166B6         380      2  Init  Gb  kalman.o
  INS912AlgorithmEntry       0x0001AE3E         248      2  Init  Gb  InsTestingEntry.o
  INS912_Output              0x0000B732       1 194      2  Init  Gb  INS_Output.o
  INS_Init                   0x0001F174         136      2  Init  Gb  INS_Init.o
  InertialSysAlignCompute    0x000130B2         486      2  Init  Gb  align.o
  InertialSysAlign_Init      0x0001E4F6         156      2  Init  Gb  align.o
  InitParaToAlgorithm        0x0000EC64         786      2  Init  Gb  SetParaBao.o
  KalCompute                 0x00016F2E         364      2  Init  Gb  kalman.o
  KalPredict                 0x0001FB02         122      2  Init  Gb  kalman.o
  Kalman_Init                0x0001C6CE         218      2  Init  Gb  kalman.o
  Kalman_StartUp             0x000094D4       1 354      2  Init  Gb  kalman.o
  LEDIndicator               0x0001D262         204      2  Init  Gb  INS_Init.o
  Led_Control                0x00022E34          28      2  Init  Gb  main.o
  Mat_Inv                    0x0000FA70         700      2  Init  Gb  matvecmath.o
  Mat_Mul                    0x0001DF7E         162      2  Init  Gb  matvecmath.o
  Mat_Tr                     0x000215BC          82      2  Init  Gb  matvecmath.o
  NaviCompute                0x0000E5FA         870      2  Init  Gb  navi.o
  Navi_Init                  0x0001C600         218      2  Init  Gb  navi.o
  ParaUpdateHandle           0x000158F6         400      2  Init  Gb  SetParaBao.o
  Pk_Init                    0x0001AFF4         246      2  Init  Gb  kalman.o
  Pwm_Init                   0x000228D0          46      2  Init  Gb  timer.o
  QToCnb                     0x00012718         518      2  Init  Gb  navi.o
  Qk_Init                    0x0001AEFE         246      2  Init  Gb  kalman.o
  Qua_Mul                    0x00017B6A         324      2  Init  Gb  matvecmath.o
  ReadBB00FileOpenFromSd     0x0001F6EC         128      2  Init  Lc  sd_fatfs.o
  ReadCom3FileOpenFromSd     0x0001F678         128      2  Init  Lc  sd_fatfs.o
  ReadFileOpenFromSd         0x0002144E          84      2  Init  Gb  sd_fatfs.o
  ReadFileToSd               0x0000898E       1 540      2  Init  Gb  sd_fatfs.o
  ReadPara                   0x00019F94         268      2  Init  Gb  SetParaBao.o
  ReadPara0_SetEnd           0x0002038C         112      2  Init  Gb  SetParaBao.o
  ReadPara0_SetHead          0x0001EFDA         140      2  Init  Gb  SetParaBao.o
  ReadPara1_SetEnd           0x00020320         112      2  Init  Gb  SetParaBao.o
  ReadPara1_SetHead          0x0001EF4E         140      2  Init  Gb  SetParaBao.o
  ReadPara2_SetEnd           0x000202B4         112      2  Init  Gb  SetParaBao.o
  ReadPara2_SetHead          0x0001EEC2         140      2  Init  Gb  SetParaBao.o
  ReadPara3_SetEnd           0x00020248         112      2  Init  Gb  SetParaBao.o
  ReadPara3_SetHead          0x0001EE36         140      2  Init  Gb  SetParaBao.o
  ReadPara4_SetEnd           0x000201DC         112      2  Init  Gb  SetParaBao.o
  ReadPara4_SetHead          0x0001EDAA         140      2  Init  Gb  SetParaBao.o
  ReadParaFromFlash          0x0000F7B8         704      2  Init  Gb  SetParaBao.o
  ReadPara_0                 0x0001A180         266      2  Init  Gb  SetParaBao.o
  ReadPara_1                 0x00019B8E         276      2  Init  Gb  SetParaBao.o
  ReadPara_2                 0x00017F32         322      2  Init  Gb  SetParaBao.o
  ReadPara_3                 0x0001A086         266      2  Init  Gb  SetParaBao.o
  ReadPara_4                 0x00016DCE         368      2  Init  Gb  SetParaBao.o
  Read_And_Check_GNSS_Data   0x00020A9A         102      2  Init  Gb  read_and_check_gnss_data.o
  RestoreFactory             0x0001183C         600      2  Init  Gb  SetParaBao.o
  ReturnColliery_Operate     0x0001E7DE         152      2  Init  Gb  main.o
  Rk_Init                    0x0000D772         974      2  Init  Gb  kalman.o
  SDUartIrqInit              0x000206BE         106      2  Init  Gb  uart.o
  SaveGNSSData               0x0000F212         774      2  Init  Gb  read_and_check_gnss_data.o
  SaveINSData                0x0001FA88         122      2  Init  Gb  kalman.o
  SaveParaToFlash            0x000188A8         310      2  Init  Gb  SetParaBao.o
  SdFileOperateTypeSet       0x0001D544         196      2  Init  Gb  sd_fatfs.o
  SdFileReadOperate          0x0001F4AC         130      2  Init  Gb  sd_fatfs.o
  SdFileWriteOperate         0x00022A18          44      2  Init  Gb  sd_fatfs.o
  SendPara_SetEnd            0x00020170         112      2  Init  Gb  SetParaBao.o
  SendPara_SetHead           0x0001ED1E         140      2  Init  Gb  SetParaBao.o
  SendVersionInfo            0x0001FC42         118      2  Init  Gb  datado.o
  SetParaAll                 0x0000BFE8       1 140      2  Init  Gb  SetParaBao.o
  SetParaAngle               0x00018528         320      2  Init  Gb  SetParaBao.o
  SetParaBaud                0x00017472         350      2  Init  Gb  SetParaBao.o
  SetParaCalibration         0x00019998         278      2  Init  Gb  SetParaBao.o
  SetParaColliery_Operate    0x0001B986         236      2  Init  Gb  SetParaBao.o
  SetParaCoord               0x00019140         286      2  Init  Gb  SetParaBao.o
  SetParaDataOutType         0x0001A468         258      2  Init  Gb  SetParaBao.o
  SetParaDebugMode           0x0001B392         240      2  Init  Gb  SetParaBao.o
  SetParaDeviation           0x000183F8         320      2  Init  Gb  SetParaBao.o
  SetParaFactorAcc           0x00015D32         392      2  Init  Gb  SetParaBao.o
  SetParaFactorGyro          0x00015BBA         392      2  Init  Gb  SetParaBao.o
  SetParaFilter              0x0001AC64         250      2  Init  Gb  SetParaBao.o
  SetParaFrequency           0x00019890         280      2  Init  Gb  SetParaBao.o
  SetParaGnss                0x000182C8         320      2  Init  Gb  SetParaBao.o
  SetParaGnssInitValue       0x0001424E         448      2  Init  Gb  SetParaBao.o
  SetParaGpsType             0x0001B2B2         240      2  Init  Gb  SetParaBao.o
  SetParaGyroType            0x0001B1D2         240      2  Init  Gb  SetParaBao.o
  SetParaKalmanQ             0x0001AB7A         250      2  Init  Gb  SetParaBao.o
  SetParaKalmanR             0x0001AA90         250      2  Init  Gb  SetParaBao.o
  SetParaSdHandle            0x00019A9E         276      2  Init  Gb  SetParaBao.o
  SetParaTime                0x0001A5F8         256      2  Init  Gb  SetParaBao.o
  SetParaUpdateEnd           0x00018796         310      2  Init  Gb  SetParaBao.o
  SetParaUpdateSend          0x000176BA         338      2  Init  Gb  SetParaBao.o
  SetParaUpdateStart         0x00015782         400      2  Init  Gb  SetParaBao.o
  SetParaUpdateStop          0x0001BD98         232      2  Init  Gb  SetParaBao.o
  SetParaVector              0x00018198         320      2  Init  Gb  SetParaBao.o
  SysInit                    0x00015466         404      2  Init  Gb  navi.o
  SysVarDefaultSet           0x0001E45A         156      2  Init  Gb  navi.o
  TIMER2_IRQHandler          0x0001A552         258      2  Init  Gb  gd32f4xx_it.o
  TransHeading0to360         0x00021E48          68      2  Init  Gb  navi.o
  UartDmaRecSetPara          0x0000CC60       1 054      2  Init  Gb  SetParaBao.o
  UartIrqInit                0x0001F0F0         136      2  Init  Gb  uart.o
  UartIrqSendMsg             0x0001E334         158      2  Init  Gb  uart.o
  Uart_SendMsg               0x00022814          50      2  Init  Gb  bsp_fmc.o
  UpdateAlignPosAndVn        0x00021260          86      2  Init  Gb  dynamic_align.o
  UpdateEnd_SetEnd           0x00020104         112      2  Init  Gb  SetParaBao.o
  UpdateEnd_SetHead          0x0001EC92         140      2  Init  Gb  SetParaBao.o
  UpdateSend_SetEnd          0x00020098         112      2  Init  Gb  SetParaBao.o
  UpdateSend_SetHead         0x0001EC06         140      2  Init  Gb  SetParaBao.o
  UpdateStart_SetEnd         0x0002002C         112      2  Init  Gb  SetParaBao.o
  UpdateStart_SetHead        0x0001EB7A         140      2  Init  Gb  SetParaBao.o
  UpdateStop_SetEnd          0x0001FFC0         112      2  Init  Gb  SetParaBao.o
  UpdateStop_SetHead         0x0001EAEE         140      2  Init  Gb  SetParaBao.o
  Vec_Cross                  0x0001F42A         130      2  Init  Gb  matvecmath.o
  Virtual_PPS_insert_5hz     0x0001C1D4         226      2  Init  Gb  InsTestingEntry.o
  WriteBB00FileOpenFromSd    0x0001F24E         134      2  Init  Lc  sd_fatfs.o
  WriteBB00FileToSd          0x00016562         380      2  Init  Gb  sd_fatfs.o
  WriteCOM3FileToSd          0x0001DC7E         166      2  Init  Gb  sd_fatfs.o
  WriteCom3FileOpenFromSd    0x0001F1D4         134      2  Init  Lc  sd_fatfs.o
  WriteFileOpenFromSd        0x0001C930         216      2  Init  Gb  sd_fatfs.o
  WriteFileToSd              0x0001DA26         172      2  Init  Gb  sd_fatfs.o
  analysisRxdata             0x0000E2D0         890      2  Init  Gb  uart.o
  app_accum_verify_8bit      0x0002250C          56      2  Init  Gb  app_tool.o
  board_init_sd_host_params  0x00016B18         370      2  Init  Wk  hpm_sdmmc_port.c.o
  bsp_gpio_init              0x0001641E         380      2  Init  Gb  bsp_gpio.o
  bsp_tim_init               0x00022E24          28      2  Init  Gb  bsp_tim.o
  caninfupdate               0x0001A6E8         254      2  Init  Gb  INS_Data.o
  change_bitmap              0x0001C2AA         224      2  Init  Lc  ff.c.o
  check_fs                   0x00013448         476      2  Init  Lc  ff.c.o
  clst2sect                  0x0002271C          54      2  Init  Lc  ff.c.o
  cmp_lfn                    0x0001A370         264      2  Init  Lc  ff.c.o
  comm_axis_read             0x00022FFE          16      2  Init  Gb  computerFrameParse.o
  comm_nav_para_syn          0x00023060           4      2  Init  Gb  computerFrameParse.o
  comm_param_setbits         0x00021E04          68      2  Init  Gb  computerFrameParse.o
  comm_read_currentFreq      0x00022FEE          16      2  Init  Gb  computerFrameParse.o
  core_local_mem_to_sys_address
                             0x00020DE6          96      2  Init  Lc  uart_dma.o
  core_local_mem_to_sys_address
                             0x00020E46          96      2  Init  Lc  hpm_sdmmc_disk.c.o
  core_local_mem_to_sys_address
                             0x00020EA6          96      2  Init  Lc  hpm_sdmmc_port.c.o
  crc_verify_8bit            0x000224D4          56      2  Init  Gb  app_tool.o
  create_chain               0x000104D0         678      2  Init  Lc  ff.c.o
  create_name                0x0000A408       1 308      2  Init  Lc  ff.c.o
  create_partition           0x000109AC         668      2  Init  Lc  ff.c.o
  create_xdir                0x0001935C         282      2  Init  Lc  ff.c.o
  dbc_1st                    0x00021DC0          68      2  Init  Lc  ff.c.o
  default_isr_111            0x00000450         248      4  Init  Gb  hpm_sdmmc_disk.c.o
  default_isr_2              0x0000021C         246      4  Init  Gb  main.o
  default_isr_63             0x0000033C         248      4  Init  Gb  timer.o
  delay_ms                   0x00022F08          24      2  Init  Gb  systick.o
  dir_alloc                  0x0001C386         222      2  Init  Lc  ff.c.o
  dir_clear                  0x0001DDB4         164      2  Init  Lc  ff.c.o
  dir_find                   0x0000F510         724      2  Init  Lc  ff.c.o
  dir_next                   0x00017322         352      2  Init  Lc  ff.c.o
  dir_read                   0x00012CF0         506      2  Init  Lc  ff.c.o
  dir_register               0x0000C884       1 056      2  Init  Lc  ff.c.o
  dir_remove                 0x0001CC4E         214      2  Init  Lc  ff.c.o
  dir_sdi                    0x00019780         280      2  Init  Lc  ff.c.o
  disk_async_write           0x00020A38         102      2  Init  Gb  diskio.c.o
  disk_initialize            0x00021BEE          70      2  Init  Gb  diskio.c.o
  disk_ioctl                 0x0002058E         108      2  Init  Gb  diskio.c.o
  disk_read                  0x000209D6         102      2  Init  Gb  diskio.c.o
  disk_status                0x00021BAC          70      2  Init  Gb  diskio.c.o
  disk_sync_read             0x00020976         102      2  Init  Gb  diskio.c.o
  disk_write                 0x00020914         102      2  Init  Gb  diskio.c.o
  dma_config_linked_descriptor
                             0x000137C4         468      2  Init  Gb  hpm_dma_drv.c.o
  dma_default_channel_config
                             0x0002228E          60      2  Init  Gb  hpm_dma_drv.c.o
  dma_get_remaining_transfer_size
                             0x00022EF0          24      2  Init  Lc  uart_dma.o
  dma_setup_channel          0x00012300         548      2  Init  Gb  hpm_dma_drv.c.o
  dmamux_config              0x00022252          60      2  Init  Lc  uart_dma.o
  extract_csd_field          0x0001CB78         214      2  Init  Gb  hpm_sdmmc_common.c.o
  f_async_write              0x0000D3BE         976      2  Init  Gb  ff.c.o
  f_chdrive                  0x000226EE          54      2  Init  Gb  ff.c.o
  f_close                    0x00020534         108      2  Init  Gb  ff.c.o
  f_lseek                    0x0000A918       1 278      2  Init  Gb  ff.c.o
  f_mkfs                     0x00003B9E       4 942      2  Init  Gb  ff.c.o
  f_mount                    0x0001E978         144      2  Init  Gb  ff.c.o
  f_open                     0x0000ADF6       1 250      2  Init  Gb  ff.c.o
  f_printf                   0x0000BB94       1 152      2  Init  Gb  ff.c.o
  f_read                     0x0000E920         868      2  Init  Gb  ff.c.o
  f_sync                     0x00010756         670      2  Init  Gb  ff.c.o
  f_unlink                   0x00014E26         418      2  Init  Gb  ff.c.o
  f_write                    0x0000D00A         976      2  Init  Gb  ff.c.o
  ff_handle_poll             0x00022BAE          38      2  Init  Gb  ff_queue.o
  ff_queue_get_buffer_by_sector
                             0x0001E746         152      2  Init  Gb  ff_queue.o
  ff_queue_init              0x0002110C          88      2  Init  Gb  ff_queue.o
  ff_queue_is_empty          0x00022AAA          42      2  Init  Gb  ff_queue.o
  ff_queue_is_full           0x00021FCA          64      2  Init  Gb  ff_queue.o
  ff_queue_poll              0x0001E8F0         144      2  Init  Gb  ff_queue.o
  ff_queue_push              0x0001D19C         206      2  Init  Gb  ff_queue.o
  ff_uni2oem                 0x0001D3D2         198      2  Init  Gb  ffunicode.c.o
  ff_wtoupper                0x000169A6         370      2  Init  Gb  ffunicode.c.o
  fill_first_frag            0x00020782         104      2  Init  Lc  ff.c.o
  fill_last_frag             0x0001FE76         114      2  Init  Lc  ff.c.o
  find_bitmap                0x0001B89E         236      2  Init  Lc  ff.c.o
  find_volume                0x0001D0DA         206      2  Init  Lc  ff.c.o
  fmc2sinsraw                0x0000073E       7 230      2  Init  Gb  readpaoche.o
  follow_path                0x000120F4         552      2  Init  Lc  ff.c.o
  fpgadata_Predo             0x00021ADC          74      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen        0x00021B80          72      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_OutDataSet
                             0x00010276         686      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_SetAlgParm_acc
                             0x0000FFD8         694      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_SetAlgParm_gyro
                             0x0001112C         640      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_SetAlgParm_setRParm_acc
                             0x0000DF08         968      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_SetAlgParm_setRParm_gyro
                             0x0000DB40         968      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_algParmCash
                             0x00020FAE          92      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_preAlgParm_370
                             0x00017A68         326      2  Init  Gb  InsTestingEntry.o
  fpgadata_syn_count_do      0x00018F5A         296      2  Init  Gb  fpgad.o
  frame_pack_and_send        0x00004DC0       4 480      2  Init  Gb  frame_analysis.o
  gd_eval_com_init           0x00022E10          28      2  Init  Gb  INS_Init.o
  gen_numname                0x00016832         376      2  Init  Lc  ff.c.o
  get_boot_reason            0x00019E88         268      2  Init  Gb  main.o
  get_fat                    0x00010EBA         658      2  Init  Lc  ff.c.o
  get_fpgadata               0x000229FE          44      2  Init  Gb  fpgad.o
  get_fpgadata_after_otherDataDo
                             0x0001E6C4         154      2  Init  Gb  fpgad.o
  get_fpgadata_before        0x0002305C           4      2  Init  Gb  fpgad.o
  get_fpgadata_do            0x0001969C         280      2  Init  Gb  fpgad.o
  get_ldnumber               0x0001DB3A         170      2  Init  Lc  ff.c.o
  get_uart_tx_idle           0x00022F48          22      2  Init  Gb  uart_dma.o
  gnss_check_bind            0x00010C34         666      2  Init  Gb  InsTestingEntry.o
  gpio_clear_pin_interrupt_flag
                             0x000229D2          44      2  Init  Lc  main.o
  gpio_enable_pin_interrupt  0x000229A6          44      2  Init  Lc  main.o
  gpio_set_pin_input         0x0002297A          44      2  Init  Lc  main.o
  gpio_set_pin_output        0x0002294E          44      2  Init  Lc  bsp_gpio.o
  gpio_toggle_pin            0x00022A80          42      2  Init  Lc  main.o
  gpio_write_pin             0x0002156A          82      2  Init  Lc  INS_Init.o
  gpiom_set_pin_controller   0x000213A6          84      2  Init  Lc  bsp_gpio.o
  gpiom_set_pin_controller   0x000213FA          84      2  Init  Lc  main.o
  gptmr_channel_config       0x00017DF0         322      2  Init  Gb  hpm_gptmr_drv.c.o
  gptmr_channel_get_default_config
                             0x0001FE04         114      2  Init  Gb  hpm_gptmr_drv.c.o
  gptmr_channel_reset_count  0x00021A92          74      2  Init  Lc  timer.o
  gptmr_check_status         0x00022C34          36      2  Init  Lc  timer.o
  gptmr_clear_status         0x00022F5E          20      2  Init  Lc  timer.o
  gptmr_enable_irq           0x00022DF4          28      2  Init  Lc  timer.o
  gptmr_start_counter        0x00022922          44      2  Init  Lc  timer.o
  gptmr_stop_counter         0x000228F6          44      2  Init  Lc  timer.o
  gptmr_update_cmp           0x00021F48          66      2  Init  Lc  timer.o
  hpm_csr_get_core_mcycle    0x0001F994         122      2  Init  Lc  hpm_sdmmc_osal.o
  hpm_csr_get_core_mcycle    0x0001FA0E         122      2  Init  Lc  hpm_sdmmc_sd.c.o
  hpm_sdmmc_osal_delay       0x00022B88          38      2  Init  Wk  hpm_sdmmc_osal.o
  hpm_sdmmc_osal_event_clear
                             0x00022D8C          30      2  Init  Wk  hpm_sdmmc_osal.o
  hpm_sdmmc_osal_event_create
                             0x00022B14          40      2  Init  Wk  hpm_sdmmc_osal.o
  hpm_sdmmc_osal_event_set   0x00022E7E          26      2  Init  Wk  hpm_sdmmc_osal.o
  hpm_sdmmc_osal_event_wait  0x0001BCB8         232      2  Init  Wk  hpm_sdmmc_osal.o
  init_alloc_info            0x00020C76          98      2  Init  Lc  ff.c.o
  init_gpio                  0x00022CCE          34      2  Init  Gb  main.o
  initializationdriversettings
                             0x00016194         384      2  Init  Gb  gdwatch.o
  irq_handler_trap           0x00000554         488      4  Init  Gb  trap.c.o
  isr_gpio                   0x00000200          36      4  Init  Gb  main.o
  ld_clust                   0x00021B40          72      2  Init  Lc  ff.c.o
  ld_dword                   0x00021D7C          68      2  Init  Lc  ff.c.o
  ld_qword                   0x000155F2         400      2  Init  Lc  ff.c.o
  ld_word                    0x00022392          58      2  Init  Lc  ff.c.o
  load_obj_xdir              0x0001F3B0         130      2  Init  Lc  ff.c.o
  load_xdir                  0x0001291E         516      2  Init  Lc  ff.c.o
  loopDoOther                0x00022D76          30      2  Init  Gb  datado.o
  main                       0x000175B8         342      2  Init  Gb  main.o
  mchtmr_get_count           0x00022FCC          18      2  Init  Lc  sd_fatfs.o
  mcusendtopcdriversdata     0x0001409A         452      2  Init  Gb  gdwatch.o
  mount_volume               0x00007CBE       2 016      2  Init  Lc  ff.c.o
  move_window                0x00020724         104      2  Init  Lc  ff.c.o
  myget_16bit_D32            0x0002249C          56      2  Init  Gb  readpaoche.o
  myget_16bit_D64            0x000208AE         102      2  Init  Gb  readpaoche.o
  myget_16bit_I32            0x000226B8          54      2  Init  Gb  readpaoche.o
  myget_8bit_I16             0x00022B62          38      2  Init  Gb  readpaoche.o
  norflash_erase_sector      0x000227EA          50      2  Init  Gb  flash.o
  norflash_init              0x00021852          78      2  Init  Gb  flash.o
  norflash_read              0x00022362          58      2  Init  Gb  flash.o
  norflash_write             0x00022330          58      2  Init  Gb  flash.o
  output_fpga_void           0x0000C44C       1 128      2  Init  Gb  INS_Output.o
  output_fpgatxt_do          0x000162C8         382      2  Init  Gb  INS_Output.o
  output_gdw_do              0x00005D90       3 824      2  Init  Gb  INS_Output.o
  output_normal_do           0x00022CB6          34      2  Init  Gb  INS_Output.o
  pick_lfn                   0x0001B0EA         240      2  Init  Lc  ff.c.o
  pnavout_set                0x0001D8C8         178      2  Init  Gb  InsTestingEntry.o
  ppor_sw_reset              0x00022FBA          18      2  Init  Lc  FirmwareUpdateFile.o
  protocol_send              0x00022EDC          24      2  Init  Gb  computerFrameParse.o
  put_fat                    0x00011CA6         574      2  Init  Lc  ff.c.o
  put_lfn                    0x0001AD4E         248      2  Init  Lc  ff.c.o
  putc_bfd                   0x00013EDC         458      2  Init  Lc  ff.c.o
  putc_flush                 0x00021A4C          74      2  Init  Lc  ff.c.o
  putc_init                  0x00022B3C          38      2  Init  Lc  ff.c.o
  remove_chain               0x00012524         520      2  Init  Lc  ff.c.o
  rom_xpi_nor_auto_config    0x00019556          46      2  Init  Lc  flash.o
  rom_xpi_nor_erase_block    0x000194A4          58      2  Init  Lc  flash.o
  rom_xpi_nor_erase_chip     0x000194DE          54      2  Init  Lc  flash.o
  rom_xpi_nor_erase_sector   0x0001946A          58      2  Init  Lc  flash.o
  rom_xpi_nor_program        0x00019514          66      2  Init  Lc  flash.o
  rom_xpi_nor_read           0x00022216          60      2  Init  Lc  flash.o
  sd_all_send_cid            0x0001FBD0         118      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_app_cmd_send_cond_op    0x000204D0         108      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_app_cmd_set_write_block_erase_count
                             0x0002065E         106      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_be2le                   0x00020C14          98      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_card_init               0x0000EF62         784      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_check_card_parameters   0x0002105C          90      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_convert_data_endian     0x0001DEE0         162      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_decode_csd              0x00009F4C       1 332      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_decode_scr              0x0001E62A         154      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_decode_status           0x000143FE         444      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_deinit                  0x00023052          10      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_disk_async_write        0x00021D3C          68      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_initialize         0x00018AEA         306      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_ioctl              0x0001E1F4         160      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_read               0x000221DE          60      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_status             0x00020BBA          98      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_sync_read          0x0002100A          90      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_write              0x000221A6          60      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_error_recovery          0x0001FD20         116      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_host_init               0x0002120E          86      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_init                    0x0001D498         196      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_is_card_present         0x00022D5C          30      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_mkfs                    0x0001F7DE         126      2  Init  Lc  sd_fatfs.o
  sd_mount_fs                0x0001F604         128      2  Init  Lc  sd_fatfs.o
  sd_polling_card_status_busy
                             0x0001902A         290      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_probe_bus_voltage       0x0001707A         362      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_read_blocks             0x00012ED4         490      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_read_status             0x0001A9A6         250      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_select_card             0x00021CFC          68      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_send_card_status        0x0002084C         102      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_send_cmd                0x00021520          82      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_send_csd                0x0001F58C         128      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_send_if_cond            0x0001FF54         112      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_send_rca                0x00020D8C          96      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_send_scr                0x0001BF54         230      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_set_bus_timing          0x0001924E         282      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_set_bus_width           0x0001E868         144      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_set_max_current         0x00022A5A          42      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_start_write_blocks      0x00013604         472      2  Init  Gb  hpm_sdmmc_sd.c.o
  sd_switch_function         0x00018064         320      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_switch_voltage          0x0002197A          76      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_transfer                0x0001DD1C         164      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_write_blocks            0x00012B06         514      2  Init  Gb  hpm_sdmmc_sd.c.o
  sdcard_isr                 0x00000438          28      4  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmc_card_async_write     0x0001B612         238      2  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmc_card_read            0x0001C52C         220      2  Init  Lc  hpm_sdmmc_disk.c.o
  sdmmc_card_write           0x0001C458         220      2  Init  Lc  hpm_sdmmc_disk.c.o
  sdmmc_enable_auto_tuning   0x0001F522         128      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmc_get_card_and_aligned_buf_info
                             0x0002216A          60      2  Init  Lc  hpm_sdmmc_disk.c.o
  sdmmc_get_sys_addr         0x00022C14          36      2  Init  Gb  hpm_sdmmc_port.c.o
  sdmmc_go_idle_state        0x000214D2          82      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmc_select_card          0x0001FCB0         116      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmc_send_application_command
                             0x00020D30          96      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmc_set_block_size       0x00021356          84      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmchost_check_host_availability
                             0x00022080          62      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_delay_ms         0x00022DDE          28      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_enable_sdio_interrupt
                             0x00020F5E          92      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_error_recovery   0x00022C98          34      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_get_data_pin_level
                             0x0002177E          80      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_init             0x0001B550         238      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_init_io          0x0000FD2C         696      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_irq_handler      0x0001B6EC         238      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_is_8bit_supported
                             0x00022EC4          24      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_is_card_detected
                             0x0001E158         160      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_is_voltage_switch_supported
                             0x00022DC2          28      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_power_control    0x000152DE         408      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_register_xfer_complete_callback
                             0x00022C76          34      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_select_voltage   0x00021932          76      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_send_command     0x0001CDD6         210      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_set_card_bus_width
                             0x0002204A          62      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_set_card_clock   0x000227B6          52      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_set_cardclk_delay_chain
                             0x0001F936         122      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_set_speed_mode   0x00022468          56      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_start_transfer   0x0001A8C4         250      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_switch_to_1v8    0x0001BBF6         234      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_switch_to_3v3_as_needed
                             0x00022C58          34      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_transfer         0x00016C86         368      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_vsel_pin_control
                             0x0001A7CE         250      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_wait_card_active
                             0x00022AF6          40      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_wait_command_done
                             0x0002173A          80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_wait_idle        0x000216F2          80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_wait_xfer_done   0x000216AE          80      2  Init  Lc  hpm_sdmmc_host.c.o
  sduart_recv_polling        0x0001F34C         132      2  Init  Gb  uart.o
  sdxc_clear_interrupt_status
                             0x00022F96          18      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_clear_interrupt_status
                             0x00022FA8          18      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_auto_tuning    0x00022430          56      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_interrupt_signal
                             0x00022682          54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_interrupt_status
                             0x00022616          54      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_interrupt_status
                             0x0002264C          54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_inverse_clock  0x0002160E          80      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_inverse_clock  0x0002165E          80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_power          0x00022870          48      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_enable_power          0x000228A0          48      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_sd_clock       0x00021C74          68      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_sd_clock       0x00021CB8          68      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_tm_clock       0x00022840          48      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_error_recovery        0x00017936         330      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_error_recovery_first_half
                             0x0001B472         238      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_execute_tuning        0x00022EAC          24      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_get_data3_0_level     0x00022F84          18      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_data7_4_level     0x00022F72          18      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_data_bus_width    0x00022782          52      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_get_default_cardclk_delay_chain
                             0x00022BF0          36      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_interrupt_signal  0x00023038          14      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_interrupt_status  0x0002301C          14      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_get_interrupt_status  0x0002302A          14      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_present_status    0x0002300E          14      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_init                  0x0001D018         206      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_is_bus_idle           0x00022D3E          30      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_is_card_inserted      0x00022E64          26      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_is_ddr50_supported    0x00023046          12      2  Init  Lc  hpm_sdmmc_port.c.o
  sdxc_is_inverse_clock_enabled
                             0x000225AA          54      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_is_inverse_clock_enabled
                             0x000225E0          54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_parse_interrupt_status
                             0x000145BA         442      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_perform_auto_tuning   0x0001FB76         118      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_perform_tuning_flow_sequence
                             0x0001C870         216      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_prepare_cmd_xfer      0x000177EC         330      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_receive_cmd_response  0x0001C798         216      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_reset                 0x0001FEE4         112      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_reset_tuning_engine   0x00022E4A          26      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_select_cardclk_delay_source
                             0x00022FDE          16      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_select_voltage        0x00020CD0          96      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_send_command          0x00022400          56      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_adma2_desc        0x00011EC8         556      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_adma3_desc        0x00014ADC         434      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_adma_table_config
                             0x0001E3CA         156      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_cardclk_delay_chain
                             0x00021C30          68      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_set_data_bus_width    0x00020F06          92      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_data_timeout      0x0001DBE0         166      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_dma_config        0x0001F760         126      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_post_change_delay
                             0x00022574          54      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_set_speed_mode        0x00021302          84      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_set_transfer_config   0x0001F2C8         132      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_stop_clock_during_phase_code_change
                             0x000222F6          58      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_transfer_cb           0x0001F066         138      2  Init  Lc  ff_queue.o
  sdxc_transfer_nonblocking  0x00014C86         428      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_tuning_error_recovery
                             0x00021F12          66      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_wait_card_active      0x0002212E          60      2  Init  Lc  hpm_sdmmc_host.c.o
  set_pwm_waveform_edge_aligned_duty
                             0x000212B6          84      2  Init  Lc  timer.o
  set_pwm_waveform_edge_aligned_frequency
                             0x0001EA7E         140      2  Init  Lc  timer.o
  show_error_string          0x00018E32         296      2  Init  Gb  sd_fatfs.o
  st_clust                   0x00021810          78      2  Init  Lc  ff.c.o
  st_dword                   0x000207E6         102      2  Init  Lc  ff.c.o
  st_qword                   0x00017CAE         322      2  Init  Lc  ff.c.o
  st_word                    0x00021ED0          66      2  Init  Lc  ff.c.o
  store_xdir                 0x0001C104         228      2  Init  Lc  ff.c.o
  sum_sfn                    0x000218E6          76      2  Init  Lc  ff.c.o
  sync_fs                    0x00018D2A         296      2  Init  Lc  ff.c.o
  sync_window                0x0001E9F8         142      2  Init  Lc  ff.c.o
  tchar2uni                  0x00019584         280      2  Init  Lc  ff.c.o
  test_gpio_input_interrupt  0x0001CF56         206      2  Init  Gb  main.o
  test_uart_recv_polling     0x0001F8D0         122      2  Init  Lc  uart.o
  tick_ms_isr                0x00000314          52      4  Init  Gb  timer.o
  timer_Init                 0x00022D06          32      2  Init  Gb  timer.o
  timer_config               0x0001B7C2         236      2  Init  Lc  timer.o
  uart4sendmsg               0x00022DAA          28      2  Init  Gb  gd32f4xx_it.o
  uart_calculate_baudrate    0x000171C0         354      2  Init  Lc  hpm_uart_drv.c.o
  uart_default_config        0x000211B8          86      2  Init  Gb  hpm_uart_drv.c.o
  uart_dma_init              0x00016028         386      2  Init  Gb  uart_dma.o
  uart_dma_output            0x000223CC          56      2  Init  Gb  uart_dma.o
  uart_dma_recv_polling      0x00015EAA         386      2  Init  Gb  uart_dma.o
  uart_dma_tx_send           0x00013D3A         458      2  Init  Lc  uart_dma.o
  uart_flush                 0x00021F8A          64      2  Init  Gb  hpm_uart_drv.c.o
  uart_get_current_recv_remaining_size
                             0x00022AD4          40      2  Init  Lc  uart_dma.o
  uart_init                  0x00015A36         396      2  Init  Gb  hpm_uart_drv.c.o
  uart_modem_config          0x000220F2          60      2  Init  Lc  hpm_uart_drv.c.o
  uart_rx_dma_autorun        0x0001CD18         210      2  Init  Lc  uart_dma.o
  uart_send_byte             0x000217C2          78      2  Init  Gb  hpm_uart_drv.c.o
  uart_tx_dma                0x00021A0A          74      2  Init  Lc  uart_dma.o
  validate                   0x00020466         110      2  Init  Lc  ff.c.o
  xdir_sum                   0x000203F8         110      2  Init  Lc  ff.c.o
  xname_sum                  0x0001E0BC         160      2  Init  Lc  ff.c.o
  xor_check                  0x000219C0          74      2  Init  Gb  frame_analysis.o
  xsum32                     0x00022BCC          36      2  Init  Lc  ff.c.o

RAM function symbols by address:

     Address  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  -------------------------  ----------  -----  ----  --  -----------
  0x00000200  isr_gpio                           36      4  Init  Gb  main.o
  0x0000021C  default_isr_2                     246      4  Init  Gb  main.o
  0x00000314  tick_ms_isr                        52      4  Init  Gb  timer.o
  0x0000033C  default_isr_63                    248      4  Init  Gb  timer.o
  0x00000438  sdcard_isr                         28      4  Init  Gb  hpm_sdmmc_disk.c.o
  0x00000450  default_isr_111                   248      4  Init  Gb  hpm_sdmmc_disk.c.o
  0x00000554  irq_handler_trap                  488      4  Init  Gb  trap.c.o
  0x0000073E  fmc2sinsraw                     7 230      2  Init  Gb  readpaoche.o
  0x000022F8  AnalyticCoordinateAxis          6 598      2  Init  Gb  InsTestingEntry.o
  0x00003B9E  f_mkfs                          4 942      2  Init  Gb  ff.c.o
  0x00004DC0  frame_pack_and_send             4 480      2  Init  Gb  frame_analysis.o
  0x00005D90  output_gdw_do                   3 824      2  Init  Gb  INS_Output.o
  0x00006BA4  AlgorithmAct                    2 318      2  Init  Gb  InsTestingEntry.o
  0x00007448  ComputeFn                       2 166      2  Init  Gb  kalman.o
  0x00007CBE  mount_volume                    2 016      2  Init  Lc  ff.c.o
  0x0000840A  FPGATo422_00BB_send             1 612      2  Init  Gb  InsTestingEntry.o
  0x0000898E  ReadFileToSd                    1 540      2  Init  Gb  sd_fatfs.o
  0x00008EEA  ErrStore_1s                     1 514      2  Init  Gb  kalman.o
  0x000094D4  Kalman_StartUp                  1 354      2  Init  Gb  kalman.o
  0x00009A06  ComputeZk                       1 350      2  Init  Gb  kalman.o
  0x00009F4C  sd_decode_csd                   1 332      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0000A408  create_name                     1 308      2  Init  Lc  ff.c.o
  0x0000A918  f_lseek                         1 278      2  Init  Gb  ff.c.o
  0x0000ADF6  f_open                          1 250      2  Init  Gb  ff.c.o
  0x0000B288  ErrCorrect_1_Navi_Time          1 222      2  Init  Gb  kalman.o
  0x0000B732  INS912_Output                   1 194      2  Init  Gb  INS_Output.o
  0x0000BB94  f_printf                        1 152      2  Init  Gb  ff.c.o
  0x0000BFE8  SetParaAll                      1 140      2  Init  Gb  SetParaBao.o
  0x0000C44C  output_fpga_void                1 128      2  Init  Gb  INS_Output.o
  0x0000C884  dir_register                    1 056      2  Init  Lc  ff.c.o
  0x0000CC60  UartDmaRecSetPara               1 054      2  Init  Gb  SetParaBao.o
  0x0000D00A  f_write                           976      2  Init  Gb  ff.c.o
  0x0000D3BE  f_async_write                     976      2  Init  Gb  ff.c.o
  0x0000D772  Rk_Init                           974      2  Init  Gb  kalman.o
  0x0000DB40  fpgadata_Predo_chen_SetAlgParm_setRParm_gyro
                                                968      2  Init  Gb  InsTestingEntry.o
  0x0000DF08  fpgadata_Predo_chen_SetAlgParm_setRParm_acc
                                                968      2  Init  Gb  InsTestingEntry.o
  0x0000E2D0  analysisRxdata                    890      2  Init  Gb  uart.o
  0x0000E5FA  NaviCompute                       870      2  Init  Gb  navi.o
  0x0000E920  f_read                            868      2  Init  Gb  ff.c.o
  0x0000EC64  InitParaToAlgorithm               786      2  Init  Gb  SetParaBao.o
  0x0000EF62  sd_card_init                      784      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x0000F212  SaveGNSSData                      774      2  Init  Gb  read_and_check_gnss_data.o
  0x0000F510  dir_find                          724      2  Init  Lc  ff.c.o
  0x0000F7B8  ReadParaFromFlash                 704      2  Init  Gb  SetParaBao.o
  0x0000FA70  Mat_Inv                           700      2  Init  Gb  matvecmath.o
  0x0000FD2C  sdmmchost_init_io                 696      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0000FFD8  fpgadata_Predo_chen_SetAlgParm_acc
                                                694      2  Init  Gb  InsTestingEntry.o
  0x00010276  fpgadata_Predo_chen_OutDataSet
                                                686      2  Init  Gb  InsTestingEntry.o
  0x000104D0  create_chain                      678      2  Init  Lc  ff.c.o
  0x00010756  f_sync                            670      2  Init  Gb  ff.c.o
  0x000109AC  create_partition                  668      2  Init  Lc  ff.c.o
  0x00010C34  gnss_check_bind                   666      2  Init  Gb  InsTestingEntry.o
  0x00010EBA  get_fat                           658      2  Init  Lc  ff.c.o
  0x0001112C  fpgadata_Predo_chen_SetAlgParm_gyro
                                                640      2  Init  Gb  InsTestingEntry.o
  0x00011394  CnbToQ                            640      2  Init  Gb  navi.o
  0x00011614  DynamicInertialSysAlignCompute
                                                606      2  Init  Gb  dynamic_align.o
  0x0001183C  RestoreFactory                    600      2  Init  Gb  SetParaBao.o
  0x00011A74  ComputeSi                         582      2  Init  Gb  dynamic_align.o
  0x00011CA6  put_fat                           574      2  Init  Lc  ff.c.o
  0x00011EC8  sdxc_set_adma2_desc               556      2  Init  Gb  hpm_sdxc_drv.c.o
  0x000120F4  follow_path                       552      2  Init  Lc  ff.c.o
  0x00012300  dma_setup_channel                 548      2  Init  Gb  hpm_dma_drv.c.o
  0x00012524  remove_chain                      520      2  Init  Lc  ff.c.o
  0x00012718  QToCnb                            518      2  Init  Gb  navi.o
  0x0001291E  load_xdir                         516      2  Init  Lc  ff.c.o
  0x00012B06  sd_write_blocks                   514      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00012CF0  dir_read                          506      2  Init  Lc  ff.c.o
  0x00012ED4  sd_read_blocks                    490      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x000130B2  InertialSysAlignCompute           486      2  Init  Gb  align.o
  0x00013278  ComputeKk                         484      2  Init  Gb  kalman.o
  0x00013448  check_fs                          476      2  Init  Lc  ff.c.o
  0x00013604  sd_start_write_blocks             472      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x000137C4  dma_config_linked_descriptor
                                                468      2  Init  Gb  hpm_dma_drv.c.o
  0x00013998  GNSSAndHeadDataTest               468      2  Init  Gb  read_and_check_gnss_data.o
  0x00013B6C  CorrectAtti                       466      2  Init  Gb  kalman.o
  0x00013D3A  uart_dma_tx_send                  458      2  Init  Lc  uart_dma.o
  0x00013EDC  putc_bfd                          458      2  Init  Lc  ff.c.o
  0x0001409A  mcusendtopcdriversdata            452      2  Init  Gb  gdwatch.o
  0x0001424E  SetParaGnssInitValue              448      2  Init  Gb  SetParaBao.o
  0x000143FE  sd_decode_status                  444      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x000145BA  sdxc_parse_interrupt_status
                                                442      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00014770  ComputeQ                          440      2  Init  Gb  navi.o
  0x00014924  ComputeFk                         440      2  Init  Gb  kalman.o
  0x00014ADC  sdxc_set_adma3_desc               434      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00014C86  sdxc_transfer_nonblocking         428      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00014E26  f_unlink                          418      2  Init  Gb  ff.c.o
  0x00014FA4  CnbToAtti                         416      2  Init  Gb  navi.o
  0x00015144  ComputeVn                         410      2  Init  Gb  navi.o
  0x000152DE  sdmmchost_power_control           408      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00015466  SysInit                           404      2  Init  Gb  navi.o
  0x000155F2  ld_qword                          400      2  Init  Lc  ff.c.o
  0x00015782  SetParaUpdateStart                400      2  Init  Gb  SetParaBao.o
  0x000158F6  ParaUpdateHandle                  400      2  Init  Gb  SetParaBao.o
  0x00015A36  uart_init                         396      2  Init  Gb  hpm_uart_drv.c.o
  0x00015BBA  SetParaFactorGyro                 392      2  Init  Gb  SetParaBao.o
  0x00015D32  SetParaFactorAcc                  392      2  Init  Gb  SetParaBao.o
  0x00015EAA  uart_dma_recv_polling             386      2  Init  Gb  uart_dma.o
  0x00016028  uart_dma_init                     386      2  Init  Gb  uart_dma.o
  0x00016194  initializationdriversettings
                                                384      2  Init  Gb  gdwatch.o
  0x000162C8  output_fpgatxt_do                 382      2  Init  Gb  INS_Output.o
  0x0001641E  bsp_gpio_init                     380      2  Init  Gb  bsp_gpio.o
  0x00016562  WriteBB00FileToSd                 380      2  Init  Gb  sd_fatfs.o
  0x000166B6  Hk_Init                           380      2  Init  Gb  kalman.o
  0x00016832  gen_numname                       376      2  Init  Lc  ff.c.o
  0x000169A6  ff_wtoupper                       370      2  Init  Gb  ffunicode.c.o
  0x00016B18  board_init_sd_host_params         370      2  Init  Wk  hpm_sdmmc_port.c.o
  0x00016C86  sdmmchost_transfer                368      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00016DCE  ReadPara_4                        368      2  Init  Gb  SetParaBao.o
  0x00016F2E  KalCompute                        364      2  Init  Gb  kalman.o
  0x0001707A  sd_probe_bus_voltage              362      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x000171C0  uart_calculate_baudrate           354      2  Init  Lc  hpm_uart_drv.c.o
  0x00017322  dir_next                          352      2  Init  Lc  ff.c.o
  0x00017472  SetParaBaud                       350      2  Init  Gb  SetParaBao.o
  0x000175B8  main                              342      2  Init  Gb  main.o
  0x000176BA  SetParaUpdateSend                 338      2  Init  Gb  SetParaBao.o
  0x000177EC  sdxc_prepare_cmd_xfer             330      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00017936  sdxc_error_recovery               330      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00017A68  fpgadata_Predo_chen_preAlgParm_370
                                                326      2  Init  Gb  InsTestingEntry.o
  0x00017B6A  Qua_Mul                           324      2  Init  Gb  matvecmath.o
  0x00017CAE  st_qword                          322      2  Init  Lc  ff.c.o
  0x00017DF0  gptmr_channel_config              322      2  Init  Gb  hpm_gptmr_drv.c.o
  0x00017F32  ReadPara_2                        322      2  Init  Gb  SetParaBao.o
  0x00018064  sd_switch_function                320      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00018198  SetParaVector                     320      2  Init  Gb  SetParaBao.o
  0x000182C8  SetParaGnss                       320      2  Init  Gb  SetParaBao.o
  0x000183F8  SetParaDeviation                  320      2  Init  Gb  SetParaBao.o
  0x00018528  SetParaAngle                      320      2  Init  Gb  SetParaBao.o
  0x00018658  AttiToCnb                         318      2  Init  Gb  navi.o
  0x00018796  SetParaUpdateEnd                  310      2  Init  Gb  SetParaBao.o
  0x000188A8  SaveParaToFlash                   310      2  Init  Gb  SetParaBao.o
  0x000189C6  ComputeVibn                       308      2  Init  Gb  navi.o
  0x00018AEA  sd_disk_initialize                306      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x00018BFC  ComputePos                        302      2  Init  Gb  navi.o
  0x00018D2A  sync_fs                           296      2  Init  Lc  ff.c.o
  0x00018E32  show_error_string                 296      2  Init  Gb  sd_fatfs.o
  0x00018F5A  fpgadata_syn_count_do             296      2  Init  Gb  fpgad.o
  0x0001902A  sd_polling_card_status_busy
                                                290      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00019140  SetParaCoord                      286      2  Init  Gb  SetParaBao.o
  0x0001924E  sd_set_bus_timing                 282      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001935C  create_xdir                       282      2  Init  Lc  ff.c.o
  0x0001946A  rom_xpi_nor_erase_sector           58      2  Init  Lc  flash.o
  0x000194A4  rom_xpi_nor_erase_block            58      2  Init  Lc  flash.o
  0x000194DE  rom_xpi_nor_erase_chip             54      2  Init  Lc  flash.o
  0x00019514  rom_xpi_nor_program                66      2  Init  Lc  flash.o
  0x00019556  rom_xpi_nor_auto_config            46      2  Init  Lc  flash.o
  0x00019584  tchar2uni                         280      2  Init  Lc  ff.c.o
  0x0001969C  get_fpgadata_do                   280      2  Init  Gb  fpgad.o
  0x00019780  dir_sdi                           280      2  Init  Lc  ff.c.o
  0x00019890  SetParaFrequency                  280      2  Init  Gb  SetParaBao.o
  0x00019998  SetParaCalibration                278      2  Init  Gb  SetParaBao.o
  0x00019A9E  SetParaSdHandle                   276      2  Init  Gb  SetParaBao.o
  0x00019B8E  ReadPara_1                        276      2  Init  Gb  SetParaBao.o
  0x00019C92  Fatfs_Init                        276      2  Init  Gb  sd_fatfs.o
  0x00019D7A  ComputeAttiRate                   270      2  Init  Gb  navi.o
  0x00019E88  get_boot_reason                   268      2  Init  Gb  main.o
  0x00019F94  ReadPara                          268      2  Init  Gb  SetParaBao.o
  0x0001A086  ReadPara_3                        266      2  Init  Gb  SetParaBao.o
  0x0001A180  ReadPara_0                        266      2  Init  Gb  SetParaBao.o
  0x0001A27A  DynamicNavi_Init                  266      2  Init  Gb  navi.o
  0x0001A370  cmp_lfn                           264      2  Init  Lc  ff.c.o
  0x0001A468  SetParaDataOutType                258      2  Init  Gb  SetParaBao.o
  0x0001A552  TIMER2_IRQHandler                 258      2  Init  Gb  gd32f4xx_it.o
  0x0001A5F8  SetParaTime                       256      2  Init  Gb  SetParaBao.o
  0x0001A6E8  caninfupdate                      254      2  Init  Gb  INS_Data.o
  0x0001A7CE  sdmmchost_vsel_pin_control
                                                250      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001A8C4  sdmmchost_start_transfer          250      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001A9A6  sd_read_status                    250      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x0001AA90  SetParaKalmanR                    250      2  Init  Gb  SetParaBao.o
  0x0001AB7A  SetParaKalmanQ                    250      2  Init  Gb  SetParaBao.o
  0x0001AC64  SetParaFilter                     250      2  Init  Gb  SetParaBao.o
  0x0001AD4E  put_lfn                           248      2  Init  Lc  ff.c.o
  0x0001AE3E  INS912AlgorithmEntry              248      2  Init  Gb  InsTestingEntry.o
  0x0001AEFE  Qk_Init                           246      2  Init  Gb  kalman.o
  0x0001AFF4  Pk_Init                           246      2  Init  Gb  kalman.o
  0x0001B0EA  pick_lfn                          240      2  Init  Lc  ff.c.o
  0x0001B1D2  SetParaGyroType                   240      2  Init  Gb  SetParaBao.o
  0x0001B2B2  SetParaGpsType                    240      2  Init  Gb  SetParaBao.o
  0x0001B392  SetParaDebugMode                  240      2  Init  Gb  SetParaBao.o
  0x0001B472  sdxc_error_recovery_first_half
                                                238      2  Init  Lc  hpm_sdxc_drv.c.o
  0x0001B550  sdmmchost_init                    238      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001B612  sdmmc_card_async_write            238      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x0001B6EC  sdmmchost_irq_handler             238      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001B7C2  timer_config                      236      2  Init  Lc  timer.o
  0x0001B89E  find_bitmap                       236      2  Init  Lc  ff.c.o
  0x0001B986  SetParaColliery_Operate           236      2  Init  Gb  SetParaBao.o
  0x0001BA5E  FinishInertialSysAlign            236      2  Init  Gb  align.o
  0x0001BB2A  FinishDynamicInertialSysAlign
                                                236      2  Init  Gb  dynamic_align.o
  0x0001BBF6  sdmmchost_switch_to_1v8           234      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001BCB8  hpm_sdmmc_osal_event_wait         232      2  Init  Wk  hpm_sdmmc_osal.o
  0x0001BD98  SetParaUpdateStop                 232      2  Init  Gb  SetParaBao.o
  0x0001BE6C  ComputeCie                        232      2  Init  Gb  align.o
  0x0001BF54  sd_send_scr                       230      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001C02A  GNSS_Valid_PPSStart               230      2  Init  Gb  InsTestingEntry.o
  0x0001C104  store_xdir                        228      2  Init  Lc  ff.c.o
  0x0001C1D4  Virtual_PPS_insert_5hz            226      2  Init  Gb  InsTestingEntry.o
  0x0001C2AA  change_bitmap                     224      2  Init  Lc  ff.c.o
  0x0001C386  dir_alloc                         222      2  Init  Lc  ff.c.o
  0x0001C458  sdmmc_card_write                  220      2  Init  Lc  hpm_sdmmc_disk.c.o
  0x0001C52C  sdmmc_card_read                   220      2  Init  Lc  hpm_sdmmc_disk.c.o
  0x0001C600  Navi_Init                         218      2  Init  Gb  navi.o
  0x0001C6CE  Kalman_Init                       218      2  Init  Gb  kalman.o
  0x0001C798  sdxc_receive_cmd_response         216      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001C870  sdxc_perform_tuning_flow_sequence
                                                216      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001C930  WriteFileOpenFromSd               216      2  Init  Gb  sd_fatfs.o
  0x0001C9E8  ComputePk                         216      2  Init  Gb  kalman.o
  0x0001CAB8  ComputeCib0i                      216      2  Init  Gb  align.o
  0x0001CB78  extract_csd_field                 214      2  Init  Gb  hpm_sdmmc_common.c.o
  0x0001CC4E  dir_remove                        214      2  Init  Lc  ff.c.o
  0x0001CD18  uart_rx_dma_autorun               210      2  Init  Lc  uart_dma.o
  0x0001CDD6  sdmmchost_send_command            210      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001CE8A  ComputeDelSenbb                   208      2  Init  Gb  navi.o
  0x0001CF56  test_gpio_input_interrupt         206      2  Init  Gb  main.o
  0x0001D018  sdxc_init                         206      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001D0DA  find_volume                       206      2  Init  Lc  ff.c.o
  0x0001D19C  ff_queue_push                     206      2  Init  Gb  ff_queue.o
  0x0001D262  LEDIndicator                      204      2  Init  Gb  INS_Init.o
  0x0001D30E  ComputeXk                         204      2  Init  Gb  kalman.o
  0x0001D3D2  ff_uni2oem                        198      2  Init  Gb  ffunicode.c.o
  0x0001D498  sd_init                           196      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x0001D544  SdFileOperateTypeSet              196      2  Init  Gb  sd_fatfs.o
  0x0001D5E6  ComputeG                          196      2  Init  Gb  navi.o
  0x0001D6AA  ComputeWnbb                       192      2  Init  Gb  navi.o
  0x0001D766  ComputeCen                        188      2  Init  Gb  align.o
  0x0001D822  ComputeVi                         186      2  Init  Gb  align.o
  0x0001D8C8  pnavout_set                       178      2  Init  Gb  InsTestingEntry.o
  0x0001D97A  ComputeSib0                       178      2  Init  Gb  dynamic_align.o
  0x0001DA26  WriteFileToSd                     172      2  Init  Gb  sd_fatfs.o
  0x0001DA9E  DeleteFileFromSd                  172      2  Init  Gb  sd_fatfs.o
  0x0001DB3A  get_ldnumber                      170      2  Init  Lc  ff.c.o
  0x0001DBE0  sdxc_set_data_timeout             166      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001DC7E  WriteCOM3FileToSd                 166      2  Init  Gb  sd_fatfs.o
  0x0001DD1C  sd_transfer                       164      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001DDB4  dir_clear                         164      2  Init  Lc  ff.c.o
  0x0001DE4C  CloseFileToSd                     164      2  Init  Gb  sd_fatfs.o
  0x0001DEE0  sd_convert_data_endian            162      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001DF7E  Mat_Mul                           162      2  Init  Gb  matvecmath.o
  0x0001E020  ComputePkk_1_Step2                162      2  Init  Gb  kalman.o
  0x0001E0BC  xname_sum                         160      2  Init  Lc  ff.c.o
  0x0001E158  sdmmchost_is_card_detected
                                                160      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001E1F4  sd_disk_ioctl                     160      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x0001E294  DynamicInertialSysAlign_Init
                                                160      2  Init  Gb  dynamic_align.o
  0x0001E334  UartIrqSendMsg                    158      2  Init  Gb  uart.o
  0x0001E3CA  sdxc_set_adma_table_config
                                                156      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001E45A  SysVarDefaultSet                  156      2  Init  Gb  navi.o
  0x0001E4F6  InertialSysAlign_Init             156      2  Init  Gb  align.o
  0x0001E58E  ComputeRmRn                       156      2  Init  Gb  navi.o
  0x0001E62A  sd_decode_scr                     154      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001E6C4  get_fpgadata_after_otherDataDo
                                                154      2  Init  Gb  fpgad.o
  0x0001E746  ff_queue_get_buffer_by_sector
                                                152      2  Init  Gb  ff_queue.o
  0x0001E7DE  ReturnColliery_Operate            152      2  Init  Gb  main.o
  0x0001E868  sd_set_bus_width                  144      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001E8F0  ff_queue_poll                     144      2  Init  Gb  ff_queue.o
  0x0001E978  f_mount                           144      2  Init  Gb  ff.c.o
  0x0001E9F8  sync_window                       142      2  Init  Lc  ff.c.o
  0x0001EA7E  set_pwm_waveform_edge_aligned_frequency
                                                140      2  Init  Lc  timer.o
  0x0001EAEE  UpdateStop_SetHead                140      2  Init  Gb  SetParaBao.o
  0x0001EB7A  UpdateStart_SetHead               140      2  Init  Gb  SetParaBao.o
  0x0001EC06  UpdateSend_SetHead                140      2  Init  Gb  SetParaBao.o
  0x0001EC92  UpdateEnd_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001ED1E  SendPara_SetHead                  140      2  Init  Gb  SetParaBao.o
  0x0001EDAA  ReadPara4_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001EE36  ReadPara3_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001EEC2  ReadPara2_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001EF4E  ReadPara1_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001EFDA  ReadPara0_SetHead                 140      2  Init  Gb  SetParaBao.o
  0x0001F066  sdxc_transfer_cb                  138      2  Init  Lc  ff_queue.o
  0x0001F0F0  UartIrqInit                       136      2  Init  Gb  uart.o
  0x0001F174  INS_Init                          136      2  Init  Gb  INS_Init.o
  0x0001F1D4  WriteCom3FileOpenFromSd           134      2  Init  Lc  sd_fatfs.o
  0x0001F24E  WriteBB00FileOpenFromSd           134      2  Init  Lc  sd_fatfs.o
  0x0001F2C8  sdxc_set_transfer_config          132      2  Init  Lc  hpm_sdxc_drv.c.o
  0x0001F34C  sduart_recv_polling               132      2  Init  Gb  uart.o
  0x0001F3B0  load_obj_xdir                     130      2  Init  Lc  ff.c.o
  0x0001F42A  Vec_Cross                         130      2  Init  Gb  matvecmath.o
  0x0001F4AC  SdFileReadOperate                 130      2  Init  Gb  sd_fatfs.o
  0x0001F522  sdmmc_enable_auto_tuning          128      2  Init  Gb  hpm_sdmmc_common.c.o
  0x0001F58C  sd_send_csd                       128      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001F604  sd_mount_fs                       128      2  Init  Lc  sd_fatfs.o
  0x0001F678  ReadCom3FileOpenFromSd            128      2  Init  Lc  sd_fatfs.o
  0x0001F6EC  ReadBB00FileOpenFromSd            128      2  Init  Lc  sd_fatfs.o
  0x0001F760  sdxc_set_dma_config               126      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001F7DE  sd_mkfs                           126      2  Init  Lc  sd_fatfs.o
  0x0001F854  ComputeDeg_Ex                     124      2  Init  Gb  navi.o
  0x0001F8D0  test_uart_recv_polling            122      2  Init  Lc  uart.o
  0x0001F936  sdmmchost_set_cardclk_delay_chain
                                                122      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0001F994  hpm_csr_get_core_mcycle           122      2  Init  Lc  hpm_sdmmc_osal.o
  0x0001FA0E  hpm_csr_get_core_mcycle           122      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001FA88  SaveINSData                       122      2  Init  Gb  kalman.o
  0x0001FB02  KalPredict                        122      2  Init  Gb  kalman.o
  0x0001FB76  sdxc_perform_auto_tuning          118      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001FBD0  sd_all_send_cid                   118      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001FC42  SendVersionInfo                   118      2  Init  Gb  datado.o
  0x0001FCB0  sdmmc_select_card                 116      2  Init  Gb  hpm_sdmmc_common.c.o
  0x0001FD20  sd_error_recovery                 116      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001FD90  ComputeXkk_1                      116      2  Init  Gb  kalman.o
  0x0001FE04  gptmr_channel_get_default_config
                                                114      2  Init  Gb  hpm_gptmr_drv.c.o
  0x0001FE76  fill_last_frag                    114      2  Init  Lc  ff.c.o
  0x0001FEE4  sdxc_reset                        112      2  Init  Gb  hpm_sdxc_drv.c.o
  0x0001FF54  sd_send_if_cond                   112      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0001FFC0  UpdateStop_SetEnd                 112      2  Init  Gb  SetParaBao.o
  0x0002002C  UpdateStart_SetEnd                112      2  Init  Gb  SetParaBao.o
  0x00020098  UpdateSend_SetEnd                 112      2  Init  Gb  SetParaBao.o
  0x00020104  UpdateEnd_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x00020170  SendPara_SetEnd                   112      2  Init  Gb  SetParaBao.o
  0x000201DC  ReadPara4_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x00020248  ReadPara3_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x000202B4  ReadPara2_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x00020320  ReadPara1_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x0002038C  ReadPara0_SetEnd                  112      2  Init  Gb  SetParaBao.o
  0x000203F8  xdir_sum                          110      2  Init  Lc  ff.c.o
  0x00020466  validate                          110      2  Init  Lc  ff.c.o
  0x000204D0  sd_app_cmd_send_cond_op           108      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00020534  f_close                           108      2  Init  Gb  ff.c.o
  0x0002058E  disk_ioctl                        108      2  Init  Gb  diskio.c.o
  0x000205F6  ComputeVib0                       108      2  Init  Gb  align.o
  0x0002065E  sd_app_cmd_set_write_block_erase_count
                                                106      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x000206BE  SDUartIrqInit                     106      2  Init  Gb  uart.o
  0x00020724  move_window                       104      2  Init  Lc  ff.c.o
  0x00020782  fill_first_frag                   104      2  Init  Lc  ff.c.o
  0x000207E6  st_dword                          102      2  Init  Lc  ff.c.o
  0x0002084C  sd_send_card_status               102      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x000208AE  myget_16bit_D64                   102      2  Init  Gb  readpaoche.o
  0x00020914  disk_write                        102      2  Init  Gb  diskio.c.o
  0x00020976  disk_sync_read                    102      2  Init  Gb  diskio.c.o
  0x000209D6  disk_read                         102      2  Init  Gb  diskio.c.o
  0x00020A38  disk_async_write                  102      2  Init  Gb  diskio.c.o
  0x00020A9A  Read_And_Check_GNSS_Data          102      2  Init  Gb  read_and_check_gnss_data.o
  0x00020AF8  BindDefaultSet_by_GNSS            102      2  Init  Gb  navi.o
  0x00020B5E  ComputePkk_1_Step1                100      2  Init  Gb  kalman.o
  0x00020BBA  sd_disk_status                     98      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x00020C14  sd_be2le                           98      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00020C76  init_alloc_info                    98      2  Init  Lc  ff.c.o
  0x00020CD0  sdxc_select_voltage                96      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00020D30  sdmmc_send_application_command
                                                 96      2  Init  Gb  hpm_sdmmc_common.c.o
  0x00020D8C  sd_send_rca                        96      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x00020DE6  core_local_mem_to_sys_address
                                                 96      2  Init  Lc  uart_dma.o
  0x00020E46  core_local_mem_to_sys_address
                                                 96      2  Init  Lc  hpm_sdmmc_disk.c.o
  0x00020EA6  core_local_mem_to_sys_address
                                                 96      2  Init  Lc  hpm_sdmmc_port.c.o
  0x00020F06  sdxc_set_data_bus_width            92      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00020F5E  sdmmchost_enable_sdio_interrupt
                                                 92      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00020FAE  fpgadata_Predo_chen_algParmCash
                                                 92      2  Init  Gb  InsTestingEntry.o
  0x0002100A  sd_disk_sync_read                  90      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x0002105C  sd_check_card_parameters           90      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x000210B2  ComputeWenn                        90      2  Init  Gb  navi.o
  0x0002110C  ff_queue_init                      88      2  Init  Gb  ff_queue.o
  0x00021160  ComputeWien                        88      2  Init  Gb  navi.o
  0x000211B8  uart_default_config                86      2  Init  Gb  hpm_uart_drv.c.o
  0x0002120E  sd_host_init                       86      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00021260  UpdateAlignPosAndVn                86      2  Init  Gb  dynamic_align.o
  0x000212B6  set_pwm_waveform_edge_aligned_duty
                                                 84      2  Init  Lc  timer.o
  0x00021302  sdxc_set_speed_mode                84      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00021356  sdmmc_set_block_size               84      2  Init  Gb  hpm_sdmmc_common.c.o
  0x000213A6  gpiom_set_pin_controller           84      2  Init  Lc  bsp_gpio.o
  0x000213FA  gpiom_set_pin_controller           84      2  Init  Lc  main.o
  0x0002144E  ReadFileOpenFromSd                 84      2  Init  Gb  sd_fatfs.o
  0x00021492  AlgorithmDo                        84      2  Init  Gb  InsTestingEntry.o
  0x000214D2  sdmmc_go_idle_state                82      2  Init  Gb  hpm_sdmmc_common.c.o
  0x00021520  sd_send_cmd                        82      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x0002156A  gpio_write_pin                     82      2  Init  Lc  INS_Init.o
  0x000215BC  Mat_Tr                             82      2  Init  Gb  matvecmath.o
  0x0002160E  sdxc_enable_inverse_clock          80      2  Init  Lc  hpm_sdxc_drv.c.o
  0x0002165E  sdxc_enable_inverse_clock          80      2  Init  Lc  hpm_sdmmc_host.c.o
  0x000216AE  sdmmchost_wait_xfer_done           80      2  Init  Lc  hpm_sdmmc_host.c.o
  0x000216F2  sdmmchost_wait_idle                80      2  Init  Lc  hpm_sdmmc_host.c.o
  0x0002173A  sdmmchost_wait_command_done
                                                 80      2  Init  Lc  hpm_sdmmc_host.c.o
  0x0002177E  sdmmchost_get_data_pin_level
                                                 80      2  Init  Gb  hpm_sdmmc_host.c.o
  0x000217C2  uart_send_byte                     78      2  Init  Gb  hpm_uart_drv.c.o
  0x00021810  st_clust                           78      2  Init  Lc  ff.c.o
  0x00021852  norflash_init                      78      2  Init  Gb  flash.o
  0x00021898  GNSS_Lost_Time                     78      2  Init  Gb  InsTestingEntry.o
  0x000218E6  sum_sfn                            76      2  Init  Lc  ff.c.o
  0x00021932  sdmmchost_select_voltage           76      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0002197A  sd_switch_voltage                  76      2  Init  Lc  hpm_sdmmc_sd.c.o
  0x000219C0  xor_check                          74      2  Init  Gb  frame_analysis.o
  0x00021A0A  uart_tx_dma                        74      2  Init  Lc  uart_dma.o
  0x00021A4C  putc_flush                         74      2  Init  Lc  ff.c.o
  0x00021A92  gptmr_channel_reset_count          74      2  Init  Lc  timer.o
  0x00021ADC  fpgadata_Predo                     74      2  Init  Gb  InsTestingEntry.o
  0x00021B04  ComputeLeverArmVn                  74      2  Init  Gb  navi.o
  0x00021B40  ld_clust                           72      2  Init  Lc  ff.c.o
  0x00021B80  fpgadata_Predo_chen                72      2  Init  Gb  InsTestingEntry.o
  0x00021BAC  disk_status                        70      2  Init  Gb  diskio.c.o
  0x00021BEE  disk_initialize                    70      2  Init  Gb  diskio.c.o
  0x00021C30  sdxc_set_cardclk_delay_chain
                                                 68      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00021C74  sdxc_enable_sd_clock               68      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00021CB8  sdxc_enable_sd_clock               68      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00021CFC  sd_select_card                     68      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00021D3C  sd_disk_async_write                68      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x00021D7C  ld_dword                           68      2  Init  Lc  ff.c.o
  0x00021DC0  dbc_1st                            68      2  Init  Lc  ff.c.o
  0x00021E04  comm_param_setbits                 68      2  Init  Gb  computerFrameParse.o
  0x00021E48  TransHeading0to360                 68      2  Init  Gb  navi.o
  0x00021E8C  GNSS_Last_TIME                     68      2  Init  Gb  InsTestingEntry.o
  0x00021ED0  st_word                            66      2  Init  Lc  ff.c.o
  0x00021F12  sdxc_tuning_error_recovery
                                                 66      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00021F48  gptmr_update_cmp                   66      2  Init  Lc  timer.o
  0x00021F8A  uart_flush                         64      2  Init  Gb  hpm_uart_drv.c.o
  0x00021FCA  ff_queue_is_full                   64      2  Init  Gb  ff_queue.o
  0x0002200A  CorrectVn                          64      2  Init  Gb  kalman.o
  0x0002204A  sdmmchost_set_card_bus_width
                                                 62      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00022080  sdmmchost_check_host_availability
                                                 62      2  Init  Lc  hpm_sdmmc_host.c.o
  0x000220BA  Drv_FlashWrite                     62      2  Init  Gb  FirmwareUpdateFile.o
  0x000220F2  uart_modem_config                  60      2  Init  Lc  hpm_uart_drv.c.o
  0x0002212E  sdxc_wait_card_active              60      2  Init  Lc  hpm_sdmmc_host.c.o
  0x0002216A  sdmmc_get_card_and_aligned_buf_info
                                                 60      2  Init  Lc  hpm_sdmmc_disk.c.o
  0x000221A6  sd_disk_write                      60      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x000221DE  sd_disk_read                       60      2  Init  Gb  hpm_sdmmc_disk.c.o
  0x00022216  rom_xpi_nor_read                   60      2  Init  Lc  flash.o
  0x00022252  dmamux_config                      60      2  Init  Lc  uart_dma.o
  0x0002228E  dma_default_channel_config
                                                 60      2  Init  Gb  hpm_dma_drv.c.o
  0x000222CA  DeviceInit                         60      2  Init  Gb  datado.o
  0x000222F6  sdxc_stop_clock_during_phase_code_change
                                                 58      2  Init  Lc  hpm_sdmmc_common.c.o
  0x00022330  norflash_write                     58      2  Init  Gb  flash.o
  0x00022362  norflash_read                      58      2  Init  Gb  flash.o
  0x00022392  ld_word                            58      2  Init  Lc  ff.c.o
  0x000223CC  uart_dma_output                    56      2  Init  Gb  uart_dma.o
  0x00022400  sdxc_send_command                  56      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00022430  sdxc_enable_auto_tuning            56      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022468  sdmmchost_set_speed_mode           56      2  Init  Gb  hpm_sdmmc_host.c.o
  0x0002249C  myget_16bit_D32                    56      2  Init  Gb  readpaoche.o
  0x000224D4  crc_verify_8bit                    56      2  Init  Gb  app_tool.o
  0x0002250C  app_accum_verify_8bit              56      2  Init  Gb  app_tool.o
  0x00022544  ComputeLeverArmSn                  56      2  Init  Gb  navi.o
  0x00022574  sdxc_set_post_change_delay
                                                 54      2  Init  Lc  hpm_sdmmc_common.c.o
  0x000225AA  sdxc_is_inverse_clock_enabled
                                                 54      2  Init  Lc  hpm_sdxc_drv.c.o
  0x000225E0  sdxc_is_inverse_clock_enabled
                                                 54      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022616  sdxc_enable_interrupt_status
                                                 54      2  Init  Lc  hpm_sdxc_drv.c.o
  0x0002264C  sdxc_enable_interrupt_status
                                                 54      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022682  sdxc_enable_interrupt_signal
                                                 54      2  Init  Lc  hpm_sdmmc_host.c.o
  0x000226B8  myget_16bit_I32                    54      2  Init  Gb  readpaoche.o
  0x000226EE  f_chdrive                          54      2  Init  Gb  ff.c.o
  0x0002271C  clst2sect                          54      2  Init  Lc  ff.c.o
  0x00022752  Drv_FlashErase                     54      2  Init  Gb  FirmwareUpdateFile.o
  0x00022782  sdxc_get_data_bus_width            52      2  Init  Gb  hpm_sdxc_drv.c.o
  0x000227B6  sdmmchost_set_card_clock           52      2  Init  Gb  hpm_sdmmc_host.c.o
  0x000227EA  norflash_erase_sector              50      2  Init  Gb  flash.o
  0x00022814  Uart_SendMsg                       50      2  Init  Gb  bsp_fmc.o
  0x00022840  sdxc_enable_tm_clock               48      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022870  sdxc_enable_power                  48      2  Init  Lc  hpm_sdmmc_common.c.o
  0x000228A0  sdxc_enable_power                  48      2  Init  Lc  hpm_sdmmc_host.c.o
  0x000228D0  Pwm_Init                           46      2  Init  Gb  timer.o
  0x000228F6  gptmr_stop_counter                 44      2  Init  Lc  timer.o
  0x00022922  gptmr_start_counter                44      2  Init  Lc  timer.o
  0x0002294E  gpio_set_pin_output                44      2  Init  Lc  bsp_gpio.o
  0x0002297A  gpio_set_pin_input                 44      2  Init  Lc  main.o
  0x000229A6  gpio_enable_pin_interrupt          44      2  Init  Lc  main.o
  0x000229D2  gpio_clear_pin_interrupt_flag
                                                 44      2  Init  Lc  main.o
  0x000229FE  get_fpgadata                       44      2  Init  Gb  fpgad.o
  0x00022A18  SdFileWriteOperate                 44      2  Init  Gb  sd_fatfs.o
  0x00022A38  Exti_Init                          44      2  Init  Gb  main.o
  0x00022A5A  sd_set_max_current                 42      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00022A80  gpio_toggle_pin                    42      2  Init  Lc  main.o
  0x00022AAA  ff_queue_is_empty                  42      2  Init  Gb  ff_queue.o
  0x00022AD4  uart_get_current_recv_remaining_size
                                                 40      2  Init  Lc  uart_dma.o
  0x00022AF6  sdmmchost_wait_card_active
                                                 40      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00022B14  hpm_sdmmc_osal_event_create
                                                 40      2  Init  Wk  hpm_sdmmc_osal.o
  0x00022B3C  putc_init                          38      2  Init  Lc  ff.c.o
  0x00022B62  myget_8bit_I16                     38      2  Init  Gb  readpaoche.o
  0x00022B88  hpm_sdmmc_osal_delay               38      2  Init  Wk  hpm_sdmmc_osal.o
  0x00022BAE  ff_handle_poll                     38      2  Init  Gb  ff_queue.o
  0x00022BCC  xsum32                             36      2  Init  Lc  ff.c.o
  0x00022BF0  sdxc_get_default_cardclk_delay_chain
                                                 36      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022C14  sdmmc_get_sys_addr                 36      2  Init  Gb  hpm_sdmmc_port.c.o
  0x00022C34  gptmr_check_status                 36      2  Init  Lc  timer.o
  0x00022C58  sdmmchost_switch_to_3v3_as_needed
                                                 34      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022C76  sdmmchost_register_xfer_complete_callback
                                                 34      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00022C98  sdmmchost_error_recovery           34      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00022CB6  output_normal_do                   34      2  Init  Gb  INS_Output.o
  0x00022CCE  init_gpio                          34      2  Init  Gb  main.o
  0x00022CF0  EXTI3_IRQHandler                   34      2  Init  Gb  gd32f4xx_it.o
  0x00022D06  timer_Init                         32      2  Init  Gb  timer.o
  0x00022D22  Drv_FlashRead                      32      2  Init  Gb  FirmwareUpdateFile.o
  0x00022D3E  sdxc_is_bus_idle                   30      2  Init  Gb  hpm_sdxc_drv.c.o
  0x00022D5C  sd_is_card_present                 30      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x00022D76  loopDoOther                        30      2  Init  Gb  datado.o
  0x00022D8C  hpm_sdmmc_osal_event_clear
                                                 30      2  Init  Wk  hpm_sdmmc_osal.o
  0x00022DAA  uart4sendmsg                       28      2  Init  Gb  gd32f4xx_it.o
  0x00022DC2  sdmmchost_is_voltage_switch_supported
                                                 28      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00022DDE  sdmmchost_delay_ms                 28      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00022DF4  gptmr_enable_irq                   28      2  Init  Lc  timer.o
  0x00022E10  gd_eval_com_init                   28      2  Init  Gb  INS_Init.o
  0x00022E24  bsp_tim_init                       28      2  Init  Gb  bsp_tim.o
  0x00022E34  Led_Control                        28      2  Init  Gb  main.o
  0x00022E4A  sdxc_reset_tuning_engine           26      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022E64  sdxc_is_card_inserted              26      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022E7E  hpm_sdmmc_osal_event_set           26      2  Init  Wk  hpm_sdmmc_osal.o
  0x00022E98  Drv_SystemReset                    26      2  Init  Gb  FirmwareUpdateFile.o
  0x00022EAC  sdxc_execute_tuning                24      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022EC4  sdmmchost_is_8bit_supported
                                                 24      2  Init  Gb  hpm_sdmmc_host.c.o
  0x00022EDC  protocol_send                      24      2  Init  Gb  computerFrameParse.o
  0x00022EF0  dma_get_remaining_transfer_size
                                                 24      2  Init  Lc  uart_dma.o
  0x00022F08  delay_ms                           24      2  Init  Gb  systick.o
  0x00022F20  Algorithm_before_otherDataDo
                                                 24      2  Init  Gb  datado.o
  0x00022F34  ACC_gyroreset_r_TAFEAG16_buf
                                                 24      2  Init  Gb  InsTestingEntry.o
  0x00022F48  get_uart_tx_idle                   22      2  Init  Gb  uart_dma.o
  0x00022F5E  gptmr_clear_status                 20      2  Init  Lc  timer.o
  0x00022F72  sdxc_get_data7_4_level             18      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022F84  sdxc_get_data3_0_level             18      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022F96  sdxc_clear_interrupt_status
                                                 18      2  Init  Lc  hpm_sdxc_drv.c.o
  0x00022FA8  sdxc_clear_interrupt_status
                                                 18      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00022FBA  ppor_sw_reset                      18      2  Init  Lc  FirmwareUpdateFile.o
  0x00022FCC  mchtmr_get_count                   18      2  Init  Lc  sd_fatfs.o
  0x00022FDE  sdxc_select_cardclk_delay_source
                                                 16      2  Init  Lc  hpm_sdmmc_common.c.o
  0x00022FEE  comm_read_currentFreq              16      2  Init  Gb  computerFrameParse.o
  0x00022FFE  comm_axis_read                     16      2  Init  Gb  computerFrameParse.o
  0x0002300E  sdxc_get_present_status            14      2  Init  Lc  hpm_sdxc_drv.c.o
  0x0002301C  sdxc_get_interrupt_status          14      2  Init  Lc  hpm_sdxc_drv.c.o
  0x0002302A  sdxc_get_interrupt_status          14      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00023038  sdxc_get_interrupt_signal          14      2  Init  Lc  hpm_sdmmc_host.c.o
  0x00023046  sdxc_is_ddr50_supported            12      2  Init  Lc  hpm_sdmmc_port.c.o
  0x00023052  sd_deinit                          10      2  Init  Gb  hpm_sdmmc_sd.c.o
  0x0002305C  get_fpgadata_before                 4      2  Init  Gb  fpgad.o
  0x00023060  comm_nav_para_syn                   4      2  Init  Gb  computerFrameParse.o

RAM function symbols by descending size:

  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  fmc2sinsraw                     7 230      2  Init  Gb  readpaoche.o
  AnalyticCoordinateAxis          6 598      2  Init  Gb  InsTestingEntry.o
  f_mkfs                          4 942      2  Init  Gb  ff.c.o
  frame_pack_and_send             4 480      2  Init  Gb  frame_analysis.o
  output_gdw_do                   3 824      2  Init  Gb  INS_Output.o
  AlgorithmAct                    2 318      2  Init  Gb  InsTestingEntry.o
  ComputeFn                       2 166      2  Init  Gb  kalman.o
  mount_volume                    2 016      2  Init  Lc  ff.c.o
  FPGATo422_00BB_send             1 612      2  Init  Gb  InsTestingEntry.o
  ReadFileToSd                    1 540      2  Init  Gb  sd_fatfs.o
  ErrStore_1s                     1 514      2  Init  Gb  kalman.o
  Kalman_StartUp                  1 354      2  Init  Gb  kalman.o
  ComputeZk                       1 350      2  Init  Gb  kalman.o
  sd_decode_csd                   1 332      2  Init  Lc  hpm_sdmmc_sd.c.o
  create_name                     1 308      2  Init  Lc  ff.c.o
  f_lseek                         1 278      2  Init  Gb  ff.c.o
  f_open                          1 250      2  Init  Gb  ff.c.o
  ErrCorrect_1_Navi_Time          1 222      2  Init  Gb  kalman.o
  INS912_Output                   1 194      2  Init  Gb  INS_Output.o
  f_printf                        1 152      2  Init  Gb  ff.c.o
  SetParaAll                      1 140      2  Init  Gb  SetParaBao.o
  output_fpga_void                1 128      2  Init  Gb  INS_Output.o
  dir_register                    1 056      2  Init  Lc  ff.c.o
  UartDmaRecSetPara               1 054      2  Init  Gb  SetParaBao.o
  f_async_write                     976      2  Init  Gb  ff.c.o
  f_write                           976      2  Init  Gb  ff.c.o
  Rk_Init                           974      2  Init  Gb  kalman.o
  fpgadata_Predo_chen_SetAlgParm_setRParm_acc
                                    968      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_SetAlgParm_setRParm_gyro
                                    968      2  Init  Gb  InsTestingEntry.o
  analysisRxdata                    890      2  Init  Gb  uart.o
  NaviCompute                       870      2  Init  Gb  navi.o
  f_read                            868      2  Init  Gb  ff.c.o
  InitParaToAlgorithm               786      2  Init  Gb  SetParaBao.o
  sd_card_init                      784      2  Init  Gb  hpm_sdmmc_sd.c.o
  SaveGNSSData                      774      2  Init  Gb  read_and_check_gnss_data.o
  dir_find                          724      2  Init  Lc  ff.c.o
  ReadParaFromFlash                 704      2  Init  Gb  SetParaBao.o
  Mat_Inv                           700      2  Init  Gb  matvecmath.o
  sdmmchost_init_io                 696      2  Init  Gb  hpm_sdmmc_host.c.o
  fpgadata_Predo_chen_SetAlgParm_acc
                                    694      2  Init  Gb  InsTestingEntry.o
  fpgadata_Predo_chen_OutDataSet
                                    686      2  Init  Gb  InsTestingEntry.o
  create_chain                      678      2  Init  Lc  ff.c.o
  f_sync                            670      2  Init  Gb  ff.c.o
  create_partition                  668      2  Init  Lc  ff.c.o
  gnss_check_bind                   666      2  Init  Gb  InsTestingEntry.o
  get_fat                           658      2  Init  Lc  ff.c.o
  CnbToQ                            640      2  Init  Gb  navi.o
  fpgadata_Predo_chen_SetAlgParm_gyro
                                    640      2  Init  Gb  InsTestingEntry.o
  DynamicInertialSysAlignCompute
                                    606      2  Init  Gb  dynamic_align.o
  RestoreFactory                    600      2  Init  Gb  SetParaBao.o
  ComputeSi                         582      2  Init  Gb  dynamic_align.o
  put_fat                           574      2  Init  Lc  ff.c.o
  sdxc_set_adma2_desc               556      2  Init  Gb  hpm_sdxc_drv.c.o
  follow_path                       552      2  Init  Lc  ff.c.o
  dma_setup_channel                 548      2  Init  Gb  hpm_dma_drv.c.o
  remove_chain                      520      2  Init  Lc  ff.c.o
  QToCnb                            518      2  Init  Gb  navi.o
  load_xdir                         516      2  Init  Lc  ff.c.o
  sd_write_blocks                   514      2  Init  Gb  hpm_sdmmc_sd.c.o
  dir_read                          506      2  Init  Lc  ff.c.o
  sd_read_blocks                    490      2  Init  Gb  hpm_sdmmc_sd.c.o
  irq_handler_trap                  488      4  Init  Gb  trap.c.o
  InertialSysAlignCompute           486      2  Init  Gb  align.o
  ComputeKk                         484      2  Init  Gb  kalman.o
  check_fs                          476      2  Init  Lc  ff.c.o
  sd_start_write_blocks             472      2  Init  Gb  hpm_sdmmc_sd.c.o
  GNSSAndHeadDataTest               468      2  Init  Gb  read_and_check_gnss_data.o
  dma_config_linked_descriptor
                                    468      2  Init  Gb  hpm_dma_drv.c.o
  CorrectAtti                       466      2  Init  Gb  kalman.o
  putc_bfd                          458      2  Init  Lc  ff.c.o
  uart_dma_tx_send                  458      2  Init  Lc  uart_dma.o
  mcusendtopcdriversdata            452      2  Init  Gb  gdwatch.o
  SetParaGnssInitValue              448      2  Init  Gb  SetParaBao.o
  sd_decode_status                  444      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdxc_parse_interrupt_status
                                    442      2  Init  Gb  hpm_sdxc_drv.c.o
  ComputeFk                         440      2  Init  Gb  kalman.o
  ComputeQ                          440      2  Init  Gb  navi.o
  sdxc_set_adma3_desc               434      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_transfer_nonblocking         428      2  Init  Gb  hpm_sdxc_drv.c.o
  f_unlink                          418      2  Init  Gb  ff.c.o
  CnbToAtti                         416      2  Init  Gb  navi.o
  ComputeVn                         410      2  Init  Gb  navi.o
  sdmmchost_power_control           408      2  Init  Lc  hpm_sdmmc_host.c.o
  SysInit                           404      2  Init  Gb  navi.o
  ParaUpdateHandle                  400      2  Init  Gb  SetParaBao.o
  SetParaUpdateStart                400      2  Init  Gb  SetParaBao.o
  ld_qword                          400      2  Init  Lc  ff.c.o
  uart_init                         396      2  Init  Gb  hpm_uart_drv.c.o
  SetParaFactorAcc                  392      2  Init  Gb  SetParaBao.o
  SetParaFactorGyro                 392      2  Init  Gb  SetParaBao.o
  uart_dma_init                     386      2  Init  Gb  uart_dma.o
  uart_dma_recv_polling             386      2  Init  Gb  uart_dma.o
  initializationdriversettings
                                    384      2  Init  Gb  gdwatch.o
  output_fpgatxt_do                 382      2  Init  Gb  INS_Output.o
  Hk_Init                           380      2  Init  Gb  kalman.o
  WriteBB00FileToSd                 380      2  Init  Gb  sd_fatfs.o
  bsp_gpio_init                     380      2  Init  Gb  bsp_gpio.o
  gen_numname                       376      2  Init  Lc  ff.c.o
  board_init_sd_host_params         370      2  Init  Wk  hpm_sdmmc_port.c.o
  ff_wtoupper                       370      2  Init  Gb  ffunicode.c.o
  ReadPara_4                        368      2  Init  Gb  SetParaBao.o
  sdmmchost_transfer                368      2  Init  Gb  hpm_sdmmc_host.c.o
  KalCompute                        364      2  Init  Gb  kalman.o
  sd_probe_bus_voltage              362      2  Init  Lc  hpm_sdmmc_sd.c.o
  uart_calculate_baudrate           354      2  Init  Lc  hpm_uart_drv.c.o
  dir_next                          352      2  Init  Lc  ff.c.o
  SetParaBaud                       350      2  Init  Gb  SetParaBao.o
  main                              342      2  Init  Gb  main.o
  SetParaUpdateSend                 338      2  Init  Gb  SetParaBao.o
  sdxc_error_recovery               330      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_prepare_cmd_xfer             330      2  Init  Lc  hpm_sdxc_drv.c.o
  fpgadata_Predo_chen_preAlgParm_370
                                    326      2  Init  Gb  InsTestingEntry.o
  Qua_Mul                           324      2  Init  Gb  matvecmath.o
  ReadPara_2                        322      2  Init  Gb  SetParaBao.o
  gptmr_channel_config              322      2  Init  Gb  hpm_gptmr_drv.c.o
  st_qword                          322      2  Init  Lc  ff.c.o
  SetParaAngle                      320      2  Init  Gb  SetParaBao.o
  SetParaDeviation                  320      2  Init  Gb  SetParaBao.o
  SetParaGnss                       320      2  Init  Gb  SetParaBao.o
  SetParaVector                     320      2  Init  Gb  SetParaBao.o
  sd_switch_function                320      2  Init  Lc  hpm_sdmmc_sd.c.o
  AttiToCnb                         318      2  Init  Gb  navi.o
  SaveParaToFlash                   310      2  Init  Gb  SetParaBao.o
  SetParaUpdateEnd                  310      2  Init  Gb  SetParaBao.o
  ComputeVibn                       308      2  Init  Gb  navi.o
  sd_disk_initialize                306      2  Init  Gb  hpm_sdmmc_disk.c.o
  ComputePos                        302      2  Init  Gb  navi.o
  fpgadata_syn_count_do             296      2  Init  Gb  fpgad.o
  show_error_string                 296      2  Init  Gb  sd_fatfs.o
  sync_fs                           296      2  Init  Lc  ff.c.o
  sd_polling_card_status_busy
                                    290      2  Init  Gb  hpm_sdmmc_sd.c.o
  SetParaCoord                      286      2  Init  Gb  SetParaBao.o
  create_xdir                       282      2  Init  Lc  ff.c.o
  sd_set_bus_timing                 282      2  Init  Lc  hpm_sdmmc_sd.c.o
  SetParaFrequency                  280      2  Init  Gb  SetParaBao.o
  dir_sdi                           280      2  Init  Lc  ff.c.o
  get_fpgadata_do                   280      2  Init  Gb  fpgad.o
  tchar2uni                         280      2  Init  Lc  ff.c.o
  SetParaCalibration                278      2  Init  Gb  SetParaBao.o
  Fatfs_Init                        276      2  Init  Gb  sd_fatfs.o
  ReadPara_1                        276      2  Init  Gb  SetParaBao.o
  SetParaSdHandle                   276      2  Init  Gb  SetParaBao.o
  ComputeAttiRate                   270      2  Init  Gb  navi.o
  ReadPara                          268      2  Init  Gb  SetParaBao.o
  get_boot_reason                   268      2  Init  Gb  main.o
  DynamicNavi_Init                  266      2  Init  Gb  navi.o
  ReadPara_0                        266      2  Init  Gb  SetParaBao.o
  ReadPara_3                        266      2  Init  Gb  SetParaBao.o
  cmp_lfn                           264      2  Init  Lc  ff.c.o
  SetParaDataOutType                258      2  Init  Gb  SetParaBao.o
  TIMER2_IRQHandler                 258      2  Init  Gb  gd32f4xx_it.o
  SetParaTime                       256      2  Init  Gb  SetParaBao.o
  caninfupdate                      254      2  Init  Gb  INS_Data.o
  SetParaFilter                     250      2  Init  Gb  SetParaBao.o
  SetParaKalmanQ                    250      2  Init  Gb  SetParaBao.o
  SetParaKalmanR                    250      2  Init  Gb  SetParaBao.o
  sd_read_status                    250      2  Init  Gb  hpm_sdmmc_sd.c.o
  sdmmchost_start_transfer          250      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_vsel_pin_control
                                    250      2  Init  Gb  hpm_sdmmc_host.c.o
  INS912AlgorithmEntry              248      2  Init  Gb  InsTestingEntry.o
  default_isr_111                   248      4  Init  Gb  hpm_sdmmc_disk.c.o
  default_isr_63                    248      4  Init  Gb  timer.o
  put_lfn                           248      2  Init  Lc  ff.c.o
  Pk_Init                           246      2  Init  Gb  kalman.o
  Qk_Init                           246      2  Init  Gb  kalman.o
  default_isr_2                     246      4  Init  Gb  main.o
  SetParaDebugMode                  240      2  Init  Gb  SetParaBao.o
  SetParaGpsType                    240      2  Init  Gb  SetParaBao.o
  SetParaGyroType                   240      2  Init  Gb  SetParaBao.o
  pick_lfn                          240      2  Init  Lc  ff.c.o
  sdmmc_card_async_write            238      2  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmchost_init                    238      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_irq_handler             238      2  Init  Gb  hpm_sdmmc_host.c.o
  sdxc_error_recovery_first_half
                                    238      2  Init  Lc  hpm_sdxc_drv.c.o
  FinishDynamicInertialSysAlign
                                    236      2  Init  Gb  dynamic_align.o
  FinishInertialSysAlign            236      2  Init  Gb  align.o
  SetParaColliery_Operate           236      2  Init  Gb  SetParaBao.o
  find_bitmap                       236      2  Init  Lc  ff.c.o
  timer_config                      236      2  Init  Lc  timer.o
  sdmmchost_switch_to_1v8           234      2  Init  Gb  hpm_sdmmc_host.c.o
  ComputeCie                        232      2  Init  Gb  align.o
  SetParaUpdateStop                 232      2  Init  Gb  SetParaBao.o
  hpm_sdmmc_osal_event_wait         232      2  Init  Wk  hpm_sdmmc_osal.o
  GNSS_Valid_PPSStart               230      2  Init  Gb  InsTestingEntry.o
  sd_send_scr                       230      2  Init  Lc  hpm_sdmmc_sd.c.o
  store_xdir                        228      2  Init  Lc  ff.c.o
  Virtual_PPS_insert_5hz            226      2  Init  Gb  InsTestingEntry.o
  change_bitmap                     224      2  Init  Lc  ff.c.o
  dir_alloc                         222      2  Init  Lc  ff.c.o
  sdmmc_card_read                   220      2  Init  Lc  hpm_sdmmc_disk.c.o
  sdmmc_card_write                  220      2  Init  Lc  hpm_sdmmc_disk.c.o
  Kalman_Init                       218      2  Init  Gb  kalman.o
  Navi_Init                         218      2  Init  Gb  navi.o
  ComputeCib0i                      216      2  Init  Gb  align.o
  ComputePk                         216      2  Init  Gb  kalman.o
  WriteFileOpenFromSd               216      2  Init  Gb  sd_fatfs.o
  sdxc_perform_tuning_flow_sequence
                                    216      2  Init  Gb  hpm_sdxc_drv.c.o
  sdxc_receive_cmd_response         216      2  Init  Gb  hpm_sdxc_drv.c.o
  dir_remove                        214      2  Init  Lc  ff.c.o
  extract_csd_field                 214      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmchost_send_command            210      2  Init  Gb  hpm_sdmmc_host.c.o
  uart_rx_dma_autorun               210      2  Init  Lc  uart_dma.o
  ComputeDelSenbb                   208      2  Init  Gb  navi.o
  ff_queue_push                     206      2  Init  Gb  ff_queue.o
  find_volume                       206      2  Init  Lc  ff.c.o
  sdxc_init                         206      2  Init  Gb  hpm_sdxc_drv.c.o
  test_gpio_input_interrupt         206      2  Init  Gb  main.o
  ComputeXk                         204      2  Init  Gb  kalman.o
  LEDIndicator                      204      2  Init  Gb  INS_Init.o
  ff_uni2oem                        198      2  Init  Gb  ffunicode.c.o
  ComputeG                          196      2  Init  Gb  navi.o
  SdFileOperateTypeSet              196      2  Init  Gb  sd_fatfs.o
  sd_init                           196      2  Init  Gb  hpm_sdmmc_sd.c.o
  ComputeWnbb                       192      2  Init  Gb  navi.o
  ComputeCen                        188      2  Init  Gb  align.o
  ComputeVi                         186      2  Init  Gb  align.o
  ComputeSib0                       178      2  Init  Gb  dynamic_align.o
  pnavout_set                       178      2  Init  Gb  InsTestingEntry.o
  DeleteFileFromSd                  172      2  Init  Gb  sd_fatfs.o
  WriteFileToSd                     172      2  Init  Gb  sd_fatfs.o
  get_ldnumber                      170      2  Init  Lc  ff.c.o
  WriteCOM3FileToSd                 166      2  Init  Gb  sd_fatfs.o
  sdxc_set_data_timeout             166      2  Init  Gb  hpm_sdxc_drv.c.o
  CloseFileToSd                     164      2  Init  Gb  sd_fatfs.o
  dir_clear                         164      2  Init  Lc  ff.c.o
  sd_transfer                       164      2  Init  Lc  hpm_sdmmc_sd.c.o
  ComputePkk_1_Step2                162      2  Init  Gb  kalman.o
  Mat_Mul                           162      2  Init  Gb  matvecmath.o
  sd_convert_data_endian            162      2  Init  Lc  hpm_sdmmc_sd.c.o
  DynamicInertialSysAlign_Init
                                    160      2  Init  Gb  dynamic_align.o
  sd_disk_ioctl                     160      2  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmchost_is_card_detected
                                    160      2  Init  Gb  hpm_sdmmc_host.c.o
  xname_sum                         160      2  Init  Lc  ff.c.o
  UartIrqSendMsg                    158      2  Init  Gb  uart.o
  ComputeRmRn                       156      2  Init  Gb  navi.o
  InertialSysAlign_Init             156      2  Init  Gb  align.o
  SysVarDefaultSet                  156      2  Init  Gb  navi.o
  sdxc_set_adma_table_config
                                    156      2  Init  Gb  hpm_sdxc_drv.c.o
  get_fpgadata_after_otherDataDo
                                    154      2  Init  Gb  fpgad.o
  sd_decode_scr                     154      2  Init  Lc  hpm_sdmmc_sd.c.o
  ReturnColliery_Operate            152      2  Init  Gb  main.o
  ff_queue_get_buffer_by_sector
                                    152      2  Init  Gb  ff_queue.o
  f_mount                           144      2  Init  Gb  ff.c.o
  ff_queue_poll                     144      2  Init  Gb  ff_queue.o
  sd_set_bus_width                  144      2  Init  Lc  hpm_sdmmc_sd.c.o
  sync_window                       142      2  Init  Lc  ff.c.o
  ReadPara0_SetHead                 140      2  Init  Gb  SetParaBao.o
  ReadPara1_SetHead                 140      2  Init  Gb  SetParaBao.o
  ReadPara2_SetHead                 140      2  Init  Gb  SetParaBao.o
  ReadPara3_SetHead                 140      2  Init  Gb  SetParaBao.o
  ReadPara4_SetHead                 140      2  Init  Gb  SetParaBao.o
  SendPara_SetHead                  140      2  Init  Gb  SetParaBao.o
  UpdateEnd_SetHead                 140      2  Init  Gb  SetParaBao.o
  UpdateSend_SetHead                140      2  Init  Gb  SetParaBao.o
  UpdateStart_SetHead               140      2  Init  Gb  SetParaBao.o
  UpdateStop_SetHead                140      2  Init  Gb  SetParaBao.o
  set_pwm_waveform_edge_aligned_frequency
                                    140      2  Init  Lc  timer.o
  sdxc_transfer_cb                  138      2  Init  Lc  ff_queue.o
  INS_Init                          136      2  Init  Gb  INS_Init.o
  UartIrqInit                       136      2  Init  Gb  uart.o
  WriteBB00FileOpenFromSd           134      2  Init  Lc  sd_fatfs.o
  WriteCom3FileOpenFromSd           134      2  Init  Lc  sd_fatfs.o
  sduart_recv_polling               132      2  Init  Gb  uart.o
  sdxc_set_transfer_config          132      2  Init  Lc  hpm_sdxc_drv.c.o
  SdFileReadOperate                 130      2  Init  Gb  sd_fatfs.o
  Vec_Cross                         130      2  Init  Gb  matvecmath.o
  load_obj_xdir                     130      2  Init  Lc  ff.c.o
  ReadBB00FileOpenFromSd            128      2  Init  Lc  sd_fatfs.o
  ReadCom3FileOpenFromSd            128      2  Init  Lc  sd_fatfs.o
  sd_mount_fs                       128      2  Init  Lc  sd_fatfs.o
  sd_send_csd                       128      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmc_enable_auto_tuning          128      2  Init  Gb  hpm_sdmmc_common.c.o
  sd_mkfs                           126      2  Init  Lc  sd_fatfs.o
  sdxc_set_dma_config               126      2  Init  Gb  hpm_sdxc_drv.c.o
  ComputeDeg_Ex                     124      2  Init  Gb  navi.o
  KalPredict                        122      2  Init  Gb  kalman.o
  SaveINSData                       122      2  Init  Gb  kalman.o
  hpm_csr_get_core_mcycle           122      2  Init  Lc  hpm_sdmmc_osal.o
  hpm_csr_get_core_mcycle           122      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmchost_set_cardclk_delay_chain
                                    122      2  Init  Gb  hpm_sdmmc_host.c.o
  test_uart_recv_polling            122      2  Init  Lc  uart.o
  SendVersionInfo                   118      2  Init  Gb  datado.o
  sd_all_send_cid                   118      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdxc_perform_auto_tuning          118      2  Init  Gb  hpm_sdxc_drv.c.o
  ComputeXkk_1                      116      2  Init  Gb  kalman.o
  sd_error_recovery                 116      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmc_select_card                 116      2  Init  Gb  hpm_sdmmc_common.c.o
  fill_last_frag                    114      2  Init  Lc  ff.c.o
  gptmr_channel_get_default_config
                                    114      2  Init  Gb  hpm_gptmr_drv.c.o
  ReadPara0_SetEnd                  112      2  Init  Gb  SetParaBao.o
  ReadPara1_SetEnd                  112      2  Init  Gb  SetParaBao.o
  ReadPara2_SetEnd                  112      2  Init  Gb  SetParaBao.o
  ReadPara3_SetEnd                  112      2  Init  Gb  SetParaBao.o
  ReadPara4_SetEnd                  112      2  Init  Gb  SetParaBao.o
  SendPara_SetEnd                   112      2  Init  Gb  SetParaBao.o
  UpdateEnd_SetEnd                  112      2  Init  Gb  SetParaBao.o
  UpdateSend_SetEnd                 112      2  Init  Gb  SetParaBao.o
  UpdateStart_SetEnd                112      2  Init  Gb  SetParaBao.o
  UpdateStop_SetEnd                 112      2  Init  Gb  SetParaBao.o
  sd_send_if_cond                   112      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdxc_reset                        112      2  Init  Gb  hpm_sdxc_drv.c.o
  validate                          110      2  Init  Lc  ff.c.o
  xdir_sum                          110      2  Init  Lc  ff.c.o
  ComputeVib0                       108      2  Init  Gb  align.o
  disk_ioctl                        108      2  Init  Gb  diskio.c.o
  f_close                           108      2  Init  Gb  ff.c.o
  sd_app_cmd_send_cond_op           108      2  Init  Lc  hpm_sdmmc_sd.c.o
  SDUartIrqInit                     106      2  Init  Gb  uart.o
  sd_app_cmd_set_write_block_erase_count
                                    106      2  Init  Lc  hpm_sdmmc_sd.c.o
  fill_first_frag                   104      2  Init  Lc  ff.c.o
  move_window                       104      2  Init  Lc  ff.c.o
  BindDefaultSet_by_GNSS            102      2  Init  Gb  navi.o
  Read_And_Check_GNSS_Data          102      2  Init  Gb  read_and_check_gnss_data.o
  disk_async_write                  102      2  Init  Gb  diskio.c.o
  disk_read                         102      2  Init  Gb  diskio.c.o
  disk_sync_read                    102      2  Init  Gb  diskio.c.o
  disk_write                        102      2  Init  Gb  diskio.c.o
  myget_16bit_D64                   102      2  Init  Gb  readpaoche.o
  sd_send_card_status               102      2  Init  Lc  hpm_sdmmc_sd.c.o
  st_dword                          102      2  Init  Lc  ff.c.o
  ComputePkk_1_Step1                100      2  Init  Gb  kalman.o
  init_alloc_info                    98      2  Init  Lc  ff.c.o
  sd_be2le                           98      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_disk_status                     98      2  Init  Gb  hpm_sdmmc_disk.c.o
  core_local_mem_to_sys_address
                                     96      2  Init  Lc  uart_dma.o
  core_local_mem_to_sys_address
                                     96      2  Init  Lc  hpm_sdmmc_disk.c.o
  core_local_mem_to_sys_address
                                     96      2  Init  Lc  hpm_sdmmc_port.c.o
  sd_send_rca                        96      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmc_send_application_command
                                     96      2  Init  Gb  hpm_sdmmc_common.c.o
  sdxc_select_voltage                96      2  Init  Gb  hpm_sdxc_drv.c.o
  fpgadata_Predo_chen_algParmCash
                                     92      2  Init  Gb  InsTestingEntry.o
  sdmmchost_enable_sdio_interrupt
                                     92      2  Init  Gb  hpm_sdmmc_host.c.o
  sdxc_set_data_bus_width            92      2  Init  Gb  hpm_sdxc_drv.c.o
  ComputeWenn                        90      2  Init  Gb  navi.o
  sd_check_card_parameters           90      2  Init  Lc  hpm_sdmmc_sd.c.o
  sd_disk_sync_read                  90      2  Init  Gb  hpm_sdmmc_disk.c.o
  ComputeWien                        88      2  Init  Gb  navi.o
  ff_queue_init                      88      2  Init  Gb  ff_queue.o
  UpdateAlignPosAndVn                86      2  Init  Gb  dynamic_align.o
  sd_host_init                       86      2  Init  Gb  hpm_sdmmc_sd.c.o
  uart_default_config                86      2  Init  Gb  hpm_uart_drv.c.o
  AlgorithmDo                        84      2  Init  Gb  InsTestingEntry.o
  ReadFileOpenFromSd                 84      2  Init  Gb  sd_fatfs.o
  gpiom_set_pin_controller           84      2  Init  Lc  bsp_gpio.o
  gpiom_set_pin_controller           84      2  Init  Lc  main.o
  sdmmc_set_block_size               84      2  Init  Gb  hpm_sdmmc_common.c.o
  sdxc_set_speed_mode                84      2  Init  Gb  hpm_sdxc_drv.c.o
  set_pwm_waveform_edge_aligned_duty
                                     84      2  Init  Lc  timer.o
  Mat_Tr                             82      2  Init  Gb  matvecmath.o
  gpio_write_pin                     82      2  Init  Lc  INS_Init.o
  sd_send_cmd                        82      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmc_go_idle_state                82      2  Init  Gb  hpm_sdmmc_common.c.o
  sdmmchost_get_data_pin_level
                                     80      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_wait_command_done
                                     80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_wait_idle                80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_wait_xfer_done           80      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_inverse_clock          80      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_inverse_clock          80      2  Init  Lc  hpm_sdmmc_host.c.o
  GNSS_Lost_Time                     78      2  Init  Gb  InsTestingEntry.o
  norflash_init                      78      2  Init  Gb  flash.o
  st_clust                           78      2  Init  Lc  ff.c.o
  uart_send_byte                     78      2  Init  Gb  hpm_uart_drv.c.o
  sd_switch_voltage                  76      2  Init  Lc  hpm_sdmmc_sd.c.o
  sdmmchost_select_voltage           76      2  Init  Gb  hpm_sdmmc_host.c.o
  sum_sfn                            76      2  Init  Lc  ff.c.o
  ComputeLeverArmVn                  74      2  Init  Gb  navi.o
  fpgadata_Predo                     74      2  Init  Gb  InsTestingEntry.o
  gptmr_channel_reset_count          74      2  Init  Lc  timer.o
  putc_flush                         74      2  Init  Lc  ff.c.o
  uart_tx_dma                        74      2  Init  Lc  uart_dma.o
  xor_check                          74      2  Init  Gb  frame_analysis.o
  fpgadata_Predo_chen                72      2  Init  Gb  InsTestingEntry.o
  ld_clust                           72      2  Init  Lc  ff.c.o
  disk_initialize                    70      2  Init  Gb  diskio.c.o
  disk_status                        70      2  Init  Gb  diskio.c.o
  GNSS_Last_TIME                     68      2  Init  Gb  InsTestingEntry.o
  TransHeading0to360                 68      2  Init  Gb  navi.o
  comm_param_setbits                 68      2  Init  Gb  computerFrameParse.o
  dbc_1st                            68      2  Init  Lc  ff.c.o
  ld_dword                           68      2  Init  Lc  ff.c.o
  sd_disk_async_write                68      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_select_card                     68      2  Init  Gb  hpm_sdmmc_sd.c.o
  sdxc_enable_sd_clock               68      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_sd_clock               68      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_set_cardclk_delay_chain
                                     68      2  Init  Lc  hpm_sdmmc_host.c.o
  gptmr_update_cmp                   66      2  Init  Lc  timer.o
  rom_xpi_nor_program                66      2  Init  Lc  flash.o
  sdxc_tuning_error_recovery
                                     66      2  Init  Lc  hpm_sdxc_drv.c.o
  st_word                            66      2  Init  Lc  ff.c.o
  CorrectVn                          64      2  Init  Gb  kalman.o
  ff_queue_is_full                   64      2  Init  Gb  ff_queue.o
  uart_flush                         64      2  Init  Gb  hpm_uart_drv.c.o
  Drv_FlashWrite                     62      2  Init  Gb  FirmwareUpdateFile.o
  sdmmchost_check_host_availability
                                     62      2  Init  Lc  hpm_sdmmc_host.c.o
  sdmmchost_set_card_bus_width
                                     62      2  Init  Gb  hpm_sdmmc_host.c.o
  DeviceInit                         60      2  Init  Gb  datado.o
  dma_default_channel_config
                                     60      2  Init  Gb  hpm_dma_drv.c.o
  dmamux_config                      60      2  Init  Lc  uart_dma.o
  rom_xpi_nor_read                   60      2  Init  Lc  flash.o
  sd_disk_read                       60      2  Init  Gb  hpm_sdmmc_disk.c.o
  sd_disk_write                      60      2  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmc_get_card_and_aligned_buf_info
                                     60      2  Init  Lc  hpm_sdmmc_disk.c.o
  sdxc_wait_card_active              60      2  Init  Lc  hpm_sdmmc_host.c.o
  uart_modem_config                  60      2  Init  Lc  hpm_uart_drv.c.o
  ld_word                            58      2  Init  Lc  ff.c.o
  norflash_read                      58      2  Init  Gb  flash.o
  norflash_write                     58      2  Init  Gb  flash.o
  rom_xpi_nor_erase_block            58      2  Init  Lc  flash.o
  rom_xpi_nor_erase_sector           58      2  Init  Lc  flash.o
  sdxc_stop_clock_during_phase_code_change
                                     58      2  Init  Lc  hpm_sdmmc_common.c.o
  ComputeLeverArmSn                  56      2  Init  Gb  navi.o
  app_accum_verify_8bit              56      2  Init  Gb  app_tool.o
  crc_verify_8bit                    56      2  Init  Gb  app_tool.o
  myget_16bit_D32                    56      2  Init  Gb  readpaoche.o
  sdmmchost_set_speed_mode           56      2  Init  Gb  hpm_sdmmc_host.c.o
  sdxc_enable_auto_tuning            56      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_send_command                  56      2  Init  Gb  hpm_sdxc_drv.c.o
  uart_dma_output                    56      2  Init  Gb  uart_dma.o
  Drv_FlashErase                     54      2  Init  Gb  FirmwareUpdateFile.o
  clst2sect                          54      2  Init  Lc  ff.c.o
  f_chdrive                          54      2  Init  Gb  ff.c.o
  myget_16bit_I32                    54      2  Init  Gb  readpaoche.o
  rom_xpi_nor_erase_chip             54      2  Init  Lc  flash.o
  sdxc_enable_interrupt_signal
                                     54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_interrupt_status
                                     54      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_enable_interrupt_status
                                     54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_is_inverse_clock_enabled
                                     54      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_is_inverse_clock_enabled
                                     54      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_set_post_change_delay
                                     54      2  Init  Lc  hpm_sdmmc_common.c.o
  sdmmchost_set_card_clock           52      2  Init  Gb  hpm_sdmmc_host.c.o
  sdxc_get_data_bus_width            52      2  Init  Gb  hpm_sdxc_drv.c.o
  tick_ms_isr                        52      4  Init  Gb  timer.o
  Uart_SendMsg                       50      2  Init  Gb  bsp_fmc.o
  norflash_erase_sector              50      2  Init  Gb  flash.o
  sdxc_enable_power                  48      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_enable_power                  48      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_enable_tm_clock               48      2  Init  Lc  hpm_sdxc_drv.c.o
  Pwm_Init                           46      2  Init  Gb  timer.o
  rom_xpi_nor_auto_config            46      2  Init  Lc  flash.o
  Exti_Init                          44      2  Init  Gb  main.o
  SdFileWriteOperate                 44      2  Init  Gb  sd_fatfs.o
  get_fpgadata                       44      2  Init  Gb  fpgad.o
  gpio_clear_pin_interrupt_flag
                                     44      2  Init  Lc  main.o
  gpio_enable_pin_interrupt          44      2  Init  Lc  main.o
  gpio_set_pin_input                 44      2  Init  Lc  main.o
  gpio_set_pin_output                44      2  Init  Lc  bsp_gpio.o
  gptmr_start_counter                44      2  Init  Lc  timer.o
  gptmr_stop_counter                 44      2  Init  Lc  timer.o
  ff_queue_is_empty                  42      2  Init  Gb  ff_queue.o
  gpio_toggle_pin                    42      2  Init  Lc  main.o
  sd_set_max_current                 42      2  Init  Gb  hpm_sdmmc_sd.c.o
  hpm_sdmmc_osal_event_create
                                     40      2  Init  Wk  hpm_sdmmc_osal.o
  sdmmchost_wait_card_active
                                     40      2  Init  Gb  hpm_sdmmc_host.c.o
  uart_get_current_recv_remaining_size
                                     40      2  Init  Lc  uart_dma.o
  ff_handle_poll                     38      2  Init  Gb  ff_queue.o
  hpm_sdmmc_osal_delay               38      2  Init  Wk  hpm_sdmmc_osal.o
  myget_8bit_I16                     38      2  Init  Gb  readpaoche.o
  putc_init                          38      2  Init  Lc  ff.c.o
  gptmr_check_status                 36      2  Init  Lc  timer.o
  isr_gpio                           36      4  Init  Gb  main.o
  sdmmc_get_sys_addr                 36      2  Init  Gb  hpm_sdmmc_port.c.o
  sdxc_get_default_cardclk_delay_chain
                                     36      2  Init  Lc  hpm_sdmmc_host.c.o
  xsum32                             36      2  Init  Lc  ff.c.o
  EXTI3_IRQHandler                   34      2  Init  Gb  gd32f4xx_it.o
  init_gpio                          34      2  Init  Gb  main.o
  output_normal_do                   34      2  Init  Gb  INS_Output.o
  sdmmchost_error_recovery           34      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_register_xfer_complete_callback
                                     34      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_switch_to_3v3_as_needed
                                     34      2  Init  Lc  hpm_sdmmc_host.c.o
  Drv_FlashRead                      32      2  Init  Gb  FirmwareUpdateFile.o
  timer_Init                         32      2  Init  Gb  timer.o
  hpm_sdmmc_osal_event_clear
                                     30      2  Init  Wk  hpm_sdmmc_osal.o
  loopDoOther                        30      2  Init  Gb  datado.o
  sd_is_card_present                 30      2  Init  Gb  hpm_sdmmc_sd.c.o
  sdxc_is_bus_idle                   30      2  Init  Gb  hpm_sdxc_drv.c.o
  Led_Control                        28      2  Init  Gb  main.o
  bsp_tim_init                       28      2  Init  Gb  bsp_tim.o
  gd_eval_com_init                   28      2  Init  Gb  INS_Init.o
  gptmr_enable_irq                   28      2  Init  Lc  timer.o
  sdcard_isr                         28      4  Init  Gb  hpm_sdmmc_disk.c.o
  sdmmchost_delay_ms                 28      2  Init  Gb  hpm_sdmmc_host.c.o
  sdmmchost_is_voltage_switch_supported
                                     28      2  Init  Gb  hpm_sdmmc_host.c.o
  uart4sendmsg                       28      2  Init  Gb  gd32f4xx_it.o
  Drv_SystemReset                    26      2  Init  Gb  FirmwareUpdateFile.o
  hpm_sdmmc_osal_event_set           26      2  Init  Wk  hpm_sdmmc_osal.o
  sdxc_is_card_inserted              26      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_reset_tuning_engine           26      2  Init  Lc  hpm_sdxc_drv.c.o
  ACC_gyroreset_r_TAFEAG16_buf
                                     24      2  Init  Gb  InsTestingEntry.o
  Algorithm_before_otherDataDo
                                     24      2  Init  Gb  datado.o
  delay_ms                           24      2  Init  Gb  systick.o
  dma_get_remaining_transfer_size
                                     24      2  Init  Lc  uart_dma.o
  protocol_send                      24      2  Init  Gb  computerFrameParse.o
  sdmmchost_is_8bit_supported
                                     24      2  Init  Gb  hpm_sdmmc_host.c.o
  sdxc_execute_tuning                24      2  Init  Lc  hpm_sdxc_drv.c.o
  get_uart_tx_idle                   22      2  Init  Gb  uart_dma.o
  gptmr_clear_status                 20      2  Init  Lc  timer.o
  mchtmr_get_count                   18      2  Init  Lc  sd_fatfs.o
  ppor_sw_reset                      18      2  Init  Lc  FirmwareUpdateFile.o
  sdxc_clear_interrupt_status
                                     18      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_clear_interrupt_status
                                     18      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_data3_0_level             18      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_data7_4_level             18      2  Init  Lc  hpm_sdmmc_host.c.o
  comm_axis_read                     16      2  Init  Gb  computerFrameParse.o
  comm_read_currentFreq              16      2  Init  Gb  computerFrameParse.o
  sdxc_select_cardclk_delay_source
                                     16      2  Init  Lc  hpm_sdmmc_common.c.o
  sdxc_get_interrupt_signal          14      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_interrupt_status          14      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_get_interrupt_status          14      2  Init  Lc  hpm_sdmmc_host.c.o
  sdxc_get_present_status            14      2  Init  Lc  hpm_sdxc_drv.c.o
  sdxc_is_ddr50_supported            12      2  Init  Lc  hpm_sdmmc_port.c.o
  sd_deinit                          10      2  Init  Gb  hpm_sdmmc_sd.c.o
  comm_nav_para_syn                   4      2  Init  Gb  computerFrameParse.o
  get_fpgadata_before                 4      2  Init  Gb  fpgad.o

Function symbols by name:

  Symbol name                   Address        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ----------  -----  ----  --  -----------
  __SEGGER_RTL_SIGNAL_SIG_DFL
                             0x80010092           2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_ERR
                             0x800279F2           2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_IGN
                             0x800105F6           2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_assert      0x8002CE94         112      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_errno_addr  0x8002A14A          14      2  Code  Wk  errno.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_file_bufsize
                             0x8002C3A6          12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_stat   0x8002C39A          12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_write  0x8002C31A         140      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_heap_lock   0x8002DA66          24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_heap_unlock
                             0x8002DA7A          24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  __SEGGER_RTL_alloc         0x8002DA12          88      2  Code  Gb  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_isctype
                             0x8002DA8E          44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_iswctype
                             0x8002DACA          44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_mbtowc  0x8002BD8C          42      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_tolower
                             0x8002DABA          16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_toupper
                             0x8002BD70          14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_towlower
                             0x8002DAF6          16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_towupper
                             0x8002BD7E          14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_wctomb  0x8002DB06          20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_current_locale
                             0x8002DB1A          20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_PolyEvalP
                             0x8002CFE6          56      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_PolyEvalQ
                             0x8002A28E          54      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_asinacos_fpu
                             0x8002A4CE         302      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_cos_inline
                             0x8002D0B8         172      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_frexp_inline
                             0x8002A2C4          62      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isinf
                             0x8002A30C          10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isnan
                             0x8002A302          10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isnormal
                             0x8002A316          10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_signbit
                             0x8002D17C          12      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_sin_inline
                             0x8002D01E         160      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_init_heap     0x8002BD40          22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __SEGGER_RTL_init_prin     0x8002AFF8          34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_init_prin_l   0x8002AFD6          38      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_isctype       0x8002DB2A          36      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ldouble_to_double
                             0x8002CF6A         124      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_pow10         0x8002D850          60      2  Code  Gb  utilops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_pre_padding   0x8002AFBC          30      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_prin_flush    0x8002AF9A          34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_print_padding
                             0x8002D934          48      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_putc          0x8002D898         160      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_puts_no_nl    0x8002CDDA          44      2  Code  Lc  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_stream_write  0x8002D88C          16      2  Code  Lc  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_vfprintf      0x8002B05E       3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_vfprintf_float_long
                             0x8002B05E       3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_xltoa         0x8002A0C4         106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_xtoa          0x8002A0C4         106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_init_copy         0x80040D42          28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __SEGGER_init_done         0x8001006C                  2  Code  Gb  startup.s.o
  __SEGGER_init_heap         0x8002BD2C          26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __SEGGER_init_lzss         0x80040CCE          96      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __SEGGER_init_pack         0x80040C84          74      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __SEGGER_init_zero         0x80040D2E          20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __ashldi3                  0x8002A5F0          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  __fixunsdfdi               0x8002CF16          84      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __floatundidf              0x8002A278          22      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __floatundisf              0x8002A1C2         182      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __lshrdi3                  0x8002A616          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  __trunctfdf2               0x8002D15E          36      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __udivdi3                  0x8002A63C       1 074      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  __umoddi3                  0x8002AA5C       1 102      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  _clean_up                  0x8002C752         172      2  Code  Wk  reset.c.o
  _init                      0x8002C81A           4      2  Code  Wk  reset.c.o
  _init_ext_ram              0x80028B9E         170      2  Code  Gb  board.c.o
  _start                     0x80010000         190      2  Code  Gb  startup.s.o
  abort                      0x8002CE8A          16      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  abs                        0x8002AE98          10      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  asin                       0x8002A5EC          10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  atan                       0x8002D368         272      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  board_delay_ms             0x8002BECE          24      2  Code  Gb  board.c.o
  board_init                 0x8002BE9A          60      2  Code  Gb  board.c.o
  board_init_clock           0x800286C2       1 586      2  Code  Gb  board.c.o
  board_init_console         0x80027F22         118      2  Code  Gb  board.c.o
  board_init_femc_clock      0x8002839A          50      2  Code  Gb  board.c.o
  board_init_pmp             0x800283D2         770      2  Code  Gb  board.c.o
  board_init_sram_pins       0x8002BEBE          20      2  Code  Gb  board.c.o
  board_init_uart            0x8002828E          34      2  Code  Gb  board.c.o
  board_init_uart_clock      0x8002BEE2         492      2  Code  Gb  board.c.o
  board_print_banner         0x800282AA         110      2  Code  Gb  board.c.o
  board_print_clock_freq     0x80027F92         654      2  Code  Gb  board.c.o
  board_sd_configure_clock   0x80028DB8         302      2  Code  Gb  board.c.o
  board_turnoff_rgb_led      0x8002833A          66      2  Code  Lc  board.c.o
  clock_add_to_group         0x80029E70          62      2  Code  Gb  hpm_clock_drv.c.o
  clock_connect_group_to_cpu
                             0x8002CA82          44      2  Code  Gb  hpm_clock_drv.c.o
  clock_cpu_delay_ms         0x80029EAA         162      2  Code  Gb  hpm_clock_drv.c.o
  clock_get_core_clock_ticks_per_ms
                             0x8002CAAE          56      2  Code  Gb  hpm_clock_drv.c.o
  clock_get_frequency        0x80029C5E         262      2  Code  Gb  hpm_clock_drv.c.o
  clock_set_source_divider   0x80029D94         226      2  Code  Gb  hpm_clock_drv.c.o
  clock_update_core_clock    0x80029F3C          62      2  Code  Gb  hpm_clock_drv.c.o
  console_init               0x8002C2B2         108      2  Code  Gb  hpm_debug_console.c.o
  cos                        0x8002D364           8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  enable_plic_feature        0x8002CC02          44      2  Code  Gb  system.c.o
  exception_handler          0x8002C826          44      2  Code  Wk  trap.c.o
  exit                       0x80010086           2      2  Code  Gb  startup.s.o
  femc_config_sram           0x80029448         418      2  Code  Gb  hpm_femc_drv.c.o
  femc_convert_actual_size_to_memory_size
                             0x80029346          82      2  Code  Lc  hpm_femc_drv.c.o
  femc_default_config        0x800291A2         134      2  Code  Gb  hpm_femc_drv.c.o
  femc_disable               0x80029180          34      2  Code  Lc  hpm_femc_drv.c.o
  femc_enable                0x8002916A          22      2  Code  Lc  hpm_femc_drv.c.o
  femc_get_typical_sram_config
                             0x800293CE         122      2  Code  Gb  hpm_femc_drv.c.o
  femc_init                  0x80029228         302      2  Code  Gb  hpm_femc_drv.c.o
  femc_sw_reset              0x8002C3B2          28      2  Code  Lc  hpm_femc_drv.c.o
  floor                      0x8002A320         202      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  fmod                       0x8002D1EC         380      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  fputc                      0x8002A19E          42      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  frexp                      0x8002D1E8           8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  fwrite                     0x8002A158          78      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  get_frequency_for_i2s_or_adc
                             0x8002C952         224      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_ip_in_common_group
                             0x80029D26         114      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_pwdg     0x8002CA5E          36      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_source   0x8002C852         300      2  Code  Gb  hpm_clock_drv.c.o
  get_frequency_for_wdg      0x8002CA2A          56      2  Code  Lc  hpm_clock_drv.c.o
  gpio_config_pin_interrupt  0x800295B4         234      2  Code  Gb  hpm_gpio_drv.c.o
  hpm_csr_get_core_cycle     0x80029B7A         122      2  Code  Lc  hpm_clock_drv.c.o
  init_gptmr_pins            0x80028FE0         186      2  Code  Gb  pinmux.c.o
  init_sdxc_cd_pin           0x8002908A          62      2  Code  Gb  pinmux.c.o
  init_sdxc_clk_data_pins    0x800290C8         162      2  Code  Gb  pinmux.c.o
  init_sdxc_cmd_pin          0x8002C242          96      2  Code  Gb  pinmux.c.o
  init_sdxc_pwr_pin          0x8002C2A2          16      2  Code  Gb  pinmux.c.o
  init_sram_pins             0x80028EBA         294      2  Code  Gb  pinmux.c.o
  init_uart_pins             0x8002C0EA         344      2  Code  Gb  pinmux.c.o
  isdigit                    0x8002BDB6          10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  isspace                    0x8002BDBC          10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  itoa                       0x8002A12E          34      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  l1c_dc_enable              0x800279BE          58      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_enable_writearound  0x80027D66          10      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_flush               0x80027D02         102      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_invalidate          0x80027A3E         102      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_invalidate_all      0x8002BDC2          24      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_writeback           0x80027C8A         102      2  Code  Gb  hpm_l1c_drv.o
  l1c_ic_enable              0x80027A02          46      2  Code  Gb  hpm_l1c_drv.o
  l1c_op                     0x8002741A          82      2  Code  Lc  hpm_l1c_drv.o
  ldexp                      0x8002D188          96      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  ldexp.localalias           0x8002D188          96      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  log                        0x8002D46E         280      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  malloc                     0x8002BD52          42      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  mchtmr_isr                 0x8002C81E           4      2  Code  Wk  trap.c.o
  memcmp                     0x8002D690         268      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  memcpy                     0x8002AEA2         134      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  memset                     0x8002D578         104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  ns2cycle                   0x80029398          54      2  Code  Lc  hpm_femc_drv.c.o
  pcfg_dcdc_set_voltage      0x8002C3CE          72      2  Code  Gb  hpm_pcfg_drv.c.o
  pcfg_dcdc_switch_to_dcm_mode
                             0x80027DD2         210      2  Code  Lc  board.c.o
  pllctl_get_div             0x80029BF4         106      2  Code  Lc  hpm_clock_drv.c.o
  pllctl_get_pll_freq_in_hz  0x8002974C         282      2  Code  Gb  hpm_pllctl_drv.c.o
  pllctl_init_int_pll_with_freq
                             0x8002C462         584      2  Code  Gb  hpm_pllctl_drv.c.o
  pllctl_pll_powerdown       0x8002C416          76      2  Code  Lc  hpm_pllctl_drv.c.o
  pllctl_pll_poweron         0x8002969E         174      2  Code  Lc  hpm_pllctl_drv.c.o
  pllctl_xtal_set_rampup_time
                             0x80027D70          38      2  Code  Lc  board.c.o
  pmp_config                 0x80029A96         242      2  Code  Gb  hpm_pmp_drv.c.o
  printf                     0x8002B036          46      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  putchar                    0x8002CED6          16      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  puts                       0x8002CEE2          68      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  raise                      0x8002CE2E         108      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  read_pma_cfg               0x8002997E         110      2  Code  Gb  hpm_pmp_drv.c.o
  read_pmp_cfg               0x80029866         110      2  Code  Gb  hpm_pmp_drv.c.o
  reset_handler              0x8002C7FE          32      2  Code  Wk  reset.c.o
  sdxc_enable_inverse_clock  0x8002BE06          80      2  Code  Lc  board.c.o
  sdxc_enable_sd_clock       0x8002BE56          68      2  Code  Lc  board.c.o
  signal                     0x8002CDFE          52      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  sin                        0x8002D360           8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  sprintf                    0x8002D95E          68      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  sqrt                       0x8002A4C8           6      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  start                      0x80010080                  2  Code  Gb  startup.s.o
  strcat                     0x8002D79C          40      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  strchr                     0x8002AF28         114      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  strcpy                     0x8002D5E0          72      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  strlen                     0x8002D628         104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  strnlen                    0x8002D7B8         152      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  strtod                     0x8002CC2E         448      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  swi_isr                    0x8002C822           4      2  Code  Wk  trap.c.o
  syscall_handler            0x80029B68          18      2  Code  Wk  trap.c.o
  sysctl_clock_set_preset    0x8002BDDA          44      2  Code  Lc  board.c.o
  sysctl_clock_target_is_busy
                             0x80029F9A          46      2  Code  Lc  hpm_sysctl_drv.c.o
  sysctl_config_clock        0x80029FC8         174      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_enable_group_resource
                             0x8002CADA         304      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_resource_target_is_busy
                             0x80029F70          42      2  Code  Lc  hpm_sysctl_drv.c.o
  system_init                0x8002A06A          94      2  Code  Wk  system.c.o
  tan                        0x8002A3DE         242      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  trgm_enable_io_output      0x8002C07A          28      2  Code  Lc  pinmux.c.o
  trgm_output_config         0x8002C096          84      2  Code  Lc  pinmux.c.o
  vfprintf                   0x8002B010          46      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  vfprintf_l                 0x8002D99A         132      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  write_pma_addr             0x800299EC         170      2  Code  Gb  hpm_pmp_drv.c.o
  write_pma_cfg              0x8002C6F6          92      2  Code  Gb  hpm_pmp_drv.c.o
  write_pmp_addr             0x800298D4         170      2  Code  Gb  hpm_pmp_drv.c.o
  write_pmp_cfg              0x8002C69A          92      2  Code  Gb  hpm_pmp_drv.c.o

Function symbols by address:

     Address  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  -------------------------  ----------  -----  ----  --  -----------
  0x80010000  _start                            190      2  Code  Gb  startup.s.o
  0x8001006C  __SEGGER_init_done                         2  Code  Gb  startup.s.o
  0x80010080  start                                      2  Code  Gb  startup.s.o
  0x80010086  exit                                2      2  Code  Gb  startup.s.o
  0x80010092  __SEGGER_RTL_SIGNAL_SIG_DFL
                                                  2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  0x800105F6  __SEGGER_RTL_SIGNAL_SIG_IGN
                                                  2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  0x8002741A  l1c_op                             82      2  Code  Lc  hpm_l1c_drv.o
  0x800279BE  l1c_dc_enable                      58      2  Code  Gb  hpm_l1c_drv.o
  0x800279F2  __SEGGER_RTL_SIGNAL_SIG_ERR
                                                  2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  0x80027A02  l1c_ic_enable                      46      2  Code  Gb  hpm_l1c_drv.o
  0x80027A3E  l1c_dc_invalidate                 102      2  Code  Gb  hpm_l1c_drv.o
  0x80027C8A  l1c_dc_writeback                  102      2  Code  Gb  hpm_l1c_drv.o
  0x80027D02  l1c_dc_flush                      102      2  Code  Gb  hpm_l1c_drv.o
  0x80027D66  l1c_dc_enable_writearound          10      2  Code  Gb  hpm_l1c_drv.o
  0x80027D70  pllctl_xtal_set_rampup_time
                                                 38      2  Code  Lc  board.c.o
  0x80027DD2  pcfg_dcdc_switch_to_dcm_mode
                                                210      2  Code  Lc  board.c.o
  0x80027F22  board_init_console                118      2  Code  Gb  board.c.o
  0x80027F92  board_print_clock_freq            654      2  Code  Gb  board.c.o
  0x8002828E  board_init_uart                    34      2  Code  Gb  board.c.o
  0x800282AA  board_print_banner                110      2  Code  Gb  board.c.o
  0x8002833A  board_turnoff_rgb_led              66      2  Code  Lc  board.c.o
  0x8002839A  board_init_femc_clock              50      2  Code  Gb  board.c.o
  0x800283D2  board_init_pmp                    770      2  Code  Gb  board.c.o
  0x800286C2  board_init_clock                1 586      2  Code  Gb  board.c.o
  0x80028B9E  _init_ext_ram                     170      2  Code  Gb  board.c.o
  0x80028DB8  board_sd_configure_clock          302      2  Code  Gb  board.c.o
  0x80028EBA  init_sram_pins                    294      2  Code  Gb  pinmux.c.o
  0x80028FE0  init_gptmr_pins                   186      2  Code  Gb  pinmux.c.o
  0x8002908A  init_sdxc_cd_pin                   62      2  Code  Gb  pinmux.c.o
  0x800290C8  init_sdxc_clk_data_pins           162      2  Code  Gb  pinmux.c.o
  0x8002916A  femc_enable                        22      2  Code  Lc  hpm_femc_drv.c.o
  0x80029180  femc_disable                       34      2  Code  Lc  hpm_femc_drv.c.o
  0x800291A2  femc_default_config               134      2  Code  Gb  hpm_femc_drv.c.o
  0x80029228  femc_init                         302      2  Code  Gb  hpm_femc_drv.c.o
  0x80029346  femc_convert_actual_size_to_memory_size
                                                 82      2  Code  Lc  hpm_femc_drv.c.o
  0x80029398  ns2cycle                           54      2  Code  Lc  hpm_femc_drv.c.o
  0x800293CE  femc_get_typical_sram_config
                                                122      2  Code  Gb  hpm_femc_drv.c.o
  0x80029448  femc_config_sram                  418      2  Code  Gb  hpm_femc_drv.c.o
  0x800295B4  gpio_config_pin_interrupt         234      2  Code  Gb  hpm_gpio_drv.c.o
  0x8002969E  pllctl_pll_poweron                174      2  Code  Lc  hpm_pllctl_drv.c.o
  0x8002974C  pllctl_get_pll_freq_in_hz         282      2  Code  Gb  hpm_pllctl_drv.c.o
  0x80029866  read_pmp_cfg                      110      2  Code  Gb  hpm_pmp_drv.c.o
  0x800298D4  write_pmp_addr                    170      2  Code  Gb  hpm_pmp_drv.c.o
  0x8002997E  read_pma_cfg                      110      2  Code  Gb  hpm_pmp_drv.c.o
  0x800299EC  write_pma_addr                    170      2  Code  Gb  hpm_pmp_drv.c.o
  0x80029A96  pmp_config                        242      2  Code  Gb  hpm_pmp_drv.c.o
  0x80029B68  syscall_handler                    18      2  Code  Wk  trap.c.o
  0x80029B7A  hpm_csr_get_core_cycle            122      2  Code  Lc  hpm_clock_drv.c.o
  0x80029BF4  pllctl_get_div                    106      2  Code  Lc  hpm_clock_drv.c.o
  0x80029C5E  clock_get_frequency               262      2  Code  Gb  hpm_clock_drv.c.o
  0x80029D26  get_frequency_for_ip_in_common_group
                                                114      2  Code  Lc  hpm_clock_drv.c.o
  0x80029D94  clock_set_source_divider          226      2  Code  Gb  hpm_clock_drv.c.o
  0x80029E70  clock_add_to_group                 62      2  Code  Gb  hpm_clock_drv.c.o
  0x80029EAA  clock_cpu_delay_ms                162      2  Code  Gb  hpm_clock_drv.c.o
  0x80029F3C  clock_update_core_clock            62      2  Code  Gb  hpm_clock_drv.c.o
  0x80029F70  sysctl_resource_target_is_busy
                                                 42      2  Code  Lc  hpm_sysctl_drv.c.o
  0x80029F9A  sysctl_clock_target_is_busy
                                                 46      2  Code  Lc  hpm_sysctl_drv.c.o
  0x80029FC8  sysctl_config_clock               174      2  Code  Gb  hpm_sysctl_drv.c.o
  0x8002A06A  system_init                        94      2  Code  Wk  system.c.o
  0x8002A0C4  __SEGGER_RTL_xtoa                 106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  0x8002A0C4  __SEGGER_RTL_xltoa                106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  0x8002A12E  itoa                               34      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  0x8002A14A  __SEGGER_RTL_X_errno_addr          14      2  Code  Wk  errno.o (libc_rv32gc_d_balanced.a)
  0x8002A158  fwrite                             78      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  0x8002A19E  fputc                              42      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  0x8002A1C2  __floatundisf                     182      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002A278  __floatundidf                      22      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002A28E  __SEGGER_RTL_float64_PolyEvalQ
                                                 54      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A2C4  __SEGGER_RTL_float64_frexp_inline
                                                 62      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A302  __SEGGER_RTL_float64_isnan
                                                 10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A30C  __SEGGER_RTL_float64_isinf
                                                 10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A316  __SEGGER_RTL_float64_isnormal
                                                 10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A320  floor                             202      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A3DE  tan                               242      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A4C8  sqrt                                6      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A4CE  __SEGGER_RTL_float64_asinacos_fpu
                                                302      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A5EC  asin                               10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002A5F0  __ashldi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002A616  __lshrdi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002A63C  __udivdi3                       1 074      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  0x8002AA5C  __umoddi3                       1 102      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  0x8002AE98  abs                                10      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  0x8002AEA2  memcpy                            134      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002AF28  strchr                            114      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002AF9A  __SEGGER_RTL_prin_flush            34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002AFBC  __SEGGER_RTL_pre_padding           30      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002AFD6  __SEGGER_RTL_init_prin_l           38      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002AFF8  __SEGGER_RTL_init_prin             34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002B010  vfprintf                           46      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002B036  printf                             46      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002B05E  __SEGGER_RTL_vfprintf_float_long
                                              3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  0x8002B05E  __SEGGER_RTL_vfprintf           3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  0x8002BD2C  __SEGGER_init_heap                 26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  0x8002BD40  __SEGGER_RTL_init_heap             22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  0x8002BD52  malloc                             42      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  0x8002BD70  __SEGGER_RTL_ascii_toupper
                                                 14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002BD7E  __SEGGER_RTL_ascii_towupper
                                                 14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002BD8C  __SEGGER_RTL_ascii_mbtowc          42      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002BDB6  isdigit                            10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002BDBC  isspace                            10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002BDC2  l1c_dc_invalidate_all              24      2  Code  Gb  hpm_l1c_drv.o
  0x8002BDDA  sysctl_clock_set_preset            44      2  Code  Lc  board.c.o
  0x8002BE06  sdxc_enable_inverse_clock          80      2  Code  Lc  board.c.o
  0x8002BE56  sdxc_enable_sd_clock               68      2  Code  Lc  board.c.o
  0x8002BE9A  board_init                         60      2  Code  Gb  board.c.o
  0x8002BEBE  board_init_sram_pins               20      2  Code  Gb  board.c.o
  0x8002BECE  board_delay_ms                     24      2  Code  Gb  board.c.o
  0x8002BEE2  board_init_uart_clock             492      2  Code  Gb  board.c.o
  0x8002C07A  trgm_enable_io_output              28      2  Code  Lc  pinmux.c.o
  0x8002C096  trgm_output_config                 84      2  Code  Lc  pinmux.c.o
  0x8002C0EA  init_uart_pins                    344      2  Code  Gb  pinmux.c.o
  0x8002C242  init_sdxc_cmd_pin                  96      2  Code  Gb  pinmux.c.o
  0x8002C2A2  init_sdxc_pwr_pin                  16      2  Code  Gb  pinmux.c.o
  0x8002C2B2  console_init                      108      2  Code  Gb  hpm_debug_console.c.o
  0x8002C31A  __SEGGER_RTL_X_file_write         140      2  Code  Gb  hpm_debug_console.c.o
  0x8002C39A  __SEGGER_RTL_X_file_stat           12      2  Code  Gb  hpm_debug_console.c.o
  0x8002C3A6  __SEGGER_RTL_X_file_bufsize
                                                 12      2  Code  Gb  hpm_debug_console.c.o
  0x8002C3B2  femc_sw_reset                      28      2  Code  Lc  hpm_femc_drv.c.o
  0x8002C3CE  pcfg_dcdc_set_voltage              72      2  Code  Gb  hpm_pcfg_drv.c.o
  0x8002C416  pllctl_pll_powerdown               76      2  Code  Lc  hpm_pllctl_drv.c.o
  0x8002C462  pllctl_init_int_pll_with_freq
                                                584      2  Code  Gb  hpm_pllctl_drv.c.o
  0x8002C69A  write_pmp_cfg                      92      2  Code  Gb  hpm_pmp_drv.c.o
  0x8002C6F6  write_pma_cfg                      92      2  Code  Gb  hpm_pmp_drv.c.o
  0x8002C752  _clean_up                         172      2  Code  Wk  reset.c.o
  0x8002C7FE  reset_handler                      32      2  Code  Wk  reset.c.o
  0x8002C81A  _init                               4      2  Code  Wk  reset.c.o
  0x8002C81E  mchtmr_isr                          4      2  Code  Wk  trap.c.o
  0x8002C822  swi_isr                             4      2  Code  Wk  trap.c.o
  0x8002C826  exception_handler                  44      2  Code  Wk  trap.c.o
  0x8002C852  get_frequency_for_source          300      2  Code  Gb  hpm_clock_drv.c.o
  0x8002C952  get_frequency_for_i2s_or_adc
                                                224      2  Code  Lc  hpm_clock_drv.c.o
  0x8002CA2A  get_frequency_for_wdg              56      2  Code  Lc  hpm_clock_drv.c.o
  0x8002CA5E  get_frequency_for_pwdg             36      2  Code  Lc  hpm_clock_drv.c.o
  0x8002CA82  clock_connect_group_to_cpu
                                                 44      2  Code  Gb  hpm_clock_drv.c.o
  0x8002CAAE  clock_get_core_clock_ticks_per_ms
                                                 56      2  Code  Gb  hpm_clock_drv.c.o
  0x8002CADA  sysctl_enable_group_resource
                                                304      2  Code  Gb  hpm_sysctl_drv.c.o
  0x8002CC02  enable_plic_feature                44      2  Code  Gb  system.c.o
  0x8002CC2E  strtod                            448      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  0x8002CDDA  __SEGGER_RTL_puts_no_nl            44      2  Code  Lc  execops.o (libc_rv32gc_d_balanced.a)
  0x8002CDFE  signal                             52      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  0x8002CE2E  raise                             108      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  0x8002CE8A  abort                              16      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  0x8002CE94  __SEGGER_RTL_X_assert             112      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  0x8002CED6  putchar                            16      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  0x8002CEE2  puts                               68      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  0x8002CF16  __fixunsdfdi                       84      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002CF6A  __SEGGER_RTL_ldouble_to_double
                                                124      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002CFE6  __SEGGER_RTL_float64_PolyEvalP
                                                 56      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D01E  __SEGGER_RTL_float64_sin_inline
                                                160      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D0B8  __SEGGER_RTL_float64_cos_inline
                                                172      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D15E  __trunctfdf2                       36      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D17C  __SEGGER_RTL_float64_signbit
                                                 12      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D188  ldexp.localalias                   96      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D188  ldexp                              96      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D1E8  frexp                               8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D1EC  fmod                              380      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D360  sin                                 8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D364  cos                                 8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D368  atan                              272      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D46E  log                               280      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  0x8002D578  memset                            104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002D5E0  strcpy                             72      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002D628  strlen                            104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x8002D690  memcmp                            268      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  0x8002D79C  strcat                             40      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  0x8002D7B8  strnlen                           152      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  0x8002D850  __SEGGER_RTL_pow10                 60      2  Code  Gb  utilops.o (libc_rv32gc_d_balanced.a)
  0x8002D88C  __SEGGER_RTL_stream_write          16      2  Code  Lc  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002D898  __SEGGER_RTL_putc                 160      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002D934  __SEGGER_RTL_print_padding
                                                 48      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002D95E  sprintf                            68      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002D99A  vfprintf_l                        132      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002DA12  __SEGGER_RTL_alloc                 88      2  Code  Gb  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  0x8002DA66  __SEGGER_RTL_X_heap_lock           24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  0x8002DA7A  __SEGGER_RTL_X_heap_unlock
                                                 24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  0x8002DA8E  __SEGGER_RTL_ascii_isctype
                                                 44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DABA  __SEGGER_RTL_ascii_tolower
                                                 16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DACA  __SEGGER_RTL_ascii_iswctype
                                                 44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DAF6  __SEGGER_RTL_ascii_towlower
                                                 16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DB06  __SEGGER_RTL_ascii_wctomb          20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DB1A  __SEGGER_RTL_current_locale
                                                 20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DB2A  __SEGGER_RTL_isctype               36      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80040C84  __SEGGER_init_pack                 74      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  0x80040CCE  __SEGGER_init_lzss                 96      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  0x80040D2E  __SEGGER_init_zero                 20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  0x80040D42  __SEGGER_init_copy                 28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)

Function symbols by descending size:

  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  __SEGGER_RTL_vfprintf           3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_vfprintf_float_long
                                  3 538      2  Code  Gb  __SEGGER_RTL_vfprintf_float_long.o (libc_rv32gc_d_balanced.a)
  board_init_clock                1 586      2  Code  Gb  board.c.o
  __umoddi3                       1 102      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  __udivdi3                       1 074      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  board_init_pmp                    770      2  Code  Gb  board.c.o
  board_print_clock_freq            654      2  Code  Gb  board.c.o
  pllctl_init_int_pll_with_freq
                                    584      2  Code  Gb  hpm_pllctl_drv.c.o
  board_init_uart_clock             492      2  Code  Gb  board.c.o
  strtod                            448      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  femc_config_sram                  418      2  Code  Gb  hpm_femc_drv.c.o
  fmod                              380      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  init_uart_pins                    344      2  Code  Gb  pinmux.c.o
  sysctl_enable_group_resource
                                    304      2  Code  Gb  hpm_sysctl_drv.c.o
  __SEGGER_RTL_float64_asinacos_fpu
                                    302      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  board_sd_configure_clock          302      2  Code  Gb  board.c.o
  femc_init                         302      2  Code  Gb  hpm_femc_drv.c.o
  get_frequency_for_source          300      2  Code  Gb  hpm_clock_drv.c.o
  init_sram_pins                    294      2  Code  Gb  pinmux.c.o
  pllctl_get_pll_freq_in_hz         282      2  Code  Gb  hpm_pllctl_drv.c.o
  log                               280      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  atan                              272      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  memcmp                            268      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  clock_get_frequency               262      2  Code  Gb  hpm_clock_drv.c.o
  pmp_config                        242      2  Code  Gb  hpm_pmp_drv.c.o
  tan                               242      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  gpio_config_pin_interrupt         234      2  Code  Gb  hpm_gpio_drv.c.o
  clock_set_source_divider          226      2  Code  Gb  hpm_clock_drv.c.o
  get_frequency_for_i2s_or_adc
                                    224      2  Code  Lc  hpm_clock_drv.c.o
  pcfg_dcdc_switch_to_dcm_mode
                                    210      2  Code  Lc  board.c.o
  floor                             202      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  _start                            190      2  Code  Gb  startup.s.o
  init_gptmr_pins                   186      2  Code  Gb  pinmux.c.o
  __floatundisf                     182      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  pllctl_pll_poweron                174      2  Code  Lc  hpm_pllctl_drv.c.o
  sysctl_config_clock               174      2  Code  Gb  hpm_sysctl_drv.c.o
  __SEGGER_RTL_float64_cos_inline
                                    172      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  _clean_up                         172      2  Code  Wk  reset.c.o
  _init_ext_ram                     170      2  Code  Gb  board.c.o
  write_pma_addr                    170      2  Code  Gb  hpm_pmp_drv.c.o
  write_pmp_addr                    170      2  Code  Gb  hpm_pmp_drv.c.o
  clock_cpu_delay_ms                162      2  Code  Gb  hpm_clock_drv.c.o
  init_sdxc_clk_data_pins           162      2  Code  Gb  pinmux.c.o
  __SEGGER_RTL_float64_sin_inline
                                    160      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_putc                 160      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  strnlen                           152      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_file_write         140      2  Code  Gb  hpm_debug_console.c.o
  femc_default_config               134      2  Code  Gb  hpm_femc_drv.c.o
  memcpy                            134      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  vfprintf_l                        132      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_ldouble_to_double
                                    124      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  femc_get_typical_sram_config
                                    122      2  Code  Gb  hpm_femc_drv.c.o
  hpm_csr_get_core_cycle            122      2  Code  Lc  hpm_clock_drv.c.o
  board_init_console                118      2  Code  Gb  board.c.o
  get_frequency_for_ip_in_common_group
                                    114      2  Code  Lc  hpm_clock_drv.c.o
  strchr                            114      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_assert             112      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  board_print_banner                110      2  Code  Gb  board.c.o
  read_pma_cfg                      110      2  Code  Gb  hpm_pmp_drv.c.o
  read_pmp_cfg                      110      2  Code  Gb  hpm_pmp_drv.c.o
  console_init                      108      2  Code  Gb  hpm_debug_console.c.o
  raise                             108      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_xltoa                106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_xtoa                 106      2  Code  Lc  convops.o (libc_rv32gc_d_balanced.a)
  pllctl_get_div                    106      2  Code  Lc  hpm_clock_drv.c.o
  memset                            104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  strlen                            104      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  l1c_dc_flush                      102      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_invalidate                 102      2  Code  Gb  hpm_l1c_drv.o
  l1c_dc_writeback                  102      2  Code  Gb  hpm_l1c_drv.o
  __SEGGER_init_lzss                 96      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  init_sdxc_cmd_pin                  96      2  Code  Gb  pinmux.c.o
  ldexp                              96      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  ldexp.localalias                   96      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  system_init                        94      2  Code  Wk  system.c.o
  write_pma_cfg                      92      2  Code  Gb  hpm_pmp_drv.c.o
  write_pmp_cfg                      92      2  Code  Gb  hpm_pmp_drv.c.o
  __SEGGER_RTL_alloc                 88      2  Code  Gb  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __fixunsdfdi                       84      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  trgm_output_config                 84      2  Code  Lc  pinmux.c.o
  femc_convert_actual_size_to_memory_size
                                     82      2  Code  Lc  hpm_femc_drv.c.o
  l1c_op                             82      2  Code  Lc  hpm_l1c_drv.o
  sdxc_enable_inverse_clock          80      2  Code  Lc  board.c.o
  fwrite                             78      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  pllctl_pll_powerdown               76      2  Code  Lc  hpm_pllctl_drv.c.o
  __SEGGER_init_pack                 74      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  pcfg_dcdc_set_voltage              72      2  Code  Gb  hpm_pcfg_drv.c.o
  strcpy                             72      2  Code  Gb  strasmops_rv.o (libc_rv32gc_d_balanced.a)
  puts                               68      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  sdxc_enable_sd_clock               68      2  Code  Lc  board.c.o
  sprintf                            68      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  board_turnoff_rgb_led              66      2  Code  Lc  board.c.o
  __SEGGER_RTL_float64_frexp_inline
                                     62      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  clock_add_to_group                 62      2  Code  Gb  hpm_clock_drv.c.o
  clock_update_core_clock            62      2  Code  Gb  hpm_clock_drv.c.o
  init_sdxc_cd_pin                   62      2  Code  Gb  pinmux.c.o
  __SEGGER_RTL_pow10                 60      2  Code  Gb  utilops.o (libc_rv32gc_d_balanced.a)
  board_init                         60      2  Code  Gb  board.c.o
  l1c_dc_enable                      58      2  Code  Gb  hpm_l1c_drv.o
  __SEGGER_RTL_float64_PolyEvalP
                                     56      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  clock_get_core_clock_ticks_per_ms
                                     56      2  Code  Gb  hpm_clock_drv.c.o
  get_frequency_for_wdg              56      2  Code  Lc  hpm_clock_drv.c.o
  __SEGGER_RTL_float64_PolyEvalQ
                                     54      2  Code  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  ns2cycle                           54      2  Code  Lc  hpm_femc_drv.c.o
  signal                             52      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  board_init_femc_clock              50      2  Code  Gb  board.c.o
  __SEGGER_RTL_print_padding
                                     48      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  l1c_ic_enable                      46      2  Code  Gb  hpm_l1c_drv.o
  printf                             46      2  Code  Wk  prinops.o (libc_rv32gc_d_balanced.a)
  sysctl_clock_target_is_busy
                                     46      2  Code  Lc  hpm_sysctl_drv.c.o
  vfprintf                           46      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_isctype
                                     44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_iswctype
                                     44      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_puts_no_nl            44      2  Code  Lc  execops.o (libc_rv32gc_d_balanced.a)
  clock_connect_group_to_cpu
                                     44      2  Code  Gb  hpm_clock_drv.c.o
  enable_plic_feature                44      2  Code  Gb  system.c.o
  exception_handler                  44      2  Code  Wk  trap.c.o
  sysctl_clock_set_preset            44      2  Code  Lc  board.c.o
  __SEGGER_RTL_ascii_mbtowc          42      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  fputc                              42      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  malloc                             42      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  sysctl_resource_target_is_busy
                                     42      2  Code  Lc  hpm_sysctl_drv.c.o
  strcat                             40      2  Code  Wk  strops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_init_prin_l           38      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __ashldi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  __lshrdi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32gc_d_balanced.a)
  pllctl_xtal_set_rampup_time
                                     38      2  Code  Lc  board.c.o
  __SEGGER_RTL_isctype               36      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __trunctfdf2                       36      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  get_frequency_for_pwdg             36      2  Code  Lc  hpm_clock_drv.c.o
  __SEGGER_RTL_init_prin             34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_prin_flush            34      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  board_init_uart                    34      2  Code  Gb  board.c.o
  femc_disable                       34      2  Code  Lc  hpm_femc_drv.c.o
  itoa                               34      2  Code  Wk  convops.o (libc_rv32gc_d_balanced.a)
  reset_handler                      32      2  Code  Wk  reset.c.o
  __SEGGER_RTL_pre_padding           30      2  Code  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_init_copy                 28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  femc_sw_reset                      28      2  Code  Lc  hpm_femc_drv.c.o
  trgm_enable_io_output              28      2  Code  Lc  pinmux.c.o
  __SEGGER_init_heap                 26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_heap_lock           24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_heap_unlock
                                     24      2  Code  Wk  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  board_delay_ms                     24      2  Code  Gb  board.c.o
  l1c_dc_invalidate_all              24      2  Code  Gb  hpm_l1c_drv.o
  __SEGGER_RTL_init_heap             22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __floatundidf                      22      2  Code  Gb  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  femc_enable                        22      2  Code  Lc  hpm_femc_drv.c.o
  __SEGGER_RTL_ascii_wctomb          20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_current_locale
                                     20      2  Code  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_init_zero                 20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32gc_d_balanced.a)
  board_init_sram_pins               20      2  Code  Gb  board.c.o
  syscall_handler                    18      2  Code  Wk  trap.c.o
  __SEGGER_RTL_ascii_tolower
                                     16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_towlower
                                     16      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_stream_write          16      2  Code  Lc  prinops.o (libc_rv32gc_d_balanced.a)
  abort                              16      2  Code  Wk  execops.o (libc_rv32gc_d_balanced.a)
  init_sdxc_pwr_pin                  16      2  Code  Gb  pinmux.c.o
  putchar                            16      2  Code  Wk  fileops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_errno_addr          14      2  Code  Wk  errno.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_toupper
                                     14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_towupper
                                     14      2  Code  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_X_file_bufsize
                                     12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_stat           12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_float64_signbit
                                     12      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isinf
                                     10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isnan
                                     10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_isnormal
                                     10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  abs                                10      2  Code  Wk  intops.o (libc_rv32gc_d_balanced.a)
  asin                               10      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  isdigit                            10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  isspace                            10      2  Code  Wk  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  l1c_dc_enable_writearound          10      2  Code  Gb  hpm_l1c_drv.o
  cos                                 8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  frexp                               8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  sin                                 8      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  sqrt                                6      2  Code  Wk  floatops.o (libc_rv32gc_d_balanced.a)
  _init                               4      2  Code  Wk  reset.c.o
  mchtmr_isr                          4      2  Code  Wk  trap.c.o
  swi_isr                             4      2  Code  Wk  trap.c.o
  __SEGGER_RTL_SIGNAL_SIG_DFL
                                      2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_ERR
                                      2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_IGN
                                      2      2  Code  Gb  execops.o (libc_rv32gc_d_balanced.a)
  exit                                2      2  Code  Gb  startup.s.o
  __SEGGER_init_done                         2  Code  Gb  startup.s.o
  start                                      2  Code  Gb  startup.s.o

Read-write data symbols by name:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA4
  
  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  Acc                        0x0008C288                     24      8  Zero  Gb  InsTestingEntry.o
  Aw                         0x0008C128                     72      8  Zero  Gb  InsTestingEntry.o
  BB00FileName               0x000A7BA0                      4      4  Init  Gb  sd_fatfs.o
  BB00Flag.2                 0x0009EA62  gp+0x054E           2      2  Zero  Lc  sd_fatfs.o
  BB00SdData                 0x0009E1F4  gp-0x0320         336      4  Zero  Gb  InsTestingEntry.o
  COM3Flag.3                 0x0009E982  gp+0x046E           2      2  Zero  Lc  sd_fatfs.o
  CollieryOperate_Flag       0x0009EB81  gp+0x066D           1         Zero  Gb  SetParaBao.o
  CollieryOperate_uartsend_Flag
                             0x0009EB80  gp+0x066C           1         Zero  Gb  main.o
  Com3FileName               0x000A7B9C                      4      4  Init  Gb  sd_fatfs.o
  Count1                     0x0009EB7F  gp+0x066B           1         Zero  Gb  InsTestingEntry.o
  CurrVol                    0x0009EB7E  gp+0x066A           1         Zero  Lc  ff.c.o
  DirBuf                     0x0009CF84                    608      4  Zero  Lc  ff.c.o
  FatFs                      0x0009EA14  gp+0x0500          40      4  Zero  Lc  ff.c.o
  Fsid                       0x0009E672  gp+0x015E           2      2  Zero  Lc  ff.c.o
  Gw                         0x0008C0E0                     72      8  Zero  Gb  InsTestingEntry.o
  LastAcc                    0x0008C270                     24      8  Zero  Gb  InsTestingEntry.o
  LfnBuf                     0x0009DD14  gp-0x0800         512      4  Zero  Lc  ff.c.o
  NaviCompute_do_count       0x0009EB60  gp+0x064C           4      4  Zero  Gb  datado.o
  Return_Colliery_Operate_Flag
                             0x000A7B89                      1         Init  Gb  main.o
  SDCnt.0                    0x0009E562  gp+0x004E           2      2  Zero  Lc  gd32f4xx_it.o
  SetSdFlieType              0x0009EB7D  gp+0x0669           1         Zero  Gb  SetParaBao.o
  SetSdOperateType           0x0009EB7C  gp+0x0668           1         Zero  Gb  SetParaBao.o
  StrMiddleWare              0x0009EA9C  gp+0x0588          24      4  Zero  Gb  InsTestingEntry.o
  __RAL_global_locale        0x00080160                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_aSigTab       0x0009EA84  gp+0x0570          24      4  Zero  Lc  execops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_global_locale
                             0x00080160                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_heap_globals  0x0009EB6C  gp+0x0658           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __SEGGER_RTL_locale_ptr    0x0009EB68  gp+0x0654           4      4  Zero  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_stdout_file   0x0009EB5C  gp+0x0648           4      4  Zero  Lc  hpm_debug_console.c.o
  aW_real                    0x0008C258                     24      8  Zero  Gb  InsTestingEntry.o
  aWm                        0x0008C240                     24      8  Zero  Gb  InsTestingEntry.o
  aw                         0x0008C228                     24      8  Zero  Gb  InsTestingEntry.o
  axisInfo                   0x0009EB58  gp+0x0644           4      4  Zero  Gb  frame_analysis.o
  canin                      0x0009EAB4  gp+0x05A0          20      4  Zero  Gb  InsTestingEntry.o
  current_reload             0x0009EB54  gp+0x0640           4      4  Zero  Gb  timer.o
  dma_done                   0x000A7B8C                      1      4  None  Lc  uart_dma.o
  en                         0x0009EB64  gp+0x0650           4      4  Zero  Lc  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  end_ticks                  0x0008C2A0                      8      8  Zero  Gb  sd_fatfs.o
  fatfs_result               0x0009EB7B  gp+0x0667           1         Zero  Gb  sd_fatfs.o
  flag.1                     0x0009EB50  gp+0x063C           4      4  Zero  Lc  SetParaBao.o
  fpga_loop_count            0x0009EB4C  gp+0x0638           4      4  Zero  Gb  INS_Init.o
  fpga_syn                   0x0009EB7A  gp+0x0666           1         Zero  Gb  INS_Init.o
  fpga_syn_NAVI_btw          0x0009EB48  gp+0x0634           4      4  Zero  Gb  fpgad.o
  fpga_syn_btw               0x0009EB44  gp+0x0630           4      4  Zero  Gb  fpgad.o
  fpga_syn_btw_last          0x0009EB40  gp+0x062C           4      4  Zero  Gb  fpgad.o
  fpga_syn_count             0x0009EB3C  gp+0x0628           4      4  Zero  Gb  INS_Init.o
  fpgatesttxt                0x0009C384                  2 048      4  Zero  Gb  INS_Output.o
  gW_real                    0x0008C210                     24      8  Zero  Gb  InsTestingEntry.o
  gWm                        0x0008C1F8                     24      8  Zero  Gb  InsTestingEntry.o
  g_Align                    0x0008BEC8                    184      8  Zero  Gb  main.o
  g_BB00WriteSdFlag          0x0009EB79  gp+0x0665           1         Zero  Gb  InsTestingEntry.o
  g_CAN_Timeout_Cnt          0x0009EB38  gp+0x0624           4      4  Zero  Gb  INS_Init.o
  g_CAN_Timeout_Start_flag   0x0009EB34  gp+0x0620           4      4  Zero  Gb  INS_Init.o
  g_Com3WriteSdFlag          0x0009EB78  gp+0x0664           1         Zero  Gb  uart.o
  g_Compen                   0x00087C98                 10 200      8  Zero  Gb  main.o
  g_DataOutTime              0x0009EB30  gp+0x061C           4      4  Zero  Gb  SetParaBao.o
  g_DataOutTypeFlag          0x0009EB77  gp+0x0663           1         Zero  Gb  SetParaBao.o
  g_DynamicInertialSysAlign  0x0008AF48                  1 000      8  Zero  Gb  main.o
  g_GNSSData_In_Use          0x0008C070                    112      8  Zero  Gb  main.o
  g_InertialSysAlign         0x0008B330                    824      8  Zero  Gb  main.o
  g_InitBind                 0x0008C170                     64      8  Zero  Gb  main.o
  g_Kalman                   0x00084180                 15 128      8  Zero  Gb  main.o
  g_LEDIndicatorState        0x0009EB76  gp+0x0662           1         Zero  Gb  INS_Data.o
  g_Navi                     0x0008A470                  2 776      8  Zero  Gb  main.o
  g_SelfTest                 0x0008BF80                    128      8  Zero  Gb  main.o
  g_StartUpdateFirm          0x0009EB75  gp+0x0661           1         Zero  Gb  main.o
  g_SysVar                   0x0008BD30                    408      8  Zero  Gb  main.o
  g_UpdateBackFlag           0x0009EB83  gp+0x066F           1         Init  Gb  SetParaBao.o
  g_UpdateSuccessful         0x0009EB74  gp+0x0660           1         Zero  Gb  SetParaBao.o
  g_console_uart             0x0009EB2C  gp+0x0618           4      4  Zero  Lc  hpm_debug_console.c.o
  g_queue                    0x000A7BA8                 33 540      4  Init  Gb  hpm_sdmmc_disk.c.o
  g_ucSystemResetFlag        0x0009EB73  gp+0x065F           1         Zero  Gb  SetParaBao.o
  galgrithomresultTx         0x0009E9E8  gp+0x04D4          44      4  Zero  Gb  gdwatch.o
  gcanInfo                   0x0009EA3C  gp+0x0528          38      4  Zero  Gb  fpgad.o
  gdriverdatalist            0x0008C2A8                 59 608      4  Zero  Gb  gdwatch.o
  gdriversettings            0x0009E8F8  gp+0x03E4         138      4  Zero  Gb  gdwatch.o
  gdriverspacket             0x0009EB28  gp+0x0614           4      4  Zero  Gb  gdwatch.o
  gfpgadata                  0x0009DF14  gp-0x0600         400      4  Zero  Gb  fpgad.o
  gfpgadataPredoSend         0x0009E0A4  gp-0x0470         336      4  Zero  Gb  InsTestingEntry.o
  gfpgasenddatalen           0x0009EB24  gp+0x0610           4      4  Zero  Gb  fpgad.o
  gframeParsebuf             0x0009CB84                  1 024      4  Zero  Gb  uart.o
  gframeindex                0x0009EB20  gp+0x060C           4      4  Zero  Gb  fpgad.o
  ggdworgdata_packet         0x0009EB1C  gp+0x0608           4      4  Zero  Gb  gdwatch.o
  ggpsorgdata                0x0009E864  gp+0x0350         148      4  Zero  Gb  frame_analysis.o
  ginputdata                 0x0009E564  gp+0x0050         270      4  Zero  Gb  InsTestingEntry.o
  gins912data                0x0009E454  gp-0x00C0         270      4  Zero  Gb  InsTestingEntry.o
  gins912outputmode          0x0009EB18  gp+0x0604           4      4  Zero  Gb  INS_Output.o
  gnavout                    0x0008C000                    112      8  Zero  Gb  INS_Data.o
  gpagedata                  0x0009E344  gp-0x01D0         272      4  Zero  Gb  fpgad.o
  gprotocol_send_baudrate    0x000A7B98                      4      4  Init  Gb  datado.o
  grxbuffer                  0x0009AB80                  4 098      4  Zero  Gb  uart.o
  grxlen                     0x0009EB14  gp+0x0600           4      4  Zero  Gb  uart.o
  grxst                      0x0009EB10  gp+0x05FC           4      4  Zero  Gb  uart.o
  gw                         0x0008C1E0                     24      8  Zero  Gb  InsTestingEntry.o
  hINSData                   0x0009E774  gp+0x0260         240      4  Zero  Gb  INS_Data.o
  hSetting                   0x0009D1E4                    596      4  Zero  Gb  computerFrameParse.o
  has_card_initialized.0     0x0009EB72  gp+0x065E           1         Zero  Lc  hpm_sdmmc_disk.c.o
  hello_str                  0x0009BB84                  2 048      4  Zero  Gb  sd_fatfs.o
  hpm_core_clock             0x0009EB0C  gp+0x05F8           4      4  Zero  Gb  hpm_clock_drv.c.o
  infoArr                    0x0009EA64  gp+0x0550          32      4  Zero  Gb  fpgad.o
  init_done                  0x0009EB71  gp+0x065D           1         Zero  Lc  ff_queue.o
  inscanruning               0x0009EB70  gp+0x065C           1         Zero  Gb  readpaoche.o
  paochedata                 0x0008BB08                    552      8  Zero  Gb  InsTestingEntry.o
  r_Gyro                     0x0008C1C8                     24      8  Zero  Gb  InsTestingEntry.o
  r_LastGyro                 0x0008C1B0                     24      8  Zero  Gb  InsTestingEntry.o
  r_TAFEAG8_buf              0x0009D8D0                    578      4  Zero  Gb  InsTestingEntry.o
  r_acc                      0x0009EAD4  gp+0x05C0          12      4  Zero  Gb  InsTestingEntry.o
  r_fog                      0x0009EAC8  gp+0x05B4          12      4  Zero  Gb  InsTestingEntry.o
  rcv_state                  0x0009EB82  gp+0x066E           1         Init  Gb  readpaoche.o
  read_count                 0x0009EB08  gp+0x05F4           4      4  Zero  Gb  sd_fatfs.o
  read_ms                    0x0009EB04  gp+0x05F0           4      4  Zero  Gb  sd_fatfs.o
  rs422_frame                0x0009E984  gp+0x0470         100      4  Zero  Gb  frame_analysis.o
  s_fileBB00                 0x0008B8B8                    592      8  Zero  Gb  sd_fatfs.o
  s_fileCOM3                 0x0008B668                    592      8  Zero  Gb  sd_fatfs.o
  s_sd                       0x00080000                    216      8  Init  Lc  hpm_sdmmc_disk.c.o
  s_sd_aligned_buf           0x00080180                 16 384     64  Zero  Lc  hpm_sdmmc_disk.c.o
  s_sd_disk                  0x0009D438                    588      4  Zero  Gb  sd_fatfs.o
  s_sd_host                  0x01100000                    788      4  Zero  Lc  hpm_sdmmc_disk.c.o
  s_xpi_nor_config           0x0009E674  gp+0x0160         256      4  Zero  Lc  flash.o
  sd_rx_descriptors          0x011003D8                     64      8  Init  Lc  uart.o
  sd_tx_descriptors          0x01100398                     64      8  Init  Lc  uart.o
  sd_uart                    0x0008011C                     68      4  Init  Lc  uart.o
  sd_uart_rx_buf             0x000A4B88                  8 192      4  None  Lc  uart.o
  sd_uart_tx_buf             0x000A6B88                  4 096      4  None  Lc  uart.o
  sd_uart_tx_done            0x000A7B88                      1      4  None  Gb  uart.o
  stSetPara                  0x0009D684                    586      4  Zero  Gb  SetParaBao.o
  start_ticks                0x00080178                      8      8  Zero  Gb  sd_fatfs.o
  stdout                     0x000A7B94                      4      4  Init  Gb  hpm_debug_console.c.o
  tCnt.0                     0x0009BB82                      2      2  Zero  Lc  InsTestingEntry.o
  tCnt.0                     0x0009D8CE                      2      2  Zero  Lc  INS_Output.o
  tCnt.0                     0x0009DB12                      2      2  Zero  Lc  sd_fatfs.o
  test_count                 0x0009EB00  gp+0x05EC           4      4  Zero  Gb  sd_fatfs.o
  test_rx_descriptors        0x01100358                     64      8  Init  Lc  uart.o
  test_tx_descriptors        0x01100318                     64      8  Init  Lc  uart.o
  test_uart                  0x000800D8                     68      4  Init  Lc  uart.o
  test_uart_rx_buf           0x0009EB84  gp+0x0670      16 384      4  None  Lc  uart.o
  test_uart_tx_buf           0x000A2B84                  8 192      4  None  Lc  uart.o
  test_uart_tx_done          0x000A4B84                      1      4  None  Gb  uart.o
  time_base_100ms_Flag       0x0009EAFC  gp+0x05E8           4      4  Zero  Gb  bsp_tim.o
  time_base_100ms_periodic_cnt
                             0x0009EAF8  gp+0x05E4           4      4  Zero  Gb  bsp_tim.o
  time_base_20ms_Flag        0x0009EAF4  gp+0x05E0           4      4  Zero  Gb  bsp_tim.o
  time_base_20ms_periodic_cnt
                             0x0009EAF0  gp+0x05DC           4      4  Zero  Gb  bsp_tim.o
  time_base_periodic_cnt     0x0009EAEC  gp+0x05D8           4      4  Zero  Gb  bsp_tim.o
  uiLastBaoInDex.2           0x000A7B90                      4      4  Init  Lc  SetParaBao.o
  uiLen.0                    0x0009EAE8  gp+0x05D4           4      4  Zero  Lc  uart.o
  uiOffsetAddr.0             0x0009EAE4  gp+0x05D0           4      4  Zero  Lc  SetParaBao.o
  work                       0x0009DB14                    512      4  Zero  Gb  sd_fatfs.o
  xgpsTime                   0x0009EAE0  gp+0x05CC           4      4  Zero  Lc  frame_analysis.o
  xgpsTrueTime               0x00080174                      4      4  Zero  Lc  frame_analysis.o

Read-write data symbols by address:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA4
  
     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x00080000             s_sd                              216      8  Init  Lc  hpm_sdmmc_disk.c.o
  0x000800D8             test_uart                          68      4  Init  Lc  uart.o
  0x0008011C             sd_uart                            68      4  Init  Lc  uart.o
  0x00080160             __SEGGER_RTL_global_locale
                                                            20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x00080160             __RAL_global_locale                20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x00080174             xgpsTrueTime                        4      4  Zero  Lc  frame_analysis.o
  0x00080178             start_ticks                         8      8  Zero  Gb  sd_fatfs.o
  0x00080180             s_sd_aligned_buf               16 384     64  Zero  Lc  hpm_sdmmc_disk.c.o
  0x00084180             g_Kalman                       15 128      8  Zero  Gb  main.o
  0x00087C98             g_Compen                       10 200      8  Zero  Gb  main.o
  0x0008A470             g_Navi                          2 776      8  Zero  Gb  main.o
  0x0008AF48             g_DynamicInertialSysAlign       1 000      8  Zero  Gb  main.o
  0x0008B330             g_InertialSysAlign                824      8  Zero  Gb  main.o
  0x0008B668             s_fileCOM3                        592      8  Zero  Gb  sd_fatfs.o
  0x0008B8B8             s_fileBB00                        592      8  Zero  Gb  sd_fatfs.o
  0x0008BB08             paochedata                        552      8  Zero  Gb  InsTestingEntry.o
  0x0008BD30             g_SysVar                          408      8  Zero  Gb  main.o
  0x0008BEC8             g_Align                           184      8  Zero  Gb  main.o
  0x0008BF80             g_SelfTest                        128      8  Zero  Gb  main.o
  0x0008C000             gnavout                           112      8  Zero  Gb  INS_Data.o
  0x0008C070             g_GNSSData_In_Use                 112      8  Zero  Gb  main.o
  0x0008C0E0             Gw                                 72      8  Zero  Gb  InsTestingEntry.o
  0x0008C128             Aw                                 72      8  Zero  Gb  InsTestingEntry.o
  0x0008C170             g_InitBind                         64      8  Zero  Gb  main.o
  0x0008C1B0             r_LastGyro                         24      8  Zero  Gb  InsTestingEntry.o
  0x0008C1C8             r_Gyro                             24      8  Zero  Gb  InsTestingEntry.o
  0x0008C1E0             gw                                 24      8  Zero  Gb  InsTestingEntry.o
  0x0008C1F8             gWm                                24      8  Zero  Gb  InsTestingEntry.o
  0x0008C210             gW_real                            24      8  Zero  Gb  InsTestingEntry.o
  0x0008C228             aw                                 24      8  Zero  Gb  InsTestingEntry.o
  0x0008C240             aWm                                24      8  Zero  Gb  InsTestingEntry.o
  0x0008C258             aW_real                            24      8  Zero  Gb  InsTestingEntry.o
  0x0008C270             LastAcc                            24      8  Zero  Gb  InsTestingEntry.o
  0x0008C288             Acc                                24      8  Zero  Gb  InsTestingEntry.o
  0x0008C2A0             end_ticks                           8      8  Zero  Gb  sd_fatfs.o
  0x0008C2A8             gdriverdatalist                59 608      4  Zero  Gb  gdwatch.o
  0x0009AB80             grxbuffer                       4 098      4  Zero  Gb  uart.o
  0x0009BB82             tCnt.0                              2      2  Zero  Lc  InsTestingEntry.o
  0x0009BB84             hello_str                       2 048      4  Zero  Gb  sd_fatfs.o
  0x0009C384             fpgatesttxt                     2 048      4  Zero  Gb  INS_Output.o
  0x0009CB84             gframeParsebuf                  1 024      4  Zero  Gb  uart.o
  0x0009CF84             DirBuf                            608      4  Zero  Lc  ff.c.o
  0x0009D1E4             hSetting                          596      4  Zero  Gb  computerFrameParse.o
  0x0009D438             s_sd_disk                         588      4  Zero  Gb  sd_fatfs.o
  0x0009D684             stSetPara                         586      4  Zero  Gb  SetParaBao.o
  0x0009D8CE             tCnt.0                              2      2  Zero  Lc  INS_Output.o
  0x0009D8D0             r_TAFEAG8_buf                     578      4  Zero  Gb  InsTestingEntry.o
  0x0009DB12             tCnt.0                              2      2  Zero  Lc  sd_fatfs.o
  0x0009DB14             work                              512      4  Zero  Gb  sd_fatfs.o
  0x0009DD14  gp-0x0800  LfnBuf                            512      4  Zero  Lc  ff.c.o
  0x0009DF14  gp-0x0600  gfpgadata                         400      4  Zero  Gb  fpgad.o
  0x0009E0A4  gp-0x0470  gfpgadataPredoSend                336      4  Zero  Gb  InsTestingEntry.o
  0x0009E1F4  gp-0x0320  BB00SdData                        336      4  Zero  Gb  InsTestingEntry.o
  0x0009E344  gp-0x01D0  gpagedata                         272      4  Zero  Gb  fpgad.o
  0x0009E454  gp-0x00C0  gins912data                       270      4  Zero  Gb  InsTestingEntry.o
  0x0009E562  gp+0x004E  SDCnt.0                             2      2  Zero  Lc  gd32f4xx_it.o
  0x0009E564  gp+0x0050  ginputdata                        270      4  Zero  Gb  InsTestingEntry.o
  0x0009E672  gp+0x015E  Fsid                                2      2  Zero  Lc  ff.c.o
  0x0009E674  gp+0x0160  s_xpi_nor_config                  256      4  Zero  Lc  flash.o
  0x0009E774  gp+0x0260  hINSData                          240      4  Zero  Gb  INS_Data.o
  0x0009E864  gp+0x0350  ggpsorgdata                       148      4  Zero  Gb  frame_analysis.o
  0x0009E8F8  gp+0x03E4  gdriversettings                   138      4  Zero  Gb  gdwatch.o
  0x0009E982  gp+0x046E  COM3Flag.3                          2      2  Zero  Lc  sd_fatfs.o
  0x0009E984  gp+0x0470  rs422_frame                       100      4  Zero  Gb  frame_analysis.o
  0x0009E9E8  gp+0x04D4  galgrithomresultTx                 44      4  Zero  Gb  gdwatch.o
  0x0009EA14  gp+0x0500  FatFs                              40      4  Zero  Lc  ff.c.o
  0x0009EA3C  gp+0x0528  gcanInfo                           38      4  Zero  Gb  fpgad.o
  0x0009EA62  gp+0x054E  BB00Flag.2                          2      2  Zero  Lc  sd_fatfs.o
  0x0009EA64  gp+0x0550  infoArr                            32      4  Zero  Gb  fpgad.o
  0x0009EA84  gp+0x0570  __SEGGER_RTL_aSigTab               24      4  Zero  Lc  execops.o (libc_rv32gc_d_balanced.a)
  0x0009EA9C  gp+0x0588  StrMiddleWare                      24      4  Zero  Gb  InsTestingEntry.o
  0x0009EAB4  gp+0x05A0  canin                              20      4  Zero  Gb  InsTestingEntry.o
  0x0009EAC8  gp+0x05B4  r_fog                              12      4  Zero  Gb  InsTestingEntry.o
  0x0009EAD4  gp+0x05C0  r_acc                              12      4  Zero  Gb  InsTestingEntry.o
  0x0009EAE0  gp+0x05CC  xgpsTime                            4      4  Zero  Lc  frame_analysis.o
  0x0009EAE4  gp+0x05D0  uiOffsetAddr.0                      4      4  Zero  Lc  SetParaBao.o
  0x0009EAE8  gp+0x05D4  uiLen.0                             4      4  Zero  Lc  uart.o
  0x0009EAEC  gp+0x05D8  time_base_periodic_cnt              4      4  Zero  Gb  bsp_tim.o
  0x0009EAF0  gp+0x05DC  time_base_20ms_periodic_cnt
                                                             4      4  Zero  Gb  bsp_tim.o
  0x0009EAF4  gp+0x05E0  time_base_20ms_Flag                 4      4  Zero  Gb  bsp_tim.o
  0x0009EAF8  gp+0x05E4  time_base_100ms_periodic_cnt
                                                             4      4  Zero  Gb  bsp_tim.o
  0x0009EAFC  gp+0x05E8  time_base_100ms_Flag                4      4  Zero  Gb  bsp_tim.o
  0x0009EB00  gp+0x05EC  test_count                          4      4  Zero  Gb  sd_fatfs.o
  0x0009EB04  gp+0x05F0  read_ms                             4      4  Zero  Gb  sd_fatfs.o
  0x0009EB08  gp+0x05F4  read_count                          4      4  Zero  Gb  sd_fatfs.o
  0x0009EB0C  gp+0x05F8  hpm_core_clock                      4      4  Zero  Gb  hpm_clock_drv.c.o
  0x0009EB10  gp+0x05FC  grxst                               4      4  Zero  Gb  uart.o
  0x0009EB14  gp+0x0600  grxlen                              4      4  Zero  Gb  uart.o
  0x0009EB18  gp+0x0604  gins912outputmode                   4      4  Zero  Gb  INS_Output.o
  0x0009EB1C  gp+0x0608  ggdworgdata_packet                  4      4  Zero  Gb  gdwatch.o
  0x0009EB20  gp+0x060C  gframeindex                         4      4  Zero  Gb  fpgad.o
  0x0009EB24  gp+0x0610  gfpgasenddatalen                    4      4  Zero  Gb  fpgad.o
  0x0009EB28  gp+0x0614  gdriverspacket                      4      4  Zero  Gb  gdwatch.o
  0x0009EB2C  gp+0x0618  g_console_uart                      4      4  Zero  Lc  hpm_debug_console.c.o
  0x0009EB30  gp+0x061C  g_DataOutTime                       4      4  Zero  Gb  SetParaBao.o
  0x0009EB34  gp+0x0620  g_CAN_Timeout_Start_flag            4      4  Zero  Gb  INS_Init.o
  0x0009EB38  gp+0x0624  g_CAN_Timeout_Cnt                   4      4  Zero  Gb  INS_Init.o
  0x0009EB3C  gp+0x0628  fpga_syn_count                      4      4  Zero  Gb  INS_Init.o
  0x0009EB40  gp+0x062C  fpga_syn_btw_last                   4      4  Zero  Gb  fpgad.o
  0x0009EB44  gp+0x0630  fpga_syn_btw                        4      4  Zero  Gb  fpgad.o
  0x0009EB48  gp+0x0634  fpga_syn_NAVI_btw                   4      4  Zero  Gb  fpgad.o
  0x0009EB4C  gp+0x0638  fpga_loop_count                     4      4  Zero  Gb  INS_Init.o
  0x0009EB50  gp+0x063C  flag.1                              4      4  Zero  Lc  SetParaBao.o
  0x0009EB54  gp+0x0640  current_reload                      4      4  Zero  Gb  timer.o
  0x0009EB58  gp+0x0644  axisInfo                            4      4  Zero  Gb  frame_analysis.o
  0x0009EB5C  gp+0x0648  __SEGGER_RTL_stdout_file            4      4  Zero  Lc  hpm_debug_console.c.o
  0x0009EB60  gp+0x064C  NaviCompute_do_count                4      4  Zero  Gb  datado.o
  0x0009EB64  gp+0x0650  en                                  4      4  Zero  Lc  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  0x0009EB68  gp+0x0654  __SEGGER_RTL_locale_ptr             4      4  Zero  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x0009EB6C  gp+0x0658  __SEGGER_RTL_heap_globals           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  0x0009EB70  gp+0x065C  inscanruning                        1         Zero  Gb  readpaoche.o
  0x0009EB71  gp+0x065D  init_done                           1         Zero  Lc  ff_queue.o
  0x0009EB72  gp+0x065E  has_card_initialized.0              1         Zero  Lc  hpm_sdmmc_disk.c.o
  0x0009EB73  gp+0x065F  g_ucSystemResetFlag                 1         Zero  Gb  SetParaBao.o
  0x0009EB74  gp+0x0660  g_UpdateSuccessful                  1         Zero  Gb  SetParaBao.o
  0x0009EB75  gp+0x0661  g_StartUpdateFirm                   1         Zero  Gb  main.o
  0x0009EB76  gp+0x0662  g_LEDIndicatorState                 1         Zero  Gb  INS_Data.o
  0x0009EB77  gp+0x0663  g_DataOutTypeFlag                   1         Zero  Gb  SetParaBao.o
  0x0009EB78  gp+0x0664  g_Com3WriteSdFlag                   1         Zero  Gb  uart.o
  0x0009EB79  gp+0x0665  g_BB00WriteSdFlag                   1         Zero  Gb  InsTestingEntry.o
  0x0009EB7A  gp+0x0666  fpga_syn                            1         Zero  Gb  INS_Init.o
  0x0009EB7B  gp+0x0667  fatfs_result                        1         Zero  Gb  sd_fatfs.o
  0x0009EB7C  gp+0x0668  SetSdOperateType                    1         Zero  Gb  SetParaBao.o
  0x0009EB7D  gp+0x0669  SetSdFlieType                       1         Zero  Gb  SetParaBao.o
  0x0009EB7E  gp+0x066A  CurrVol                             1         Zero  Lc  ff.c.o
  0x0009EB7F  gp+0x066B  Count1                              1         Zero  Gb  InsTestingEntry.o
  0x0009EB80  gp+0x066C  CollieryOperate_uartsend_Flag
                                                             1         Zero  Gb  main.o
  0x0009EB81  gp+0x066D  CollieryOperate_Flag                1         Zero  Gb  SetParaBao.o
  0x0009EB82  gp+0x066E  rcv_state                           1         Init  Gb  readpaoche.o
  0x0009EB83  gp+0x066F  g_UpdateBackFlag                    1         Init  Gb  SetParaBao.o
  0x0009EB84  gp+0x0670  test_uart_rx_buf               16 384      4  None  Lc  uart.o
  0x000A2B84             test_uart_tx_buf                8 192      4  None  Lc  uart.o
  0x000A4B84             test_uart_tx_done                   1      4  None  Gb  uart.o
  0x000A4B88             sd_uart_rx_buf                  8 192      4  None  Lc  uart.o
  0x000A6B88             sd_uart_tx_buf                  4 096      4  None  Lc  uart.o
  0x000A7B88             sd_uart_tx_done                     1      4  None  Gb  uart.o
  0x000A7B89             Return_Colliery_Operate_Flag
                                                             1         Init  Gb  main.o
  0x000A7B8C             dma_done                            1      4  None  Lc  uart_dma.o
  0x000A7B90             uiLastBaoInDex.2                    4      4  Init  Lc  SetParaBao.o
  0x000A7B94             stdout                              4      4  Init  Gb  hpm_debug_console.c.o
  0x000A7B98             gprotocol_send_baudrate             4      4  Init  Gb  datado.o
  0x000A7B9C             Com3FileName                        4      4  Init  Gb  sd_fatfs.o
  0x000A7BA0             BB00FileName                        4      4  Init  Gb  sd_fatfs.o
  0x000A7BA8             g_queue                        33 540      4  Init  Gb  hpm_sdmmc_disk.c.o
  0x01100000             s_sd_host                         788      4  Zero  Lc  hpm_sdmmc_disk.c.o
  0x01100318             test_tx_descriptors                64      8  Init  Lc  uart.o
  0x01100358             test_rx_descriptors                64      8  Init  Lc  uart.o
  0x01100398             sd_tx_descriptors                  64      8  Init  Lc  uart.o
  0x011003D8             sd_rx_descriptors                  64      8  Init  Lc  uart.o

Read-write data symbols by descending size:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA4
  
  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  gdriverdatalist                59 608      4  Zero  Gb  gdwatch.o
  g_queue                        33 540      4  Init  Gb  hpm_sdmmc_disk.c.o
  s_sd_aligned_buf               16 384     64  Zero  Lc  hpm_sdmmc_disk.c.o
  test_uart_rx_buf               16 384      4  None  Lc  uart.o
  g_Kalman                       15 128      8  Zero  Gb  main.o
  g_Compen                       10 200      8  Zero  Gb  main.o
  sd_uart_rx_buf                  8 192      4  None  Lc  uart.o
  test_uart_tx_buf                8 192      4  None  Lc  uart.o
  grxbuffer                       4 098      4  Zero  Gb  uart.o
  sd_uart_tx_buf                  4 096      4  None  Lc  uart.o
  g_Navi                          2 776      8  Zero  Gb  main.o
  fpgatesttxt                     2 048      4  Zero  Gb  INS_Output.o
  hello_str                       2 048      4  Zero  Gb  sd_fatfs.o
  gframeParsebuf                  1 024      4  Zero  Gb  uart.o
  g_DynamicInertialSysAlign       1 000      8  Zero  Gb  main.o
  g_InertialSysAlign                824      8  Zero  Gb  main.o
  s_sd_host                         788      4  Zero  Lc  hpm_sdmmc_disk.c.o
  DirBuf                            608      4  Zero  Lc  ff.c.o
  hSetting                          596      4  Zero  Gb  computerFrameParse.o
  s_fileBB00                        592      8  Zero  Gb  sd_fatfs.o
  s_fileCOM3                        592      8  Zero  Gb  sd_fatfs.o
  s_sd_disk                         588      4  Zero  Gb  sd_fatfs.o
  stSetPara                         586      4  Zero  Gb  SetParaBao.o
  r_TAFEAG8_buf                     578      4  Zero  Gb  InsTestingEntry.o
  paochedata                        552      8  Zero  Gb  InsTestingEntry.o
  LfnBuf                            512      4  Zero  Lc  ff.c.o
  work                              512      4  Zero  Gb  sd_fatfs.o
  g_SysVar                          408      8  Zero  Gb  main.o
  gfpgadata                         400      4  Zero  Gb  fpgad.o
  BB00SdData                        336      4  Zero  Gb  InsTestingEntry.o
  gfpgadataPredoSend                336      4  Zero  Gb  InsTestingEntry.o
  gpagedata                         272      4  Zero  Gb  fpgad.o
  ginputdata                        270      4  Zero  Gb  InsTestingEntry.o
  gins912data                       270      4  Zero  Gb  InsTestingEntry.o
  s_xpi_nor_config                  256      4  Zero  Lc  flash.o
  hINSData                          240      4  Zero  Gb  INS_Data.o
  s_sd                              216      8  Init  Lc  hpm_sdmmc_disk.c.o
  g_Align                           184      8  Zero  Gb  main.o
  ggpsorgdata                       148      4  Zero  Gb  frame_analysis.o
  gdriversettings                   138      4  Zero  Gb  gdwatch.o
  g_SelfTest                        128      8  Zero  Gb  main.o
  g_GNSSData_In_Use                 112      8  Zero  Gb  main.o
  gnavout                           112      8  Zero  Gb  INS_Data.o
  rs422_frame                       100      4  Zero  Gb  frame_analysis.o
  Aw                                 72      8  Zero  Gb  InsTestingEntry.o
  Gw                                 72      8  Zero  Gb  InsTestingEntry.o
  sd_uart                            68      4  Init  Lc  uart.o
  test_uart                          68      4  Init  Lc  uart.o
  g_InitBind                         64      8  Zero  Gb  main.o
  sd_rx_descriptors                  64      8  Init  Lc  uart.o
  sd_tx_descriptors                  64      8  Init  Lc  uart.o
  test_rx_descriptors                64      8  Init  Lc  uart.o
  test_tx_descriptors                64      8  Init  Lc  uart.o
  galgrithomresultTx                 44      4  Zero  Gb  gdwatch.o
  FatFs                              40      4  Zero  Lc  ff.c.o
  gcanInfo                           38      4  Zero  Gb  fpgad.o
  infoArr                            32      4  Zero  Gb  fpgad.o
  Acc                                24      8  Zero  Gb  InsTestingEntry.o
  LastAcc                            24      8  Zero  Gb  InsTestingEntry.o
  StrMiddleWare                      24      4  Zero  Gb  InsTestingEntry.o
  __SEGGER_RTL_aSigTab               24      4  Zero  Lc  execops.o (libc_rv32gc_d_balanced.a)
  aW_real                            24      8  Zero  Gb  InsTestingEntry.o
  aWm                                24      8  Zero  Gb  InsTestingEntry.o
  aw                                 24      8  Zero  Gb  InsTestingEntry.o
  gW_real                            24      8  Zero  Gb  InsTestingEntry.o
  gWm                                24      8  Zero  Gb  InsTestingEntry.o
  gw                                 24      8  Zero  Gb  InsTestingEntry.o
  r_Gyro                             24      8  Zero  Gb  InsTestingEntry.o
  r_LastGyro                         24      8  Zero  Gb  InsTestingEntry.o
  __RAL_global_locale                20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_global_locale
                                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  canin                              20      4  Zero  Gb  InsTestingEntry.o
  r_acc                              12      4  Zero  Gb  InsTestingEntry.o
  r_fog                              12      4  Zero  Gb  InsTestingEntry.o
  end_ticks                           8      8  Zero  Gb  sd_fatfs.o
  start_ticks                         8      8  Zero  Gb  sd_fatfs.o
  BB00FileName                        4      4  Init  Gb  sd_fatfs.o
  Com3FileName                        4      4  Init  Gb  sd_fatfs.o
  NaviCompute_do_count                4      4  Zero  Gb  datado.o
  __SEGGER_RTL_heap_globals           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32gc_d_balanced.a)
  __SEGGER_RTL_locale_ptr             4      4  Zero  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_stdout_file            4      4  Zero  Lc  hpm_debug_console.c.o
  axisInfo                            4      4  Zero  Gb  frame_analysis.o
  current_reload                      4      4  Zero  Gb  timer.o
  en                                  4      4  Zero  Lc  heapops.o (heapops_disable_interrupts_locking_rv32gc_d_balanced.a)
  flag.1                              4      4  Zero  Lc  SetParaBao.o
  fpga_loop_count                     4      4  Zero  Gb  INS_Init.o
  fpga_syn_NAVI_btw                   4      4  Zero  Gb  fpgad.o
  fpga_syn_btw                        4      4  Zero  Gb  fpgad.o
  fpga_syn_btw_last                   4      4  Zero  Gb  fpgad.o
  fpga_syn_count                      4      4  Zero  Gb  INS_Init.o
  g_CAN_Timeout_Cnt                   4      4  Zero  Gb  INS_Init.o
  g_CAN_Timeout_Start_flag            4      4  Zero  Gb  INS_Init.o
  g_DataOutTime                       4      4  Zero  Gb  SetParaBao.o
  g_console_uart                      4      4  Zero  Lc  hpm_debug_console.c.o
  gdriverspacket                      4      4  Zero  Gb  gdwatch.o
  gfpgasenddatalen                    4      4  Zero  Gb  fpgad.o
  gframeindex                         4      4  Zero  Gb  fpgad.o
  ggdworgdata_packet                  4      4  Zero  Gb  gdwatch.o
  gins912outputmode                   4      4  Zero  Gb  INS_Output.o
  gprotocol_send_baudrate             4      4  Init  Gb  datado.o
  grxlen                              4      4  Zero  Gb  uart.o
  grxst                               4      4  Zero  Gb  uart.o
  hpm_core_clock                      4      4  Zero  Gb  hpm_clock_drv.c.o
  read_count                          4      4  Zero  Gb  sd_fatfs.o
  read_ms                             4      4  Zero  Gb  sd_fatfs.o
  stdout                              4      4  Init  Gb  hpm_debug_console.c.o
  test_count                          4      4  Zero  Gb  sd_fatfs.o
  time_base_100ms_Flag                4      4  Zero  Gb  bsp_tim.o
  time_base_100ms_periodic_cnt
                                      4      4  Zero  Gb  bsp_tim.o
  time_base_20ms_Flag                 4      4  Zero  Gb  bsp_tim.o
  time_base_20ms_periodic_cnt
                                      4      4  Zero  Gb  bsp_tim.o
  time_base_periodic_cnt              4      4  Zero  Gb  bsp_tim.o
  uiLastBaoInDex.2                    4      4  Init  Lc  SetParaBao.o
  uiLen.0                             4      4  Zero  Lc  uart.o
  uiOffsetAddr.0                      4      4  Zero  Lc  SetParaBao.o
  xgpsTime                            4      4  Zero  Lc  frame_analysis.o
  xgpsTrueTime                        4      4  Zero  Lc  frame_analysis.o
  BB00Flag.2                          2      2  Zero  Lc  sd_fatfs.o
  COM3Flag.3                          2      2  Zero  Lc  sd_fatfs.o
  Fsid                                2      2  Zero  Lc  ff.c.o
  SDCnt.0                             2      2  Zero  Lc  gd32f4xx_it.o
  tCnt.0                              2      2  Zero  Lc  InsTestingEntry.o
  tCnt.0                              2      2  Zero  Lc  INS_Output.o
  tCnt.0                              2      2  Zero  Lc  sd_fatfs.o
  CollieryOperate_Flag                1         Zero  Gb  SetParaBao.o
  CollieryOperate_uartsend_Flag
                                      1         Zero  Gb  main.o
  Count1                              1         Zero  Gb  InsTestingEntry.o
  CurrVol                             1         Zero  Lc  ff.c.o
  Return_Colliery_Operate_Flag
                                      1         Init  Gb  main.o
  SetSdFlieType                       1         Zero  Gb  SetParaBao.o
  SetSdOperateType                    1         Zero  Gb  SetParaBao.o
  dma_done                            1      4  None  Lc  uart_dma.o
  fatfs_result                        1         Zero  Gb  sd_fatfs.o
  fpga_syn                            1         Zero  Gb  INS_Init.o
  g_BB00WriteSdFlag                   1         Zero  Gb  InsTestingEntry.o
  g_Com3WriteSdFlag                   1         Zero  Gb  uart.o
  g_DataOutTypeFlag                   1         Zero  Gb  SetParaBao.o
  g_LEDIndicatorState                 1         Zero  Gb  INS_Data.o
  g_StartUpdateFirm                   1         Zero  Gb  main.o
  g_UpdateBackFlag                    1         Init  Gb  SetParaBao.o
  g_UpdateSuccessful                  1         Zero  Gb  SetParaBao.o
  g_ucSystemResetFlag                 1         Zero  Gb  SetParaBao.o
  has_card_initialized.0              1         Zero  Lc  hpm_sdmmc_disk.c.o
  init_done                           1         Zero  Lc  ff_queue.o
  inscanruning                        1         Zero  Gb  readpaoche.o
  rcv_state                           1         Init  Gb  readpaoche.o
  sd_uart_tx_done                     1      4  None  Gb  uart.o
  test_uart_tx_done                   1      4  None  Gb  uart.o

Read-only data symbols by name:

  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  LfnOfs                     0x80027F84                     13      4  Cnst  Lc  ff.c.o
  SetDir                     0x80010B14                     96      4  Cnst  Lc  InsTestingEntry.o
  __SEGGER_RTL_Moeller_inverse_lut
                             0x80026CFC                  1 024      4  Cnst  Lc  intops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_aPower2       0x800108F0                     72      8  Cnst  Lc  utilops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_ctype_map
                             0x80027298                    128      4  Cnst  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_ascii_ctype_mask
                             0x800283C4                     13      4  Cnst  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale      0x80027214                     12      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_abbrev_day_names
                             0x8002837C                     29      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_abbrev_month_names
                             0x80028308                     49      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_am_pm_indicator
                             0x80028D58                      7      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_data
                             0x80027220                     88      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_date_format
                             0x80028B94                      9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_date_time_format
                             0x80028D48                     15      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_day_names
                             0x80027D98                     58      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_month_names
                             0x80028D60                     87      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_c_locale_time_format
                             0x800286B8                      9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_codeset_ascii
                             0x80027278                     32      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_data_empty_string
                             0x800282A8                      1      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_data_utf8_period
                             0x800105F4                      2      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_ASinACos
                             0x80010840                    112      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_ATan  0x80010780                     96      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_Log   0x80010818                     40      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_SinCos
                             0x800108B0                     64      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_float64_Tan   0x800107E0                     56      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_hex_lc        0x800270FC                     16      4  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_hex_uc        0x8002710C                     16      4  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_ipow10        0x80010938                    160      8  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  __SEGGER_init_data__       0x8002DC28               [77 916]      4  Cnst  Lc  [ Linker created ]
  __SEGGER_init_table__      0x8002DB48                  [224]      4  Cnst  Lc  [ Linker created ]
  axisTab                    0x80010AB0                    100      4  Cnst  Gb  frame_analysis.o
  cst.0                      0x80027A30                     14      4  Cnst  Lc  ff.c.o
  cst32.1                    0x800279F4                     14      4  Cnst  Lc  ff.c.o
  cvt1.1                     0x80027A98                    498      4  Cnst  Lc  ffunicode.c.o
  cvt2.0                     0x80026B68                    188      4  Cnst  Lc  ffunicode.c.o
  defopt.2                   0x800116B0                     16      4  Cnst  Lc  ff.c.o
  driver_num_buf             0x80028C84                      3      4  Cnst  Gb  sd_fatfs.o
  fw_info                    0x8000E010                    128      4  Cnst  Gb  hpm_bootheader.c.o
  header                     0x8000E000                     16      4  Cnst  Gb  hpm_bootheader.c.o
  option                     0x8000D000                     16      4  Cnst  Gb  board.c.o
  s_adc_clk_mux_node         0x80010094                      4      4  Cnst  Lc  hpm_clock_drv.c.o
  s_i2s_clk_mux_node         0x8001050C                      4      4  Cnst  Lc  hpm_clock_drv.c.o
  s_wdgs                     0x80026C64                     16      4  Cnst  Lc  hpm_clock_drv.c.o
  uni2oem936                 0x800116C0                 87 172      4  Cnst  Lc  ffunicode.c.o

Read-only data symbols by address:

     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x8000D000             option                             16      4  Cnst  Gb  board.c.o
  0x8000E000             header                             16      4  Cnst  Gb  hpm_bootheader.c.o
  0x8000E010             fw_info                           128      4  Cnst  Gb  hpm_bootheader.c.o
  0x80010094             s_adc_clk_mux_node                  4      4  Cnst  Lc  hpm_clock_drv.c.o
  0x8001050C             s_i2s_clk_mux_node                  4      4  Cnst  Lc  hpm_clock_drv.c.o
  0x800105F4             __SEGGER_RTL_data_utf8_period
                                                             2      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80010780             __SEGGER_RTL_float64_ATan          96      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x800107E0             __SEGGER_RTL_float64_Tan           56      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x80010818             __SEGGER_RTL_float64_Log           40      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x80010840             __SEGGER_RTL_float64_ASinACos
                                                           112      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x800108B0             __SEGGER_RTL_float64_SinCos
                                                            64      8  Cnst  Lc  floatops.o (libc_rv32gc_d_balanced.a)
  0x800108F0             __SEGGER_RTL_aPower2               72      8  Cnst  Lc  utilops.o (libc_rv32gc_d_balanced.a)
  0x80010938             __SEGGER_RTL_ipow10               160      8  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x80010AB0             axisTab                           100      4  Cnst  Gb  frame_analysis.o
  0x80010B14             SetDir                             96      4  Cnst  Lc  InsTestingEntry.o
  0x800116B0             defopt.2                           16      4  Cnst  Lc  ff.c.o
  0x800116C0             uni2oem936                     87 172      4  Cnst  Lc  ffunicode.c.o
  0x80026B68             cvt2.0                            188      4  Cnst  Lc  ffunicode.c.o
  0x80026C64             s_wdgs                             16      4  Cnst  Lc  hpm_clock_drv.c.o
  0x80026CFC             __SEGGER_RTL_Moeller_inverse_lut
                                                         1 024      4  Cnst  Lc  intops.o (libc_rv32gc_d_balanced.a)
  0x800270FC             __SEGGER_RTL_hex_lc                16      4  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x8002710C             __SEGGER_RTL_hex_uc                16      4  Cnst  Gb  prinops.o (libc_rv32gc_d_balanced.a)
  0x80027214             __SEGGER_RTL_c_locale              12      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80027220             __SEGGER_RTL_c_locale_data
                                                            88      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80027278             __SEGGER_RTL_codeset_ascii
                                                            32      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80027298             __SEGGER_RTL_ascii_ctype_map
                                                           128      4  Cnst  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x800279F4             cst32.1                            14      4  Cnst  Lc  ff.c.o
  0x80027A30             cst.0                              14      4  Cnst  Lc  ff.c.o
  0x80027A98             cvt1.1                            498      4  Cnst  Lc  ffunicode.c.o
  0x80027D98             __SEGGER_RTL_c_locale_day_names
                                                            58      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80027F84             LfnOfs                             13      4  Cnst  Lc  ff.c.o
  0x800282A8             __SEGGER_RTL_data_empty_string
                                                             1      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028308             __SEGGER_RTL_c_locale_abbrev_month_names
                                                            49      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002837C             __SEGGER_RTL_c_locale_abbrev_day_names
                                                            29      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x800283C4             __SEGGER_RTL_ascii_ctype_mask
                                                            13      4  Cnst  Lc  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x800286B8             __SEGGER_RTL_c_locale_time_format
                                                             9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028B94             __SEGGER_RTL_c_locale_date_format
                                                             9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028C84             driver_num_buf                      3      4  Cnst  Gb  sd_fatfs.o
  0x80028D48             __SEGGER_RTL_c_locale_date_time_format
                                                            15      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028D58             __SEGGER_RTL_c_locale_am_pm_indicator
                                                             7      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x80028D60             __SEGGER_RTL_c_locale_month_names
                                                            87      4  Cnst  Gb  mbops.o (mbops_timeops_rv32gc_d_balanced.a)
  0x8002DB48             __SEGGER_init_table__           [224]      4  Cnst  Lc  [ Linker created ]
  0x8002DC28             __SEGGER_init_data__         [77 916]      4  Cnst  Lc  [ Linker created ]

Thread-local data symbols by name:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA4
  
  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  __SEGGER_RTL_errno         0x000A7BA4  tp+0x0000           4      4  Zero  Lc  errno.o (libc_rv32gc_d_balanced.a)

Thread-local data symbols by address:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA4
  
     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x000A7BA4  tp+0x0000  __SEGGER_RTL_errno                  4      4  Zero  Lc  errno.o (libc_rv32gc_d_balanced.a)

Untyped symbols by name:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA4
  
  Symbol name                     Value     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  __AHB_SRAM_segment_end__   0xF0308000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_size__  0x00008000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_start__
                             0xF0300000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_end__
                             0xF0300000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_size__
                             0x00000000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_start__
                             0xF0300000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_end__   0xF40F2000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_size__  0x00002000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_start__
                             0xF40F0000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_used_end__
                             0xF40F0000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_used_size__
                             0x00000000                                ----  Gb  [ Linker created ]
  __APB_SRAM_segment_used_start__
                             0xF40F0000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_end__   0x01100000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_size__  0x00080000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_start__
                             0x01080000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_used_end__
                             0x01080000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_used_size__
                             0x00000000                                ----  Gb  [ Linker created ]
  __AXI_SRAM_segment_used_start__
                             0x01080000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_end__
                             0x80010000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_size__
                             0x00002000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_start__
                             0x8000E000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_end__
                             0x8000E090                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_size__
                             0x00000090                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_start__
                             0x8000E000                                ----  Gb  [ Linker created ]
  __DLM_segment_end__        0x000C0000                                ----  Gb  [ Linker created ]
  __DLM_segment_size__       0x00040000                                ----  Gb  [ Linker created ]
  __DLM_segment_start__      0x00080000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_end__   0x000C0000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_size__  0x00040000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_start__
                             0x00080000                                ----  Gb  [ Linker created ]
  __HEAPSIZE__               0x00004000                                ----  Gb  [ Linker created ]
  __ILM_segment_end__        0x00040000                                ----  Gb  [ Linker created ]
  __ILM_segment_size__       0x00040000                                ----  Gb  [ Linker created ]
  __ILM_segment_start__      0x00000000                                ----  Gb  [ Linker created ]
  __ILM_segment_used_end__   0x00023064                                ----  Gb  [ Linker created ]
  __ILM_segment_used_size__  0x00023064                                ----  Gb  [ Linker created ]
  __ILM_segment_used_start__
                             0x00000000                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_end__
                             0x01140000                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_size__
                             0x00040000                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_start__
                             0x01100000                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_used_end__
                             0x01100418                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_used_size__
                             0x00000418                                ----  Gb  [ Linker created ]
  __NONCACHEABLE_RAM_segment_used_start__
                             0x01100000                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_end__
                             0x8000DC00                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_size__
                             0x00000C00                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_start__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_end__
                             0x8000D010                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_size__
                             0x00000010                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_start__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __SEGGER_RTL_2pow32        0x80010770                      8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_2pow64        0x80010768                      8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __SEGGER_RTL_2powNeg32     0x80010778                      8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  __SHARE_RAM_segment_end__  0x01180000                                ----  Gb  [ Linker created ]
  __SHARE_RAM_segment_size__
                             0x00004000                                ----  Gb  [ Linker created ]
  __SHARE_RAM_segment_start__
                             0x0117C000                                ----  Gb  [ Linker created ]
  __SHARE_RAM_segment_used_end__
                             0x0117C000                                ----  Gb  [ Linker created ]
  __SHARE_RAM_segment_used_size__
                             0x00000000                                ----  Gb  [ Linker created ]
  __SHARE_RAM_segment_used_start__
                             0x0117C000                                ----  Gb  [ Linker created ]
  __STACKSIZE__              0x00004000                                ----  Gb  [ Linker created ]
  __XPI0_segment_end__       0x80074000                                ----  Gb  [ Linker created ]
  __XPI0_segment_size__      0x00064000                                ----  Gb  [ Linker created ]
  __XPI0_segment_start__     0x80010000                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_end__  0x80040D5E                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_size__
                             0x00030D5E                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_start__
                             0x80010000                                ----  Gb  [ Linker created ]
  __app_load_addr__          0x80010000                                ----  Gb  [ Linker created ]
  __app_offset__             0x00002000                                ----  Gb  [ Linker created ]
  __boot_header_length__     0x00000090                                ----  Gb  [ Linker created ]
  __boot_header_load_addr__  0x8000E000                                ----  Gb  [ Linker created ]
  __fsymtab_end              0x80027A96                                ----  Gb  [ Linker created ]
  __fsymtab_start            0x80027A96                                ----  Gb  [ Linker created ]
  __fw_size__                0x00001000                                ----  Gb  [ Linker created ]
  __global_pointer$          0x0009E514  gp+0x0000                     ----  Gb  [ Linker created ]
  __heap_end__               0x000B3EB0                                ----  Gb  [ Linker created ]
  __heap_start__             0x000AFEB0                                ----  Gb  [ Linker created ]
  __noncacheable_end__       0x01140000                                ----  Gb  [ Linker created ]
  __noncacheable_start__     0x01100000                                ----  Gb  [ Linker created ]
  __nor_cfg_option_load_addr__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __rt_init_end              0x80027A96                                ----  Gb  [ Linker created ]
  __rt_init_start            0x80027A96                                ----  Gb  [ Linker created ]
  __rtmsymtab_end            0x80027A96                                ----  Gb  [ Linker created ]
  __rtmsymtab_start          0x80027A96                                ----  Gb  [ Linker created ]
  __share_mem_end__          0x01180000                                ----  Gb  [ Linker created ]
  __share_mem_start__        0x0117C000                                ----  Gb  [ Linker created ]
  __stack_end__              0x000C0000                                ----  Gb  [ Linker created ]
  __startup_complete         0x80010080                             2  Code  Lc  startup.s.o
  __thread_pointer$          0x000A7BA4                                ----  Gb  [ Linker created ]
  __usbh_class_info_end__    0x00080000                                ----  Gb  [ Linker created ]
  __usbh_class_info_start__  0x00080000                                ----  Gb  [ Linker created ]
  __vector_table             0x00000000                  [512]    512  Init  Gb  startup.s.o
  __vsymtab_end              0x80027A96                                ----  Gb  [ Linker created ]
  __vsymtab_start            0x80027A96                                ----  Gb  [ Linker created ]
  _extram_size               0x02000000                                ----  Gb  [ Linker created ]
  _flash_size                0x01000000                                ----  Gb  [ Linker created ]
  _stack                     0x000C0000                                ----  Gb  [ Linker created ]
  _stack_safe                0x000C0000                                ----  Gb  [ Linker created ]
  default_irq_handler        0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_1              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_10             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_100            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_101            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_102            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_103            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_104            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_105            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_106            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_107            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_108            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_109            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_11             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_110            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_111            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_112            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_113            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_114            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_115            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_116            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_117            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_118            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_119            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_12             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_120            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_121            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_122            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_123            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_124            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_125            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_126            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_127            0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_13             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_14             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_15             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_16             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_17             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_18             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_19             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_2              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_20             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_21             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_22             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_23             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_24             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_25             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_26             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_27             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_28             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_29             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_3              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_30             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_31             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_32             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_33             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_34             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_35             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_36             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_37             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_38             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_39             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_4              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_40             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_41             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_42             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_43             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_44             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_45             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_46             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_47             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_48             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_49             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_5              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_50             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_51             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_52             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_53             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_54             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_55             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_56             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_57             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_58             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_59             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_6              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_60             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_61             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_62             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_63             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_64             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_65             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_66             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_67             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_68             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_69             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_7              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_70             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_71             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_72             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_73             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_74             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_75             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_76             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_77             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_78             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_79             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_8              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_80             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_81             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_82             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_83             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_84             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_85             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_86             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_87             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_88             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_89             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_9              0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_90             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_91             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_92             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_93             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_94             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_95             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_96             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_97             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_98             0x0000054E                             4  Init  Wk  startup.s.o
  default_isr_99             0x0000054E                             4  Init  Wk  startup.s.o
  nmi_handler                0x0000054C                    [6]      4  Init  Wk  startup.s.o

Untyped symbols by address:

  Global base (gp) at 0x0009E514
  Thread base (tp) at 0x000A7BA4
  
       Value     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x00000000             __vector_table                  [512]    512  Init  Gb  startup.s.o
  0x00000000             __SHARE_RAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000000             __ILM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x00000000             __ILM_segment_start__                         ----  Gb  [ Linker created ]
  0x00000000             __AXI_SRAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000000             __APB_SRAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000000             __AHB_SRAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000010             __NOR_CFG_OPTION_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000090             __boot_header_length__                        ----  Gb  [ Linker created ]
  0x00000090             __BOOT_HEADER_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000418             __NONCACHEABLE_RAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x0000054C             nmi_handler                       [6]      4  Init  Wk  startup.s.o
  0x0000054E             default_isr_99                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_98                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_97                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_96                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_95                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_94                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_93                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_92                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_91                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_90                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_9                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_89                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_88                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_87                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_86                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_85                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_84                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_83                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_82                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_81                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_80                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_8                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_79                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_78                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_77                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_76                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_75                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_74                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_73                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_72                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_71                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_70                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_7                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_69                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_68                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_67                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_66                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_65                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_64                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_63                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_62                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_61                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_60                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_6                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_59                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_58                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_57                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_56                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_55                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_54                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_53                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_52                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_51                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_50                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_5                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_49                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_48                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_47                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_46                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_45                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_44                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_43                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_42                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_41                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_40                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_4                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_39                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_38                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_37                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_36                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_35                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_34                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_33                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_32                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_31                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_30                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_3                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_29                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_28                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_27                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_26                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_25                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_24                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_23                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_22                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_21                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_20                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_2                              4  Init  Wk  startup.s.o
  0x0000054E             default_isr_19                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_18                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_17                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_16                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_15                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_14                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_13                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_127                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_126                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_125                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_124                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_123                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_122                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_121                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_120                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_12                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_119                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_118                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_117                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_116                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_115                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_114                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_113                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_112                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_111                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_110                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_11                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_109                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_108                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_107                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_106                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_105                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_104                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_103                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_102                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_101                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_100                            4  Init  Wk  startup.s.o
  0x0000054E             default_isr_10                             4  Init  Wk  startup.s.o
  0x0000054E             default_isr_1                              4  Init  Wk  startup.s.o
  0x0000054E             default_irq_handler                        4  Init  Wk  startup.s.o
  0x00000C00             __NOR_CFG_OPTION_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00001000             __fw_size__                                   ----  Gb  [ Linker created ]
  0x00002000             __app_offset__                                ----  Gb  [ Linker created ]
  0x00002000             __BOOT_HEADER_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00002000             __APB_SRAM_segment_size__                     ----  Gb  [ Linker created ]
  0x00004000             __STACKSIZE__                                 ----  Gb  [ Linker created ]
  0x00004000             __SHARE_RAM_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00004000             __HEAPSIZE__                                  ----  Gb  [ Linker created ]
  0x00008000             __AHB_SRAM_segment_size__                     ----  Gb  [ Linker created ]
  0x00023064             __ILM_segment_used_size__                     ----  Gb  [ Linker created ]
  0x00023064             __ILM_segment_used_end__                      ----  Gb  [ Linker created ]
  0x00030D5E             __XPI0_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00040000             __NONCACHEABLE_RAM_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00040000             __ILM_segment_size__                          ----  Gb  [ Linker created ]
  0x00040000             __ILM_segment_end__                           ----  Gb  [ Linker created ]
  0x00040000             __DLM_segment_used_size__                     ----  Gb  [ Linker created ]
  0x00040000             __DLM_segment_size__                          ----  Gb  [ Linker created ]
  0x00064000             __XPI0_segment_size__                         ----  Gb  [ Linker created ]
  0x00080000             __usbh_class_info_start__                     ----  Gb  [ Linker created ]
  0x00080000             __usbh_class_info_end__                       ----  Gb  [ Linker created ]
  0x00080000             __DLM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x00080000             __DLM_segment_start__                         ----  Gb  [ Linker created ]
  0x00080000             __AXI_SRAM_segment_size__                     ----  Gb  [ Linker created ]
  0x0009E514  gp+0x0000  __global_pointer$                             ----  Gb  [ Linker created ]
  0x000A7BA4             __thread_pointer$                             ----  Gb  [ Linker created ]
  0x000AFEB0             __heap_start__                                ----  Gb  [ Linker created ]
  0x000B3EB0             __heap_end__                                  ----  Gb  [ Linker created ]
  0x000C0000             _stack_safe                                   ----  Gb  [ Linker created ]
  0x000C0000             _stack                                        ----  Gb  [ Linker created ]
  0x000C0000             __stack_end__                                 ----  Gb  [ Linker created ]
  0x000C0000             __DLM_segment_used_end__                      ----  Gb  [ Linker created ]
  0x000C0000             __DLM_segment_end__                           ----  Gb  [ Linker created ]
  0x01000000             _flash_size                                   ----  Gb  [ Linker created ]
  0x01080000             __AXI_SRAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x01080000             __AXI_SRAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x01080000             __AXI_SRAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x01100000             __noncacheable_start__                        ----  Gb  [ Linker created ]
  0x01100000             __NONCACHEABLE_RAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x01100000             __NONCACHEABLE_RAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x01100000             __AXI_SRAM_segment_end__                      ----  Gb  [ Linker created ]
  0x01100418             __NONCACHEABLE_RAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x01140000             __noncacheable_end__                          ----  Gb  [ Linker created ]
  0x01140000             __NONCACHEABLE_RAM_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x0117C000             __share_mem_start__                           ----  Gb  [ Linker created ]
  0x0117C000             __SHARE_RAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x0117C000             __SHARE_RAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x0117C000             __SHARE_RAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x01180000             __share_mem_end__                             ----  Gb  [ Linker created ]
  0x01180000             __SHARE_RAM_segment_end__                     ----  Gb  [ Linker created ]
  0x02000000             _extram_size                                  ----  Gb  [ Linker created ]
  0x8000D000             __nor_cfg_option_load_addr__
                                                                       ----  Gb  [ Linker created ]
  0x8000D000             __NOR_CFG_OPTION_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000D000             __NOR_CFG_OPTION_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000D010             __NOR_CFG_OPTION_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x8000DC00             __NOR_CFG_OPTION_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x8000E000             __boot_header_load_addr__                     ----  Gb  [ Linker created ]
  0x8000E000             __BOOT_HEADER_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000E000             __BOOT_HEADER_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000E090             __BOOT_HEADER_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x80010000             __app_load_addr__                             ----  Gb  [ Linker created ]
  0x80010000             __XPI0_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x80010000             __XPI0_segment_start__                        ----  Gb  [ Linker created ]
  0x80010000             __BOOT_HEADER_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x80010080             __startup_complete                         2  Code  Lc  startup.s.o
  0x80010768             __SEGGER_RTL_2pow64                 8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x80010770             __SEGGER_RTL_2pow32                 8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x80010778             __SEGGER_RTL_2powNeg32              8      8  Cnst  Lc  floatasmops_rv.o (libc_rv32gc_d_balanced.a)
  0x80027A96             __vsymtab_start                               ----  Gb  [ Linker created ]
  0x80027A96             __vsymtab_end                                 ----  Gb  [ Linker created ]
  0x80027A96             __rtmsymtab_start                             ----  Gb  [ Linker created ]
  0x80027A96             __rtmsymtab_end                               ----  Gb  [ Linker created ]
  0x80027A96             __rt_init_start                               ----  Gb  [ Linker created ]
  0x80027A96             __rt_init_end                                 ----  Gb  [ Linker created ]
  0x80027A96             __fsymtab_start                               ----  Gb  [ Linker created ]
  0x80027A96             __fsymtab_end                                 ----  Gb  [ Linker created ]
  0x80040D5E             __XPI0_segment_used_end__                     ----  Gb  [ Linker created ]
  0x80074000             __XPI0_segment_end__                          ----  Gb  [ Linker created ]
  0xF0300000             __AHB_SRAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0xF0300000             __AHB_SRAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0xF0300000             __AHB_SRAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0xF0308000             __AHB_SRAM_segment_end__                      ----  Gb  [ Linker created ]
  0xF40F0000             __APB_SRAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0xF40F0000             __APB_SRAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0xF40F0000             __APB_SRAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0xF40F2000             __APB_SRAM_segment_end__                      ----  Gb  [ Linker created ]


***********************************************************************************************
***                                                                                         ***
***                                      LINK SUMMARY                                       ***
***                                                                                         ***
***********************************************************************************************

Memory breakdown:

  166 820 bytes read-only  code    + 
  176 800 bytes read-only  data    = 343 620 bytes read-only (total)
  230 075 bytes read-write data

Region summary:

  Name        Range                     Size                 Used               Unused       Alignment Loss
  ----------  -----------------  -----------  -------------------  -------------------  -------------------
  ILM         00000000-0003ffff      262 144      143 452  54.72%      118 684  45.27%            8   0.00%
  DLM         00080000-000bffff      262 144      229 031  87.37%       33 110  12.63%            3   0.00%
  NONCACHEABLE_RAM
              01100000-0113ffff      262 144        1 044   0.40%      261 100  99.60%            0   0.00%
  NOR_CFG_OPTION
              8000d000-8000dbff        3 072           16   0.52%        3 056  99.48%            0   0.00%
  BOOT_HEADER
              8000e000-8000ffff        8 192          144   1.76%        8 048  98.24%            0   0.00%
  XPI0        80010000-80073fff      409 600      200 008  48.83%      209 576  51.17%           16   0.00%

Link complete: 0 errors, 0 warnings, 0 remarks
