#ifndef ____BSP_TIM_H____
#define ____BSP_TIM_H____

#include <stdio.h>
#include "board.h"
#include "hpm_debug_console.h"
#include "hpm_l1c_drv.h"
#include "hpm_romapi.h"
#include "hpm_clock_drv.h"


extern uint32_t time_periodic_sec_cnt;
extern uint32_t time_periodic_min_cnt;
extern uint32_t time_periodic_hour_cnt;
extern uint8_t time_sync_flag;				//RTC 
extern uint32_t time_base_periodic_cnt;
extern uint32_t time_base_100ms_periodic_cnt;
extern uint32_t time_base_100ms_Flag;
extern uint32_t time_base_20ms_periodic_cnt;
extern uint32_t time_base_20ms_Flag;

void bsp_tim_init(void);



#endif //____BSP_TIM_H____
