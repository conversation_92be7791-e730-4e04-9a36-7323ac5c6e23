#ifndef _CONST_H
#define _CONST_H

#include "appmain.h"
#include "deviceconfig.h"

/*****************************************************文件说明******************************************************************************/
/*文件名称：CONST.h                                                                                                                        */
/*版本号：  Ver 0.1                                                                                                                        */
/*编写日期/时间：                                                                                                */
/*编写人：                                                                                                                             */
/*包含文件：无                                                                                                                             */
/*测试用例：由GNSSlocusGen.m文件生成整套软件系统测试用例，单个模块测试用例暂缺                                                              */
/*说明：本文件定义了程序中所需的常量。（此文件为初始测试版本，文件中所定义参数常量仅供程序设计人员参考选用）                               */
/*******************************************************************************************************************************************/
#define   Null                    (0x0) 
//程序中绝对值最小的数据定义
#define   MIN_DATA                (1.0e-18)                              //最小数据定义，用于除零保护
//矩阵求逆函数中所能处理的最大矩阵行、列数定义(仅矩阵求逆使用)
#define   MAX_MAT_DIM             (7)                                   //用于限定矩阵求逆函数的最大处理能力
#define   MAX_LEASTSQUARE_NUM     (8)

/*****************************************************逻辑常量定义**************************************************************************/
//逻辑判断常量定义
#define   YES                     (0x55)                                    //表示逻辑“是” 
#define   NO                      (0xAA)                                    //表示逻辑“否”
      

/**************************************地球相关常量参数定义，采用WGS84参考旋转椭球体参数（同GNSS）*******************************************/
#define   WIE                   (7.292115147e-5)                           //地球自转角速率定义，单位：rad/s
//#define   PI                    (3.1415926535897932384626433832795)        //圆周率π，无量纲
#define   PIDIV2                (1.5707963267948966192313216916398)        //表示π/2,无量纲
#define   PIMUL2                (6.283185307179586476925286766559)         //表示2 * π,无量纲
#define   R2D                   (57.295779513082320876798154814105)        //弧度转度(180.0/pi)，无量纲
#define   D2R                   (1.7453292519943295769236907684886e-2)    //度转弧度(pi/180.0)，无量纲
#define   F                     (3.3528131778969144060323814696721e-3)    //(1.0/298.257)参考旋转椭球体的扁率，无量纲
#define   G0                    (9.78049)                                  //标准重力加速度，单位：m/s2
#define   G                     (9.78815815671269)                          //标定时采用的重力加速度当量，单位：m/s2
#define   RE                    (6378137.0)                                //地球半长轴半径，单位：m
#define   INVRE                 (1.5678559428873979972521756744955e-7)     //地球半长轴半径的倒数，单位：1/m
#define   NATUTICALMILE         (1853.2)                                   //1海里=1853.2米
#define   KTS                   (1853.2)                                   //1节=1海里/小时(Knots per hour)
/******************************************************标定参数补偿相关常量定义**********************************************************************/                   
#define NUM_TEMP_SAM                         (12)           //温度采集点的个数

//温度计数据当量
#define TEMP_TRANS_FACTOR         (0.0625)               //所有温度传感器相同 ,修改，QXF，20230926
#define   TIME_VG                 (0.002)                //垂直陀螺计算周期，单位：s
//启动过程补偿相关参数
#define TIME_COLD_START_TRIGGER      (3.0)
//#define TIME_COLD_START_COMPEN       (600.0)
//#define TIME_COLD_START_COMPEN_GYRO  (300.0)
#define COLD_START_TEMP_DIFF         (2.5)
/**************************************************惯性导航相关常量定义*********************************************************************/
//默认速度位置信息定义
#define   DEFAULT_LATI            (39.898717)                                 //纬度默认值，单位：°
#define   DEFAULT_LOGI            (106.10659)                                //经度默认值，单位：°
#define   DEFAULT_HEI             (160.0)                                    //高度默认值，单位：m
#define   DEFAULT_VN              (0.0)                                      //北速默认值，单位：m/s
#define   DEFAULT_VU              (0.0)                                      //天速默认值，单位：m/s
#define   DEFAULT_VE              (0.0)                                      //东速默认值，单位：m/s
//系统工作阶段定义
#define   PHASE_SYS_INIT           (0x70)                                    //开机上电后的系统初始化阶段
#define   PHASE_STANDBY            (0x71)                                    //开机上电后的待机阶段
//#define   PHASE_SELF_TEST          (0x72)                                    //自检阶段
#define   PHASE_CALI               (0x73)                                    //标定阶段
#define   PHASE_FAIL               (0x74)                                    //故障阶段
#define   PHASE_COARSE_ALIGN       (0x75)                                    //粗对准阶段
#define   PHASE_FINE_ALIGN         (0x76)                                    //精对准阶段
#define   PHASE_INS_NAVI           (0x77)                                    //纯惯性导航阶段
#define   PHASE_INTEGRATED_NAVI    (0x78)                                    //组合导航阶段
#define   PHASE_ZERO_SPEED         (0x79)
#define   PHASE_DR_INTEGRATED_NAVI (0x80)
#define   PHASE_OPEN_LOOP          (0x81)
#define   PHASE_DRIFT_FAI_CALI     (0x82)
#define   PHASE_COMPASS            (0x83)
//系统相关时间常量定义
#define   TIME_NAVI                 (0.01)                         //惯性导航计算周期，单位：s
#define   TIME_NAVI_HALF            (0.005)
#define   TIME_FILTER               (0.2)                           //Kalman滤波周期，单位：s 
#define   TIME_GNSS_LOST            (1.5)
#define   TIME_START_DELAY          (5.0)      //开始对准前的时间延迟，用于避开IMU上电的启动过程的异常输出值
#define   TIME_COARSE_ALIGN         (180.0)    //粗对准开始时间，单位：s,原300即5min。   20231019
#define   TIME_FAST_ALIGN           (25.0)
#define   TIME_FINE_ALIGN           (300.0)
//导航过程中的野值判据
#define   THRESHOLD_GYRO          (800.0)     //陀螺单轴输出绝对值大于800°/s,则判定为野值，《产品技术协议书》中范围是±400°/s ,但因飞行测试超400量程出问题而改，光纤陀螺器件设计范围可达±1000°/s无问题。          
#define   THRESHOLD_ACC           (30.0 * G0) //加速度计单轴输出绝对值大于30个g0,则判定为野值
#define   COUNT_IMU_FAIL          (100)
//计算机故障判定，导航中断停止持续时间
//#define  TIME_NO_NAVI_INTERRUPT   (0.2)
         
//判断惯导中是否引入GNSS数据的阈值判据
//常规判据       
#ifdef code_test_gyc_old
#define   THRESHOLD_VIS_NUM       (10)                                        //GNSS可见星数阈值，少于4颗则不引入该帧GNSS数据//实际未使用
#define   THRESHOLD_HDOP          (3.5)                                       //GNSS HDOP值阈值，大于4则不引入该帧GNSS数据
#endif
#ifdef code_test_gyc_gao_chen_20240708
#define   THRESHOLD_VIS_NUM       (4)                                        //GNSS可见星数阈值，少于4颗则不引入该帧GNSS数据//实际未使用
#define   THRESHOLD_HDOP          (5.0)                                       //GNSS HDOP值阈值，大于4则不引入该帧GNSS数据
#endif
#define   THRESHOLD_VALID_3S      (30)
//速度野值相对值判据
#define   THRESHOLD_VN_DIFF       (1.5)                                      //GNSS北速阈值，如果GNSS速度与同时刻的惯导速度相差1.5米/s,则不引入该帧GNSS数据//实际未使用
#define   THRESHOLD_VE_DIFF       (1.5)                                      //GNSS东速阈值，如果GNSS速度与同时刻的惯导速度相差1.5米/s,则不引入该帧GNSS数据//实际未使用

//GPS速度野值绝对值判据
#define   THRESHOLD_VN            (327.0)                                      //GNSS北速阈值
#define   THRESHOLD_VU            (327.0)                                      //GNSS天速阈值
#define   THRESHOLD_VE            (327.0)                                      //GNSS东速阈值

//GPS位置野值相对值判据
#define THRESHOLD_POS_ERROR       (3.0)   //单位：m
//GPS高度野值相对值判据
#define THRESHOLD_HEIGHT_ERROR    (5.0)   //单位：m
//GPS无效判定时间
#define TIME_NO_DATA           (5.0)      //5s无GNSS、VMC、空速，则对应判断无效
#define TIME_UART_NO_DATA      (3.0)
#define TIME_IMU_NO_DATA       (2.0)
//ADC无效判定时间
//#define TIME_GPS           (1.0)
//
#define GNSSHEIGHT_BUFFER_SIZE     (15)

//进入纯惯导状态后地速无效时间判定
//#define TIME_VN_INVALID           (100.0)
#define TIME_ATTI_RATE_INVALID    (600.0)   //未使用
#define TIME_NAVI_INVALID         (400.0)   //未使用
//#define TIME_HEADING_INVALID      (200.0)
#define TIME_ATTI_INVALID          (600.0) //未使用
#define TIME_RESET_V_P             (10.0)  //未使用
#define TIME_INS_ALONE_LONG       (5.0)    //
//重置滤波器的滤波次数
#define COUNT_RESET_KALMAN_FILTER  (9000)

//判定连续5s GPS有效的收到数据个数
#define GNSS_Valid_2S_BUFFER_COUNT  (5)
#define VMC_Valid_2S_BUFFER_COUNT   (5)
#define AIR_Valid_2S_BUFFER_COUNT   (5)
//#define GNSS_500MS_COUNT            (50)
//GPS数据从无效到恢复时间间隔多久，重置滤波器，并重新赋值惯导速度位置
#define TIME_RESTART_KALMAN_RESTORE_FROM_VALID   (5.0)

//从纯惯导恢复至组合导航后时间间隔多久，恢复导航速度姿态信息有效
#define TIME_NAVI_RESTORE_FROM_VALID              (5.0)


//判断飞机快速转弯的偏航角速率阈值，用于判断快速转弯状态，屏蔽转弯阶段滞后的GPS速度输入以及航向输入
#define THRESHOLD_HEADING_RATE                    (3.0)        //单位：°/s
#define THRESHOLD_HEADING_RATE_LARGE              (3.0)        //单位：°/s
//判断飞机快速转弯的横滚角阈值，用于判断快速转弯状态，屏蔽转弯阶段滞后的GPS速度输入
#define THRESHOLD_GAMA                            (10.0)        //单位：°

//判断飞机速度的阈值，用于确定是否计算航迹角修正惯导航向
#define THRESHOLD_GPS_VN_Horizontal               (10.0)        //单位:m/s

//非航迹角航向角修正需要达到的水平加速度
#define THRESHOLD_HEADING_CORRECT                  (0.3 * G0)
//GPS航向最大变化量，超过判断为故障
#define THRESHOLD_HEADING_ERROR                     (2.5) //单位：°

/***********************************************Kalman滤波相关常量定义**********************************************************************/
#define   DIM_STATE               (15)                  //定义Kalman滤波的状态量维数                     
#define   DIM_MAX_OBV             (7)                   //定义Kalman滤波的观测量维数

//误差量反馈修正模式定义
#define   MODE_OPEN_LOOP          (0x50)  //开环估计模式，即仅仅估计误差量的大小而不采取任何反馈修正措施，用于程序调试及滤波精度验证 
#define   MODE_VEPO_CORR          (0x51)  //速度位置修正模式，即仅仅修正惯导速度和位置误差，用于采用高精度陀螺惯导系统中，不对其他误差项进行修正
#define   MODE_ATTI_CORR          (0x52)  //姿态修正模式，即修正惯导速度和位置误差以及姿态角误差，用于中等精度陀螺惯导系统中，不对其他误差项进行修正
#define   MODE_ALL_CORR           (0x53)  //所有误差修正模式，即在对应误差项可观测条件下，尝试修正所有的已建模误差项，用于MEMS低精度惯导系统中

//误差反馈修正相关常量
#define   POSITION_CORRECT_COUNT   (1)          //滤波多少次后开始修正位置误差,最小值1次
#define   VELOCITY_CORRECT_COUNT   (10)         //滤波多少次后开始修正速度误差
#define   ATTITUDE_CORRECT_COUNT   (50)          //滤波多少次后开始修正姿态误差
#define   HEADING_CORRECT_COUNT    (5)      //滤波多少次后开始修正航向误差
#define   GYRO_DRIFT_CORRECT_COUNT (2500)       //滤波多少次后开始修正陀螺零偏
#define   ACC_DRIFT_CORRECT_COUNT  (2500)       //滤波多少次后开始修正加速度计零偏

#define   POSITION_CORRECT_CYCLE   (1)
#define   VELOCITY_CORRECT_CYCLE   (5)        //速度误差修正频率
#define   ATTITUDE_CORRECT_CYCLE   (5)        //姿态误差修正频率
#define   HEADING_CORRECT_CYCLE    (5)        //航向误差修正频率

#define   HEADING_VALID_COUNT_FOR_CORRECT      (5)

#define   GYRO_DRIFT_CORRECT_CYCLE (200)        //陀螺零偏修正频率
#define   ACC_DRIFT_CORRECT_CYCLE  (200)        //加速度计修正频率

#define   ATTITUDE_CORRECT_MAX_VAL          (2.5)        //姿态误差修正最大幅值，单位：°
#define   ATTITUDE_CORRECT_PERCENT_LIMIT    (0.25)       //超出姿态误差限制后的修正百分比

#define   HEADING_CORRECT_MAX_VAL           (2.5)        //航向误差修正最大幅值，单位：°
#define   HEADING_CORRECT_PERCENT_LIMIT     (0.25)       //超出姿态误差限制后的修正百分比

#define   HEADINGRATE_BUFFER_SIZE           (200)

#define   ATTI_CORRECT_FACTOR               (0.5)

#define GPS_VELOCITY_MIN_LIMIT              (0.2)
//GNSS状态字定义
#define STATE_GNSS_DATA             (0x01)
#define STATE_GNSS_POS_VEL_VALID    (0x02)
#define STATE_GNSS_HEAD_VALID       (0x04)
#define STATE_GNSS_RTK_VALID        (0x08)
#define STATE_GNSS_TIME_VALID       (0x10)
#define STATE_GPS_WORK              (0x20)
#define STATE_BD_WORK               (0x40)

//导航状态字定义
#define STATE_GNSS_STANDBY           (0x01)
#define STATE_GNSS_ALIGN             (0x02)
#define STATE_GNSS_NAVI              (0x08)
#define STATE_INTEGRATED_NAVI        (0x10)

//传感器状态字定义
#define STATE_GYRO_TEMP_INVALID      (0x01)
#define STATE_ACC_TEMP_INVALID       (0x02) 
#define STATE_GYRO_INVALID           (0x04)
#define STATE_ACC_INVALID            (0x08)    

//其他状态字定义
#define STATE_HEIGHT_DAMPED          (0x01)
#define STATE_GPS_REFRESH            (0x20)  

#define NUM_TEMP_DIFF_BUFFER         (200)   //
#define NUM_1S_COUNT                 (200)   //
//神经网络相关参数设置
#define INPUT_DIM                     (2)  //输入维数
#define DENSE_1_CELL_NUM              (4)  //第一隐藏层神经元个数
#define DENSE_2_CELL_NUM              (2)  //第二隐藏层神经元个数
#define OUTPUT_DIM                    (1)  //输出数据维数

//3阶高度通道阻尼参数
#define K1                         (1.237)
#define K2                         (0.52)
#define K3                         (0.072)
#define K4                         (0.025)
//#define K1                          (0.03)
//#define K2                          (0.00040307)
//#define K3                          (0.000002)
//#define K4                          (0.0)
#define  COUNT_COMPASS_ATTI_OK      (120000)
//IIR滤波器相关参数
#define NUM_A                       (4)
#define NUM_B                       (5)
#define TIME_CORRECT_HEAD_DRIFT     (600.0)
//DR 
#define DR_D                        (0.535)
#define K_DR                        (1.0)
#define K_DR_1                      (1.0)
#define Install_ANGLE_1             (0.0)

//??????????
#define ID_READ                      (0x11)
#define ID_WRITE_IMUDATA             (0x10)
#define ID_WRITE_INFO                (0x12)

#define ID_GYRO_NORMALTEMP           (0x01)
#define ID_ACC_NORMALTEMP            (0x02)
#define ID_GYRO_FULLTEMP             (0x03)
#define ID_ACC_FULLTEMP              (0x04)
#define ID_GYRO_X_ANNPARA            (0x05)
#define ID_GYRO_Y_ANNPARA            (0x06)
#define ID_GYRO_Z_ANNPARA            (0x07)
#define ID_ACC_X_ANNPARA             (0x08)
#define ID_ACC_Y_ANNPARA             (0x09)
#define ID_ACC_Z_ANNPARA             (0x0A)

#define SUBCMD_READ_IMUDATA          (0x01)
#define SUBCMD_READ_INFO             (0x02)

#define CMD_WRITE_IMUDATA            (0x01)
#define CMD_WRITE_INFO               (0x02)

#define CMD_READ_IMUDATA             (0x04)
#define CMD_READ_INFO                (0x08)

//#define LEN_IMUDATA                  (4078)
#define LEN_NORMALTEMP                (183)
#define LEN_FULLTEMP                  (967)
#define LEN_ANN                       (607)
#define LEN_MAX_IMU_BUFFER_SIZE       LEN_FULLTEMP

#define LEN_INFO                     (25)
#define LEN_READ                     (7)
#define LEN_RESPONSE                 (7)

#define ID_WRITE_RESPONSE             (0x11)
#define ID_WRITE_IMUDATA_SUCCESS      (0x01)
#define ID_WRITE_INFO_SUCCESS         (0x02)
#define ID_WRITE_IMUDATA_FAILED       (0x03)
#define ID_WRITE_INFO_FAILED          (0x04)

#define TIME_SENDIMUDELAY              (0.15)


//姿态修正量幅度限制
#define CORRECT_ATTI_LIMIT             (8.726646259971647e-02)    //±5°
#define CORRECT_HEAD_LIMIT             (1.745329251994329e-01)    //±10°
//IMU滑动平均个数
#define IMU_SMOOTH_AVE_NUM             (20)
//????
#define X_GYRO_AXIS_REVERSE            (0x01)
#define Y_GYRO_AXIS_REVERSE            (0x02)
#define Z_GYRO_AXIS_REVERSE            (0x04)
#define X_ACC_AXIS_REVERSE             (0x08)
#define Y_ACC_AXIS_REVERSE             (0x10)
#define Z_ACC_AXIS_REVERSE             (0x20)

#define IMU_RAW_MODE                   (0x00)
#define IMU_MODE                       (0x01)
#define USER_MODE                      (0x02)
#define TEST_MODE                      (0x04)
#define SIMU_MODE                      (0x08)

#define CORRECT_MASK                   (0x03)
#define NO_CORRECT_ACTION              (0x00)
#define CORRECT_PRE                    (0x01)
#define CORRECT_ACT                    (0x02)
#define CORRECT_STOP                   (0x03)

#define NO_CORRECT                     (0x01)
#define CORRECT_FINISH                 (0x02)
#define CORRECT_CONFIRM                (0x03)

#define GNSS_STATE_MASK                (0x03)
#define GNSS_FAIL                      (0x00)
#define GNSS_SINGLE_POINT              (0x01)
#define GNSS_RTD                       (0x02)
#define GNSS_RTK                       (0x03)

#define GNSS_VEL_AND_POS_VALID         (0x04)
#define GNSS_TIME_VALID                (0x08)

#define WORK_PHASE_MASK                (0x03)

#define GNSS_FAIL                       (0x00)
#define GNSS_STANDBY                    (0x01)
#define GNSS_ALIGN                      (0x02)
#define GNSS_NAVI                       (0x03)

#define INS_FAIL                       (0x00)
#define INS_STANDBY                    (0x01)
#define INS_ALIGN                      (0x02)
#define INS_NAVI                       (0x03)

#define INS_SIMU_WORK                  (0x20)   


#define GNSS_HEAD_VALID                 (0x02)
#define GNSS_ALIGN_HEAD_VALID           (0x40)

#define WORK_MODE_MASK                 (0x1C)
#define WORK_MODE_FAIL                 (0x00)
#define WORK_MODE_PURE_INS            (0x04)
#define WORK_MODE_INS_AIR             (0x08)
#define WORK_MODE_INS_VMC_GNSS        (0x0C)
#define WORK_MODE_INS_GNSS_GNSS        (0x10)
#define WORK_MODE_INS_VMC_DGNSS       (0x14)
#define WORK_MODE_INS_GNSS_DGNSS       (0x18)



#define VMC_HEAD_VALID                 (0x04)
#define VMC_HEAD_CORR_VALID            (0x08)

#define VMC_GNSS_HEAD_DATA_VALID       (0x10)
#define VMC_AIR_DATA_VALID             (0x20)


#define AIR_HEIGHT_VALID               (0x01)
#define INDICATED_AIR_V_VALID          (0x02)
#define TRUE_AIR_V_VALID               (0x04)
#define VERTICAL_V_VALID               (0x20)



//系统状态字2
#define VMC_GNSS_VALID                  (0x04)
#define VMC_GNSS_STATE_MASK             (0x18)
#define VMC_GNSS_FAIL                   (0x00)
#define VMC_GNSS_SINGLE_POINT           (0x08)
#define VMC_GNSS_RTD                    (0x10)
#define VMC_GNSS_RTK                    (0x18) 
#define VMC_AIR_VALID                   (0x20)
#define VMC_HEAD_CMD_VALID              (0x40)
#define VMC_HEAD_CORR_STATE             (0x80)
//通信状态字
#define COM_VMC_VALID                   (0x01)
#define COM_GNSS_VALID                  (0x02)
#define COM_PPS_VALID                   (0x04)
#define VIRTUAL_PPS_VALID               (0x08)

//IMU数据状态字
#define IMU_FAIL                         (0x00)
#define GYRO_X_VALID                     (0x01)
#define GYRO_Y_VALID                     (0x02)
#define GYRO_Z_VALID                     (0x04)

#define ACC_X_VALID                      (0x10)
#define ACC_Y_VALID                      (0x08)
#define ACC_Z_VALID                      (0x20)

#define IMU_VALID_MASK                   (0x3F)

//INS数据有效字
#define ATTI_VALID                       (0x01)
#define HEAD_VALID                       (0x02)
#define POS_VALID                        (0x04)
#define HEIGHT_VALID                     (0x08)
#define VEL_H_VALID                      (0x10)
#define VEL_V_VALID                      (0x20)
#define Q_VALID                          (0x40)
#define VG_VALID                         (0x80)
//系统故障字1
#define GYRO_X_FAIL                      (0x01)
#define GYRO_Y_FAIL                      (0x02)
#define GYRO_Z_FAIL                      (0x04)

#define ACC_X_FAIL                       (0x20)
#define ACC_Y_FAIL                       (0x10)
#define ACC_Z_FAIL                       (0x40)

//系统故障字2
#define COM_IMU_FAIL                      (0x04) 
#define ALIGN_FAIL                        (0x08)

//无效
#define FAIL                              (0x00)
#define INS_VALID                        (0x03)

//串口收取相同数据重复次数
#define ERR_VMC_REPEAT_COUNT              (5)
#define ERR_GNSS_REPEAT_COUNT              (5)

//高度范围
#define MAX_HEIGHT                        (20000.0)
#define MIN_HEIGHT                        (-1000.0)

//大气高度范围
#define MAX_AIR_HEIGHT                    (14000.0)
#define MIN_AIR_HEIGHT                    (-400.0) 

//升降速度范围          
#define VERTICAL_VEL_LIMIT                 (20.0) //±20m/s

//组合状态下载体处于静态判断阈值
#define THRESHOLD_VEL_STATIC                (0.5) //0.5m/s
//纯惯导使用预测值阻尼前的滤波次数下限
#define THRESHOLD_KAL_COUNT_FOR_PREDICT     (600)

//禁止姿态修正的横滚角
#define THRESHOLD_MAX_ROLL                  (15.0)
//禁止姿态修正的水平加速度
#define THRESHOLD_HORIZONTAL_ACC           (0.2 * G0)
#define THRESHOLD_ALL_ACC                  (0.7 * G0)
//对准工作模式
#define ALIGN_MODE_MASK                          (0x30)
#define ALIGN_MODE_NONE                          (0x00) //不对准
#define ALIGN_MODE_LAND                          (0x10) //地面对准
#define ALIGN_MODE_BOARD                         (0x20) //空中对准
//内外GNSS屏蔽工作模式
#define BLOCK_VMC_GNSS                           (0x80)
#define BLOCK_GNSS_GNSS                          (0x40)
#define BLOCK_MASK                               (0xC0)
//
#define VN_BUFFER_COUNT                           (9000)

//Kalman
#define POS_FIT                                   (0x01)
#define VEL_FIT                                   (0x02)
#define POSVEL_FIT                                (0x03)
#define POSVELHEAD_FIT                            (0x04)
#define POS_HEAD_FIT                              (0x05)
#define VEL_HEAD_FIT                              (0x06)
//Kalman WorkMode
#define FILTER                                   (0x01)
#define PREDICT                                  (0x02)
#endif

