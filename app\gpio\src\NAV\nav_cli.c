///***********************************************************************************
//nav cli module
//All rights reserved I-NAV 2023 2033
//***********************************************************************************/
///***********************************************************************************
//Modification history

//|-----------+---------------+-------------------|
//|Author          |    Date               |    Done                      |
//|-----------+---------------+-------------------|
//|DengWei       |  2023-5-17          | First Creation             |
//|-----------+---------------+-------------------|  
//***********************************************************************************/
//#include "nav_includes.h"
//#include <string.h>
//#include <stdio.h>
//#ifndef linux
//#include "UartAdapter.h"
//#include "computerFrameParse.h"
//extern AppSettingTypeDef hSetting;
//extern struct calib_t caliData;

//#endif


//extern unsigned long int g_NavIndex;
//void Arm_SendMsg(char* buffer,unsigned short len);


///********************************************$ALGO:writeConfig*************************************************************/
//#define SetWriteConfig					"$ALGO:writeConfig"

////imu选择MEMS or fog
//#define SetStrImuSelect					"$ALGO:writeConfig,imuSelect,"

////安装杆臂设置
//#define SetStrGnssArmLength				"$ALGO:writeConfig,gnssArmLength,"
////安装角度设置
//#define SetStrGnssAtt_from_vehicle		"$ALGO:writeConfig,gnssAtt_from_vehicle,"
////底盘与IMU安装角度
//#define SetStrOBAtt_from_vehicle		"$ALGO:writeConfig,OBAtt_from_vehicle,"

////标定设置
//#define SetStrAdj_Nav_Standard_flag		"$ALGO:writeConfig,Adj_Nav_Standard_flag,"
//#define SetStrAdj_gyro_off				"$ALGO:writeConfig,Adj_gyro_off,"
//#define SetStrAdj_acc_off				"$ALGO:writeConfig,Adj_acc_off,"
//#define SetStrAdj_Gnssheading			"$ALGO:writeConfig,Adj_Gnssheading,"
//#define SetStrAdj_Odsheading			"$ALGO:writeConfig,Adj_Odsheading,"


////算法设置
//#define SetStrAlg_methodType			"$ALGO:writeConfig,methodType,"
//#define SetStrAlg_wb_set				"$ALGO:writeConfig,wb_set,"
//#define SetStrAlg_HP					"$ALGO:writeConfig,HP,"

////调试模拟
//#define SetStrDebug_simulate			"$ALGO:writeConfig,simulate,"
//#define SetStrDebug_lostepoch			"$ALGO:writeConfig,lostepoch,"

///*************************************$ALGO:setKf********************************************************/
////kalman误差参数配置，注意配置完需要重启
//#define SetStrKF_YES					"$ALGO:setKf"
//#define SetStrKF_attBias				"$ALGO:setKf,attBias,"	
//#define SetStrKF_velBias				"$ALGO:setKf,velBias,"
//#define SetStrKF_posBias				"$ALGO:setKf,posBias,"
//#define SetStrKF_leverBias				"$ALGO:setKf,leverBias,"
//#define SetStrKF_gyroBias				"$ALGO:setKf,gyroBias,"
//#define SetStrKF_accBias				"$ALGO:setKf,accBias,"

//#define SetStrKF_angRandomWalk			"$ALGO:setKf,angRandomWalk,"
//#define SetStrKF_velRandomWalk			"$ALGO:setKf,velRandomWalk,"

//#define SetStrKF_gnssAttErr				"$ALGO:setKf,gnssAttErr,"
//#define SetStrKF_gnssVelErr				"$ALGO:setKf,gnssVelErr,"
//#define SetStrKF_gnssSppPosErr			"$ALGO:setKf,gnssSppPosErr,"
//#define SetStrKF_gnssRtkPosErr			"$ALGO:setKf,gnssRtkPosErr,"

//#define SetStrKF_odsVelErr				"$ALGO:setKf,odsVelErr,"
//#define SetStrKF_magAttErr				"$ALGO:setKf,magAttErr,"
//#define SetStrKF_uwbPosErr				"$ALGO:setKf,uwbPosErr,"





//void PrintOutMatrix(char *Name,double * mat, unsigned short row,unsigned short col)
//{
//	char buf[4096]={0}; 
//	char tmp[128]={0};
//	unsigned short i,j;
//	unsigned short len;
//	if(NULL==Name || NULL==mat)
//	{
//		return;
//	}
//	strcat(buf,Name);
//	strcat(buf,"=[\r\n");
//	for(j=0;j<col;j++)
//	{
//		for(i=0;i<row;i++)
//		{
//			memset(tmp,0,sizeof(tmp));
//			sprintf(tmp,"%.7e",mat[i+j*row]);	
//			strcat(buf,tmp);
//			if(i!=(row-1))
//			{
//				strcat(buf,",");
//			}
//		}
//		strcat(buf,"\r\n");
//	}
//	strcat(buf,"]\r\n");
//	len=strlen(buf);
//#ifdef linux	
//	printf(buf);
//#else
//	Arm_SendMsg(buf,len);
//#endif

//}

//void Arm_SendMsg(char* buffer,unsigned short len)
//{
//#ifndef linux
//	 Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, (unsigned char *)buffer);
//#endif	 
//}


//void CliShowBuildVersion()
//{ 
//	char buf[128]={0};
//	unsigned short len;
//	strcat(buf,SINS_ALGORITHM_BUILD_VERSION); 
//	strcat(buf,"@"); 
//	strcat(buf,__TIME__); 
//	strcat(buf,"_"); 
//	strcat(buf,__DATE__);
//	len=strlen(buf);
//#ifdef linux	
//	printf("I-NAV_ALGO@Version:%s.\r\r\n", buf);
//#else
//	Arm_SendMsg(buf,len);
//#endif
//}

//void CliShowNavStatus()
//{ 
//	char buf[1024]={0}; 
//	char tmp[128]={0}; 
//	unsigned short len;

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"\r\n**********NAV STATUS**************\r\n");
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"nav status: %s\r\n",g_NavStatusStaTxt[NAV_Data_Full.Nav_Status]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"fusion status: %s\r\n",g_NavFusionSourceStaTxt[NAV_Data_Full.KF.fusion_source]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"gps status: %s\r\n",g_NavRtkStatusStaTxt[NAV_Data_Full.GPS.rtkStatus]);
//	strcat(buf,tmp);

//	if(NAV_Data_Full.ODS.ods_flag == E_ODS_WHEEL_FLAG_HAVE)
//	{
//		strcat(buf,"wheel status: E_ODS_WHEEL_FLAG_HAVE\r\n");
//	}
//	else
//	{
//		strcat(buf,"wheel status: E_ODS_WHEEL_FLAG_NONE\r\n");
//	}

//	len=strlen(buf);
//	
//#ifdef linux	
//	printf(buf);
//#else
//	Arm_SendMsg(buf,len);
//#endif	
//}

//void CliShowAbormal()
//{


//}
////$ALGO:set;navstatus=1;methodType=0;wb_set=0,0,40.0;imuSelect=0;memsType=0
////set数据以分号进行分割
//void CliShowNavSet(char *pData)
//{
//	//设置导航状态
//	//暂时不做，等需求
//	char buf[1024]={0}; 
//	char tmp[128]={0}; 
//	unsigned short len;

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"Error Cmd:%s.\r\r\n", pData);
//	strcat(buf,tmp);
//	len=strlen(buf);
//	
//#ifdef linux	
//	printf(buf);
//#else
//	Arm_SendMsg(buf,len);
//#endif
//}

//void CliShowErrorCmd(char *pData)
//{
//	char buf[1024]={0}; 
//	char tmp[128]={0}; 
//	unsigned short len;

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"Error Cmd:%s.\r\r\n", pData);
//	strcat(buf,tmp);
//	len=strlen(buf);
//	
//#ifdef linux	
//	printf(buf);
//#else
//	Arm_SendMsg(buf,len);
//#endif
//}

//void CliSetRestartNav()
//{
//	NAV_Data_Full.Nav_Status=E_NAV_STATUS_START;
//	memset(&NAV_Data_Full,0,sizeof(NAV_Data_Full));
//}
//void CliSetStopNav()
//{
//	NAV_Data_Full.Nav_Status=E_NAV_STATUS_STOP;
//}

//void CliReadConfig()
//{
//	char buf[2048]={0}; 
//	char tmp[128]={0}; 
//	unsigned short len;

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"\r\n**********NAV Config**************\r\n");
//	strcat(buf,tmp);

///****************************************device parameters*********************************************************/
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"\r\n**********device parameters**************\r\n");
//	strcat(buf,tmp);
//	
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"imuSelect=%d(0:mems 1:ifog)\r\n",combineData.imuSelect);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"memsType=%d(0:460 1:scha63x)\r\n",combineData.memsType);
//	strcat(buf,tmp);

///**************************************installation parameters******************************************************/
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"\r\n**********installation parameters**************\r\n");
//	strcat(buf,tmp);
//	
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"gnssArmLength=[%8.4f,%8.4f,%8.4f](m)\r\n",\
//		combineData.Param.gnssArmLength[0],combineData.Param.gnssArmLength[1],combineData.Param.gnssArmLength[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"gnssAtt_from_vehicle=[%8.4f,%8.4f,%8.4f](degree)\r\n",\
//		combineData.Param.gnssAtt_from_vehicle[0],combineData.Param.gnssAtt_from_vehicle[1],combineData.Param.gnssAtt_from_vehicle[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"OD_Att_from_vehicle=[%8.4f,%8.4f,%8.4f](degree)\r\n",\
//		combineData.Param.OBAtt_from_vehicle[0],combineData.Param.OBAtt_from_vehicle[1],combineData.Param.OBAtt_from_vehicle[2]);
//	strcat(buf,tmp);

///*************************************Adjust parameters************************************************************/
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"\r\n**************Adjust parameters*****************\r\n");
//	strcat(buf,tmp);
//	
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"Nav_Standard_flag=%d(0:no 2:yes)\r\n",combineData.Adj.Nav_Standard_flag);
//	strcat(buf,tmp);
//	
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"gyro_off=[%8.6f,%8.6f,%8.6f](rad/s)\r\n",\
//		combineData.Adj.gyro_off[0],combineData.Adj.gyro_off[1],combineData.Adj.gyro_off[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"acc_off=[%8.6f,%8.6f,%8.6f](m/s2)\r\n",\
//		combineData.Adj.acc_off[0],combineData.Adj.acc_off[1],combineData.Adj.acc_off[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"gnssAtt_from_vehicle2=[%8.6f,%8.6f,%8.6f](degree)\r\n",\
//		combineData.Adj.gnssAtt_from_vehicle2[0],combineData.Adj.gnssAtt_from_vehicle2[1],combineData.Adj.gnssAtt_from_vehicle2[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"att_ods2_b_filte_2=%f(degree)\r\n",combineData.Adj.att_ods2_b_filte_2);
//	strcat(buf,tmp);

///***********************************Algorithm parameters**************************************************************/	
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"\r\n**********Algorithm parameters**************\r\n");
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"methodType=%d(0:kalman 1:DR using ODS)\r\n",combineData.Param.methodType);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"wb_set=[%8.4f,%8.4f,%8.4f](DR using ODS parmaeter,degree/h)\r\n",\
//		combineData.Param.wb_set[0],combineData.Param.wb_set[1],combineData.Param.wb_set[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"HP=%d(0:low cost MEMS; 1: high precision)\r\n",combineData.Param.HP);
//	strcat(buf,tmp);

///*************************************Debug parameters************************************************************/ 
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"\r\n**********Debug parameters**************\r\n");
//	strcat(buf,tmp);
//	
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"simulate=%d(0:normal 1:simulation)\r\n",combineData.Param.sim);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"current Epoch=%ld(FREQ=%d)\r\n",g_NavIndex,SAMPLE_FREQ);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"lost Epoch=%d\r\n",combineData.Param.lostepoch);
//	strcat(buf,tmp);

///*************************************Calc parameters************************************************************/ 
//	if(E_NAV_STANDARD_PROCCSSED == NAV_Data_Full.Nav_Standard_flag)
//	{
//		memset(tmp,0,sizeof(tmp));
//		sprintf(tmp,"\r\n**********Calc adj data**************\r\n");
//		strcat(buf,tmp);
//		
//		memset(tmp,0,sizeof(tmp));
//		sprintf(tmp,"gnssAtt_from_vehicle2[2]=%8.6f\r\n",\
//			NAV_Data_Full.Param.gnssAtt_from_vehicle2[2]);
//		strcat(buf,tmp);

//		memset(tmp,0,sizeof(tmp));
//		sprintf(tmp,"ODS.att_ods2_b_filte[2]=%8.6f\r\n",\
//			NAV_Data_Full.ODS.att_ods2_b_filte[2]);
//		strcat(buf,tmp);
//	}
//		
//	
//	len=strlen(buf);
//	
//#ifdef linux	
//	printf(buf);
//#else
//	Arm_SendMsg(buf,len);
//#endif
//}

////将kalman滤波参数写入flash
//void SaveKalmanErrMat(KalmanErrorMat_t *pkalmanErrMat)
//{
////fengaibing,save to flash
//#ifndef linux
//	//保存参数
//	memcpy(&combineData.Param.mKFErrMat, pkalmanErrMat, sizeof(KalmanErrorMat_t));
//	memcpy((void*)&hSetting.settingData.param,(void*)&combineData.Param, sizeof(Param_t));
//	comm_set_customPara();
//#endif	
//}
//void CliReadKalmanErrMat()
//{
//	char buf[4096]={0}; 
//	char tmp[256]={0}; 
//	unsigned short len;

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"\r\n**********Kalman Error Matrix parameters**************\r\n");
//	strcat(buf,tmp);

///******************************************kalman P矩阵相关参数****************************************/
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"\r\n**********kalman P matrix parameters**************\r\n");
//	strcat(buf,tmp);
//	
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"attBias=[%8.6f,%8.6f,%8.6f](degree)\r\n",\
//		g_kalmanErrMat.attBias[0],g_kalmanErrMat.attBias[1],g_kalmanErrMat.attBias[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"velBias=[%8.6f,%8.6f,%8.6f](m/s)\r\n",\
//		g_kalmanErrMat.velBias[0],g_kalmanErrMat.velBias[1],g_kalmanErrMat.velBias[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"posBias=[%8.6f,%8.6f,%8.6f](m)\r\n",\
//		g_kalmanErrMat.posBias[0],g_kalmanErrMat.posBias[1],g_kalmanErrMat.posBias[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"leverBias=[%8.6f,%8.6f,%8.6f](m)\r\n",\
//		g_kalmanErrMat.leverBias[0],g_kalmanErrMat.leverBias[1],g_kalmanErrMat.leverBias[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"gyroBias=[%8.6f,%8.6f,%8.6f](dps)\r\n",\
//		g_kalmanErrMat.gyroBias[0],g_kalmanErrMat.gyroBias[1],g_kalmanErrMat.gyroBias[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"accBias=[%8.6f,%8.6f,%8.6f](mg)\r\n",\
//		g_kalmanErrMat.accBias[0],g_kalmanErrMat.accBias[1],g_kalmanErrMat.accBias[2]);
//	strcat(buf,tmp);
///******************************************kalman Q矩阵相关参数****************************************/
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"\r\n**********kalman Q matrix parameters**************\r\n");
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"angRandomWalk=[%8.6f,%8.6f,%8.6f](dpsh)\r\n",\
//		g_kalmanErrMat.angRandomWalk[0],g_kalmanErrMat.angRandomWalk[1],g_kalmanErrMat.angRandomWalk[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"velRandomWalk=[%8.6f,%8.6f,%8.6f](MGPSH)\r\n",\
//		g_kalmanErrMat.velRandomWalk[0],g_kalmanErrMat.velRandomWalk[1],g_kalmanErrMat.velRandomWalk[2]);
//	strcat(buf,tmp);
///******************************************kalman R矩阵相关参数****************************************/
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"\r\n**********kalman R matrix parameters**************\r\n");
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"gnssAttErr=[%8.6f,%8.6f,%8.6f](degree)\r\n",\
//		g_kalmanErrMat.gnssAttErr[0],g_kalmanErrMat.gnssAttErr[1],g_kalmanErrMat.gnssAttErr[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"gnssVelErr=[%8.6f,%8.6f,%8.6f](m/s)\r\n",\
//		g_kalmanErrMat.gnssVelErr[0],g_kalmanErrMat.gnssVelErr[1],g_kalmanErrMat.gnssVelErr[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"gnssSppPosErr=[%8.6f,%8.6f,%8.6f](m)\r\n",\
//		g_kalmanErrMat.gnssSppPosErr[0],g_kalmanErrMat.gnssSppPosErr[1],g_kalmanErrMat.gnssSppPosErr[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"gnssRtkPosErr=[%8.6f,%8.6f,%8.6f](m)\r\n",\
//		g_kalmanErrMat.gnssRtkPosErr[0],g_kalmanErrMat.gnssRtkPosErr[1],g_kalmanErrMat.gnssRtkPosErr[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"odsVelErr=[%8.6f,%8.6f,%8.6f](m/s)\r\n",\
//		g_kalmanErrMat.odsVelErr[0],g_kalmanErrMat.odsVelErr[1],g_kalmanErrMat.odsVelErr[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"magAttErr=[%8.6f,%8.6f,%8.6f](degree)\r\n",\
//		g_kalmanErrMat.magAttErr[0],g_kalmanErrMat.magAttErr[1],g_kalmanErrMat.magAttErr[2]);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"uwbPosErr=[%8.6f,%8.6f,%8.6f](m)\r\n",\
//		g_kalmanErrMat.uwbPosErr[0],g_kalmanErrMat.uwbPosErr[1],g_kalmanErrMat.uwbPosErr[2]);
//	strcat(buf,tmp);
//	
//	len=strlen(buf);		
//#ifdef linux	
//	printf(buf);
//#else
//	Arm_SendMsg(buf,len);
//#endif
//		
//}
//void CliSetKalmanConf(char *pData)
//{
//	char *p=NULL;
///******************************************kalman P矩阵相关参数****************************************/
//	if(strstr(pData, SetStrKF_attBias) != NULL)
//	{
//		p=pData+strlen(SetStrKF_attBias);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.attBias);
//		//保存参数
//		//SaveKalmanErrMat(&g_kalmanErrMat);	
//	}
//	else if(strstr(pData, SetStrKF_velBias) != NULL)
//	{
//		p=pData+strlen(SetStrKF_velBias);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.velBias);
//		//保存参数
//		//SaveKalmanErrMat(&g_kalmanErrMat);
//	}
//	else if(strstr(pData, SetStrKF_posBias) != NULL)
//	{
//		p=pData+strlen(SetStrKF_posBias);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.posBias);
//	}
//	else if(strstr(pData, SetStrKF_leverBias) != NULL)
//	{
//		p=pData+strlen(SetStrKF_leverBias);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.leverBias);
//	}
//	else if(strstr(pData, SetStrKF_gyroBias) != NULL)
//	{
//		p=pData+strlen(SetStrKF_gyroBias);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.gyroBias);
//	}
//	else if(strstr(pData, SetStrKF_accBias) != NULL)
//	{
//		p=pData+strlen(SetStrKF_accBias);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.accBias);
//	}
///******************************************kalman Q矩阵相关参数****************************************/		
//	else if(strstr(pData, SetStrKF_angRandomWalk) != NULL)
//	{
//		p=pData+strlen(SetStrKF_angRandomWalk);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.angRandomWalk);
//	}
//	else if(strstr(pData, SetStrKF_velRandomWalk) != NULL)
//	{
//		p=pData+strlen(SetStrKF_velRandomWalk);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.velRandomWalk);
//	}
///******************************************kalman R矩阵相关参数****************************************/		
//	/*******************************GNSS误差******************************/
//	else if(strstr(pData, SetStrKF_gnssAttErr) != NULL)
//	{
//		p=pData+strlen(SetStrKF_gnssAttErr);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.gnssAttErr);
//	}
//	else if(strstr(pData, SetStrKF_gnssVelErr) != NULL)
//	{
//		p=pData+strlen(SetStrKF_gnssVelErr);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.gnssVelErr);
//	}
//	else if(strstr(pData, SetStrKF_gnssSppPosErr) != NULL)
//	{
//		p=pData+strlen(SetStrKF_gnssSppPosErr);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.gnssSppPosErr);
//	}
//	else if(strstr(pData, SetStrKF_gnssRtkPosErr) != NULL)
//	{
//		p=pData+strlen(SetStrKF_gnssRtkPosErr);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.gnssRtkPosErr);
//	}
//	/*******************************ODSMETER误差******************************/
//	else if(strstr(pData, SetStrKF_odsVelErr) != NULL)
//	{
//		p=pData+strlen(SetStrKF_odsVelErr);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.odsVelErr);
//	}
//	/*******************************磁力计误差******************************/
//	else if(strstr(pData, SetStrKF_magAttErr) != NULL)
//	{
//		p=pData+strlen(SetStrKF_magAttErr);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.magAttErr);
//	}
//	/*******************************UWB误差*********************************/
//	else if(strstr(pData, SetStrKF_uwbPosErr) != NULL)
//	{
//		p=pData+strlen(SetStrKF_uwbPosErr);
//		GetDoubleParmsFormString(p,",",3,g_kalmanErrMat.uwbPosErr);
//	}
///************************************Error Cmd***************************************************/		
//	else
//	{
//		CliShowErrorCmd(pData);
//	}
//	//保存参数
//	SaveKalmanErrMat(&g_kalmanErrMat);
//	CliReadKalmanErrMat();	
//	
//}

//void CliWriteConfig(char *pData)
//{
//	char *p=NULL;
//	unsigned int	len=0;
//#if	0//linux测试
//	if(strstr(pData, SetStrAdj_acc_off) != NULL)
//	{
//		len=0;
//		float tmp[3]={0.0};
//		p=pData+strlen(SetStrAdj_acc_off);
//		parse_split_fnum(p,",",3,tmp,&len);
//		combineData.Adj.acc_off[0]=tmp[0];
//		combineData.Adj.acc_off[1]=tmp[1];
//		combineData.Adj.acc_off[2]=tmp[2];
//	}
//#endif

//#ifndef linux
///************************************device parameters**********************************************/ 
//	if(strstr(pData, SetStrImuSelect) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrImuSelect);
//		combineData.imuSelect = atoi(p);
//        caliData.imuSelect = combineData.imuSelect;
//        comm_saveCaliData();
//	}
///************************************installation parameters**********************************************/	
//	else if(strstr(pData, SetStrGnssArmLength) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrGnssArmLength);
//		parse_split_fnum(p,",",3,combineData.Param.gnssArmLength,&len);
//		//保存参数
//		memcpy((void*)&hSetting.settingData.param,(void*)&combineData.Param, sizeof(Param_t));
//		comm_set_customPara();
//	}
//	else if(strstr(pData, SetStrGnssAtt_from_vehicle) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrGnssAtt_from_vehicle);
//		parse_split_fnum(p,",",3,combineData.Param.gnssAtt_from_vehicle,&len);
//		//保存参数
//		memcpy((void*)&hSetting.settingData.param,(void*)&combineData.Param, sizeof(Param_t));
//		comm_set_customPara();
//	}
//	else if(strstr(pData, SetStrOBAtt_from_vehicle) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrOBAtt_from_vehicle);
//		parse_split_fnum(p,",",3,combineData.Param.OBAtt_from_vehicle,&len);
//		//保存参数
//		memcpy((void*)&hSetting.settingData.param,(void*)&combineData.Param, sizeof(Param_t));
//		comm_set_customPara();
//	}
///************************************Adj parameters**********************************************/
//	else if(strstr(pData, SetStrAdj_Nav_Standard_flag) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrAdj_Nav_Standard_flag);
//		combineData.Adj.Nav_Standard_flag = atoi(p);
//		memcpy(&caliData.adj,&combineData.Adj,sizeof(AdjPara_t));
//        comm_saveCaliData();
//	}
//	else if(strstr(pData, SetStrAdj_gyro_off) != NULL)
//	{
//		len=0;
//		float tmp[3]={0.0};
//		p=pData+strlen(SetStrAdj_gyro_off);
//		parse_split_fnum(p,",",3,tmp,&len);
//		combineData.Adj.gyro_off[0]=tmp[0];
//		combineData.Adj.gyro_off[1]=tmp[1];
//		combineData.Adj.gyro_off[2]=tmp[2];
//        memcpy(&caliData.adj,&combineData.Adj,sizeof(AdjPara_t));
//        comm_saveCaliData();
//	}
//	else if(strstr(pData, SetStrAdj_acc_off) != NULL)
//	{
//		len=0;
//		float tmp[3]={0.0};
//		p=pData+strlen(SetStrAdj_acc_off);
//		parse_split_fnum(p,",",3,tmp,&len);
//		combineData.Adj.acc_off[0]=tmp[0];
//		combineData.Adj.acc_off[1]=tmp[1];
//		combineData.Adj.acc_off[2]=tmp[2];
//		memcpy(&caliData.adj,&combineData.Adj,sizeof(AdjPara_t));
//    	comm_saveCaliData();
//	}
//	else if(strstr(pData, SetStrAdj_Gnssheading) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrAdj_Gnssheading);
//		//parse_split_fnum(p,",",3,combineData.Adj.gnssAtt_from_vehicle2,&len);
//		combineData.Adj.gnssAtt_from_vehicle2[2]=atof(p);
//        memcpy(&caliData.adj,&combineData.Adj,sizeof(AdjPara_t));
//        comm_saveCaliData();
//	}
//	else if(strstr(pData, SetStrAdj_Odsheading) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrAdj_Odsheading);
//		combineData.Adj.att_ods2_b_filte_2=atof(p);
//        memcpy(&caliData.adj,&combineData.Adj,sizeof(AdjPara_t));
//        comm_saveCaliData();
//	}
///************************************Algorithm parameters**********************************************/
//	else if(strstr(pData, SetStrAlg_methodType) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrAlg_methodType);
//		combineData.Param.methodType=atoi(p);
//		//保存参数
//		memcpy((void*)&hSetting.settingData.param,(void*)&combineData.Param, sizeof(Param_t));
//		comm_set_customPara();
//	}
//	else if(strstr(pData, SetStrAlg_wb_set) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrAlg_wb_set);
//		parse_split_fnum(p,",",3,combineData.Param.wb_set,&len);
//		//保存参数
//		memcpy((void*)&hSetting.settingData.param,(void*)&combineData.Param, sizeof(Param_t));
//		comm_set_customPara();
//	}
//	else if(strstr(pData, SetStrAlg_HP) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrAlg_HP);
//		combineData.Param.HP=atoi(p);
//		//保存参数
//		memcpy((void*)&hSetting.settingData.param,(void*)&combineData.Param, sizeof(Param_t));
//		comm_set_customPara();
//	}
///************************************Debug parameters**********************************************/
//	else if(strstr(pData, SetStrDebug_simulate) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrDebug_simulate);
//		combineData.Param.sim=atoi(p);
//		//保存参数
//		memcpy((void*)&hSetting.settingData.param,(void*)&combineData.Param, sizeof(Param_t));
//		comm_set_customPara();
//	}
//	else if(strstr(pData, SetStrDebug_lostepoch) != NULL)
//	{
//		len=0;
//		p=pData+strlen(SetStrDebug_lostepoch);
//		combineData.Param.lostepoch=atoi(p);
//		//保存参数
//		memcpy((void*)&hSetting.settingData.param,(void*)&combineData.Param, sizeof(Param_t));
//		comm_set_customPara();
//	}
///************************************Error Cmd***************************************************/		
//	else
//	{
//		CliShowErrorCmd(pData);
//	}

//	CliReadConfig();
//#endif	
//}

//void CliShowHelp()
//{
//	char buf[2048]={0}; 
//	char tmp[128]={0}; 
//	unsigned short len;

//	strcat(buf,"\r\n**********NAV HELP**************\r\n");
//	strcat(buf,"$ALGO:version: Show version\r\n");
//	strcat(buf,"$ALGO:outtypeX: 0:bin data;3:obs data,4:no output\r\n");
//	strcat(buf,"$ALGO:status: Show nav status\r\n");
//	strcat(buf,"$ALGO:abnormal:Show nav abnormal statistics\r\n");
//	strcat(buf,"$ALGO:set : set parameters\r\n");
//	strcat(buf,"$ALGO:restart: restart nav system\r\n");
//	strcat(buf,"$ALGO:stop: stop nav system\r\n");
//	//读取配置参数
//	strcat(buf,"$ALGO:readConfig\r\n");
//	
//	//写配置参数
//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%s0:mems 1:ifog\r\n",SetStrImuSelect);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrGnssArmLength);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrGnssAtt_from_vehicle);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrOBAtt_from_vehicle);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%s0:NO 2:YES\r\n",SetStrAdj_Nav_Standard_flag);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrAdj_gyro_off);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrAdj_acc_off);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX\r\n",SetStrAdj_Gnssheading);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX\r\n",SetStrAdj_Odsheading);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%s0:kalman;1:DR using ods vel\r\n",SetStrAlg_methodType);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrAlg_wb_set);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%s0:low precision 1:high precision\r\n",SetStrAlg_HP);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%s0:normal 1:simulate\r\n",SetStrDebug_simulate);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX\r\n",SetStrDebug_lostepoch);
//	strcat(buf,tmp);

//	strcat(buf,"$ALGO:readkf\r\n");

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_attBias);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_velBias);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_posBias);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_leverBias);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_gyroBias);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_accBias);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_angRandomWalk);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_velRandomWalk);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_gnssAttErr);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_gnssVelErr);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_gnssSppPosErr);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_gnssRtkPosErr);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_odsVelErr);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_magAttErr);
//	strcat(buf,tmp);

//	memset(tmp,0,sizeof(tmp));
//	sprintf(tmp,"%sXXX,XXX,XXX\r\n",SetStrKF_uwbPosErr);
//	strcat(buf,tmp);
//	
//	len=strlen(buf);
//#ifdef linux	
//		printf(buf);
//#else
//		Arm_SendMsg(buf,len);
//#endif

//}


////$ALGO:version
//void ParseStrCmd(char *pData)
//{
//	if(NULL==pData)
//	{
//#ifdef linux	
//		inav_log(INAVMD(LOG_ERR),"pData is NULL");
//#endif
//		return;
//	}
//	if(strstr(pData, "navhelp") != NULL)
//	{
//		CliShowHelp();
//	}
//	else if(strstr(pData, "$ALGO:version") != NULL)
//	{
//		CliShowBuildVersion();
//	}
//	else if(strstr(pData, "$ALGO:status") != NULL)
//	{
//		CliShowNavStatus();
//	}
//	else if(strstr(pData, "$ALGO:abnormal") != NULL)
//	{
//		CliShowAbormal();
//	}
//	else if(strstr(pData, "$ALGO:navset") != NULL)
//	{
//		CliShowNavSet(pData);
//	}
//	else if(strstr(pData, "$ALGO:restart") != NULL)
//	{
//		CliSetRestartNav();
//	}
//	else if(strstr(pData, "$ALGO:stop") != NULL)
//	{
//		CliSetStopNav();
//	}
//	else if(strstr(pData, "$ALGO:outtype0") != NULL)
//	{
//		combineData.outputType = 0;
//	}
//	else if(strstr(pData, "$ALGO:outtype1") != NULL)
//	{
//		combineData.outputType = 1;
//	}
//	else if(strstr(pData, "$ALGO:outtype2") != NULL)
//	{
//		combineData.outputType = 2;
//	}
//	else if(strstr(pData, "$ALGO:outtype3") != NULL)
//	{
//		combineData.outputType = 3;
//	}
//	else if(strstr(pData, "$ALGO:outtype4") != NULL)
//	{
//		combineData.outputType = 4;
//	}
//	else if(strstr(pData, "$ALGO:readConfig") != NULL)
//	{
//		 CliReadConfig();
//	}
//	else if(strstr(pData, SetWriteConfig) != NULL)
//	{
//		CliWriteConfig(pData);
//	}
//	else if(strstr(pData, "$ALGO:readkf") != NULL)
//	{
//		CliReadKalmanErrMat();
//	}
//	else if(strstr(pData, SetStrKF_YES) != NULL)
//	{
//		CliSetKalmanConf(pData);
//	}
//	else
//	{
//		CliShowErrorCmd(pData);
//	}
//}


