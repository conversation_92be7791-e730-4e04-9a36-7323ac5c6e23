#include "appmain.h"
#include "ins.h"

ftype SQR(ftype x)
{
    ftype    result = x*x;
	return result;
}

ftype dotn(ftype *a,ftype *b,uint16_t n)
{
    uint16_t i=0;
    ftype c = 0.0;

    for(i=0;i<n;i++) c =  c + a[i]*b[i];        
       
    return c;       
}

ftype iggii(ftype v,ftype var,ftype c)
{
	 ftype gama = 1.0;
	 ftype v_ = 0.0;
	 ftype r  = 0.0;

	v_ = fabs(v / sqrt(fabs(var)));
	if(v_ <= c)
	{
		r = 1.0;
	}
    else
	{
		r = fabs(v_ / c);
    }

	gama =  sqrt(r);

	return gama;
}

V3_ST v3_cross(V3_ST v1,V3_ST v2)
{
	V3_ST product = {0};

	product.x =  v1.y*v2.z - v1.z*v2.y;
	product.y = -v1.x*v2.z + v1.z*v2.x;
	product.z =  v1.x*v2.y - v1.y*v2.x;
    
	return product;

}

//eo_pos
VNED_ST ecef2ned(V3_ST *ecef,double *geo_pos)//ECEFNED
{
	ftype lat =  geo_pos[0]*DEG2RAD;
    ftype lon =  geo_pos[1]*DEG2RAD;
    //ftype hgt = geo_pos[2]*DEG2RAD;

	VNED_ST ned = {0};

	ftype sinLat = sin(lat), cosLat = cos(lat);
	ftype sinLon = sin(lon), cosLon = cos(lon);

	//| -sin(lat)*cos(lon)   -sin(lat)*sin(lon)    cos(lat)  |
    //| -sin(lon)             cos(lon)              0        |
    //| cos(lat)*cos(lon)     cos(lat)*sin(lon)    sin(lat)  |
	ftype ECEF2NED[3][3] ={0};//

	ECEF2NED[0][0] = -sinLat*cosLon;ECEF2NED[0][1] = -sinLat*sinLon;ECEF2NED[0][2] = cosLat;
    ECEF2NED[1][0] = -sinLon;       ECEF2NED[1][1] =  cosLon;       ECEF2NED[1][2] = 0;
    ECEF2NED[2][0] =  cosLat*cosLon;ECEF2NED[2][1] =  cosLat*sinLon;ECEF2NED[2][2] = sinLat;

    ned.n = ECEF2NED[0][0]*ecef->x + ECEF2NED[0][1]*ecef->y + ECEF2NED[0][2]*ecef->z;
	ned.e = ECEF2NED[1][0]*ecef->x + ECEF2NED[1][1]*ecef->y + ECEF2NED[1][2]*ecef->z;
	ned.d = ECEF2NED[2][0]*ecef->x + ECEF2NED[2][1]*ecef->y + ECEF2NED[2][2]*ecef->z;

	return ned;
}

V3_ST geo2ecef(double *geo)
{
	double lat = geo[0]*DEG2RAD;
    double lon = geo[1]*DEG2RAD;
    double hgt = geo[2];

	V3_ST ecef = {0};

	ftype sinLat = sin(lat),cosLat = cos(lat);
	ftype cosLon = cos(lon),sinLon = sin(lon);

	//e^2 = f*(2-f)
	ftype wgs84_esq = WGS84_F*(2.0 - WGS84_F);//1wgs84_esq = WGS84_F*(2-WGS84_F);
	ftype wgs84_RN = WGS84_RE / sqrt(1.0 - wgs84_esq*sinLat*sinLat);//P209

    ecef.x = (wgs84_RN+hgt)*cosLat*cosLon;//P212P45
	ecef.y = (wgs84_RN+hgt)*cosLat*sinLon;
	ecef.z = (wgs84_RN*(1.0-wgs84_esq) + hgt)*sinLat;

	return ecef;
}

VNED_ST geo2ned(double *geo,double *orig_geo)
{
	V3_ST ecef      = geo2ecef(geo);//ECEF
    V3_ST orig_ecef = geo2ecef(orig_geo);
	V3_ST decef = {0};
	
	VNED_ST ned = {0};

	decef.x = ecef.x - orig_ecef.x;//ECEF
    decef.y = ecef.y - orig_ecef.y;
    decef.z = ecef.z - orig_ecef.z;

	ned  = ecef2ned(&decef,orig_geo);//ECEFNED

	return ned;
}

//geo_pos
V3_ST ned2ecef(VNED_ST *ned,double *geo_pos)//NEDECEF
{
	V3_ST ecef = {0};
    double lat = geo_pos[0]*DEG2RAD;
    double lon = geo_pos[1]*DEG2RAD;
    //double hgt = geo_pos[2];

    ftype sinLat = sin(lat); ftype cosLat = cos(lat);
	ftype sinLon = sin(lon); ftype cosLon = cos(lon);

    //| -sin(lat)*cos(lon)     -sin(lat)*sin(lon)     cos(lat) |
    //| -sin(lon)               cos(lon)                    0  |
    //|  cos(lat)*cos(lon)      cos(lat)*sin(lon)     sin(lat) |

    //| -sin(lat)*cos(lon)     -sin(lon)     cos(lat)*cos(lon) |
    //| -sin(lat)*sin(lon)      cos(lon)     cos(lat)*sin(lon) |
   //|  cos(lat)               0            sin(lat)          |
	ftype NED2ECEF[3][3];//

    NED2ECEF[0][0] = -sinLat*cosLon;    NED2ECEF[0][1] =  -sinLon;           NED2ECEF[0][2] =  cosLat*cosLon;
    NED2ECEF[1][0] = -sinLat*sinLon;    NED2ECEF[1][1] =   cosLon;           NED2ECEF[1][2] =  cosLat*sinLon;
    NED2ECEF[2][0] =  cosLat;           NED2ECEF[2][1] =   0;                NED2ECEF[2][2] =  sinLat;

    ecef.x = NED2ECEF[0][0]*ned->n + NED2ECEF[0][1]*ned->e + NED2ECEF[0][2]*ned->d;
	ecef.y = NED2ECEF[1][0]*ned->n + NED2ECEF[1][1]*ned->e + NED2ECEF[1][2]*ned->d;
	ecef.z = NED2ECEF[2][0]*ned->n + NED2ECEF[2][1]*ned->e + NED2ECEF[2][2]*ned->d;

    return ecef;
}

void ecef2geo(V3_ST ecef,double *geo)//ECEF       
{
    ftype r[3]={0};
    r[0] = ecef.x;
    r[1] = ecef.y;
    r[2] = ecef.z;

    ftype e2 = WGS84_F*(2.0 - WGS84_F);
	ftype r2 = dotn(r, r, 2);
    //r2 = SQR(ecef.x) + SQR(ecef.y);
    
    ftype z = r[2];
    ftype zk = 0.0;
    ftype v = WGS84_RE;
    ftype sinp = 0.0;
	while(fabs(z-zk) >= 1E-5) //
    {
		zk = z;
		sinp = z / sqrt(r2 + z*z);
		v = WGS84_RE / sqrt(1.0 - e2*sinp*sinp);
		z = r[2] + v*e2*sinp;
	}

    if(r2> 1E-12)
    {
        geo[0] = atan(z / sqrt(r2));//c
        geo[1] = atan2(r[1], r[0]);

        //geo(1) = atan(sqrt(r2) / z);%matlab
        //geo(2) = atan2(r(1), r(2));
    }
    else
    {
        if(r[2] > 0.0)
        {
            geo[0] =  PI/2.0;
        }
        else
        {
            geo[0] = - PI/2.0;
        }
        geo[1] = 0.0;
    }

	geo[2] = sqrt(r2 + z*z) - v;

    geo[0] = geo[0]*RAD2DEG;
    geo[1] = geo[1]*RAD2DEG;
    geo[2] = geo[2];
}

//geo_pos
void ned2geo(VNED_ST *ned,double *geo_pos,double *geo)//NED
{         
        V3_ST origecef = geo2ecef(geo_pos);//ECEF
        V3_ST ecef     = ned2ecef(ned,geo_pos);//NEDECEF

        ecef.x = origecef.x + ecef.x;
        ecef.y = origecef.y + ecef.y;
        ecef.z = origecef.z + ecef.z;

        ecef2geo(ecef,geo);//ECEF        
}


ftype state_constrain(ftype state,ftype min,ftype max)
{
  ftype temp=0.0;

  if(state < min)
  {
    temp=min;
  }
  else if(state > max)
  {
    temp=max;
  }
  else
  {
     temp =state;
  } 

  return temp;
}

