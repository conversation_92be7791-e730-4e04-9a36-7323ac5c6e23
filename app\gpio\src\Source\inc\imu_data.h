#ifndef ____IMU_DATA_H____
#define ____IMU_DATA_H____


#include "data_convert.h"

typedef struct imu_data_t
{
	float timestamp;		/* 时间戳, 单位: s , 精度：0.0001*/
	float ImuGyro_x;		/* imu陀螺, 单位: °/s, 精度：1/2^14*/
	float ImuGyro_y;		/* imu陀螺, 单位: °/s, 精度：1/2^14*/
	float ImuGyro_z;		/* imu陀螺, 单位: °/s, 精度：1/2^14*/
	float ImuAcc_x;			/* imu加表, 单位: g, 精度：1/2^17*/
	float ImuAcc_y;			/* imu加表, 单位: g, 精度：1/2^17*/
	float ImuAcc_z;			/* imu加表, 单位: g, 精度：1/2^17*/
	float TempGyro_x;		/* 陀螺温度, 单位: ℃, 精度： 1/2^8*/
	float TempGyro_y;		/* 陀螺温度, 单位: ℃, 精度： 1/2^8*/
	float TempGyro_z;		/* 陀螺温度, 单位: ℃, 精度： 1/2^8*/
	float TempAcc_x;		/* 加表温度, 单位: ℃, 精度： 1/2^8*/
	float TempAcc_y;		/* 加表温度, 单位: ℃, 精度： 1/2^8*/
	float TempAcc_z;		/* 加表温度, 单位: ℃, 精度： 1/2^8*/
} IMUDataTypeDef;

extern IMUDataTypeDef hIMUData;

#endif //____IMU_DATA_H____
