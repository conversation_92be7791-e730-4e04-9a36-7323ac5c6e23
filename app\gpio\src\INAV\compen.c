/***********************************************************************************************************************************/
/*COMPENSATION.C                                                                                                                 */
/*  Ver 0.1                                                                                                                        */
/*/                                                                                                */
/*                                                                                                                             */
/*const.htypedefine.hmath.hDATASTRUCT.hEXTERNGLOBALDATA.hFUNCTION.hmemory.h                                          */
/*GNSSlocusGen.m                                                                  */
/* 加计陀螺温度（常温/全温）参数                      */
/*******************************************************************************************************************************************/
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include <math.h>
//#include <string.h>
#include <EXTERNGLOBALDATA.h>
/*********************************************函数说明*******************************************************/
/*函数名称：Compen_Init_60                                                                                  */
/*函数功能描述：补偿的温度区间点的初始设置                                                                  */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：矢量:TempSamPoint                                                                               */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递                                                                           */
/*备注2：调用该函数前需根据具体型号产品补偿的温度区间在CONST.H中预先填入温度点宏                            */
/*返回值：无                                                                                                */
/************************************************************************************************************/
/*void Compen_Init_60(p_Compen lp_compen)
{
    Gyro_Compen_Para_Init_60(lp_compen);
    Acc_Compen_Para_Init_60(lp_compen);
}*/



/*void Compen_Init_60(p_Compen lp_compen)
{
    //陀螺刻度因子
    lp_compen -> RTGyroScalFac[0] = 186515.606018518510000;
    lp_compen -> RTGyroScalFac[1] = 186335.484259259250000;
    lp_compen -> RTGyroScalFac[2] = 186256.740740740730000;

    //陀螺零偏
    lp_compen -> RTGyroBias[0] = -112.730823863636370;
    lp_compen -> RTGyroBias[1] = -89.487215909090935;
    lp_compen -> RTGyroBias[2] = -206.778409090909070;

    //陀螺安装误差
    lp_compen -> GyroInstErr[0] =  0.999991897944744;
    lp_compen -> GyroInstErr[1] = -0.001929999786207;
    lp_compen -> GyroInstErr[2] = -0.000647707532497;
    lp_compen -> GyroInstErr[3] =  0.003883630165239;
    lp_compen -> GyroInstErr[4] =  0.999992730888586;
    lp_compen -> GyroInstErr[5] = -0.000377473964527;
    lp_compen -> GyroInstErr[6] =  0.000933122423848;
    lp_compen -> GyroInstErr[7] = -0.000605480447011;
    lp_compen -> GyroInstErr[8] =  0.999999621959384;

    //加速度计刻度因子
    lp_compen -> RTAccScalFac[0] = 4899.884336454783800;
    lp_compen -> RTAccScalFac[1] = 4700.751920464166700;
    lp_compen -> RTAccScalFac[2] = 4879.755248021465400;

    //加速度计零偏
    lp_compen -> RTAccBias[0] = -40.435414062502794;
    lp_compen -> RTAccBias[1] = -99.493053030304409;
    lp_compen -> RTAccBias[2] = -127.850224195073680;

    //加速度计安装误差
    lp_compen -> AccInstErr[0] =  0.999997390758417;
    lp_compen -> AccInstErr[1] = -0.002245345434514;
    lp_compen -> AccInstErr[2] = -0.000746644980587;
    lp_compen -> AccInstErr[3] =  0.001246585677185;
    lp_compen -> AccInstErr[4] =  0.999996122686483;
    lp_compen -> AccInstErr[5] = -0.003134411684147;
    lp_compen -> AccInstErr[6] = -0.000251380171548;
    lp_compen -> AccInstErr[7] =  0.000344683203718;
    lp_compen -> AccInstErr[8] =  0.999999109401478;
}*/





/*********************************************????*******************************************************/
/*????:Gyro_Compen_Para_Init_60                                                                        */
/*??????:60???????????                                                                    */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                                           */
/*???:                                                                                                  */
/*????:                                                                                                */
/*????:?                                                                                              */
/*????:??                                                                                            */
/*??1:????????????                                                                           */
/*???:?                                                                                                */
/************************************************************************************************************/
/*void Gyro_Compen_Para_Init_60(p_Compen lp_compen)
{
    IPARA i,j;
    MATR GyroTempSamPoint[NUM_TEMP_SAM][3] ={    -2.367527032344595e+01,-2.351199932661612e+01,-2.312256487208477e+01
                                                ,-1.539079784905117e+01,-1.530074626452071e+01,-1.480934680378203e+01
                                                ,-5.538117545668173e+00,-5.609270043562118e+00,-5.133259690631372e+00
                                                , 3.737357425512104e+00, 3.575590111510154e+00, 4.142994313647246e+00
                                                , 1.373955319732642e+01, 1.348874298904851e+01, 1.412843896869735e+01
                                                , 2.794513653343088e+01, 2.764860930987851e+01, 2.835969714352222e+01
                                                , 4.240033918595371e+01, 4.201189679491443e+01, 4.275515117883747e+01
                                                , 5.220903858517335e+01, 5.183295819200585e+01, 5.261573508302297e+01
                                                , 6.752070759344240e+01, 6.721897169016583e+01, 6.806698160525849e+01
                                                , 8.314192223386097e+01, 8.303197742085661e+01, 8.388145568690697e+01 };

    MATR GyroBias[NUM_TEMP_SAM][3] = {    4.602565438336702e-01, 3.798329648381154e-01, 5.494870227928254e-01
                                        , 4.372781918783627e-01, 3.775092916537578e-01, 5.223431680442221e-01
                                        , 4.262341974649546e-01, 3.708018894191205e-01, 4.874915618989705e-01
                                        , 4.442265159007090e-01, 3.575895989163274e-01, 4.502394669269035e-01
                                        , 4.430114321018267e-01, 3.447055207040375e-01, 4.352569250274619e-01
                                        , 3.676622700906528e-01, 3.961266100895113e-01, 4.897890479324899e-01
                                        , 3.993976052861839e-01, 3.934136212626961e-01, 4.704192853626573e-01
                                        , 4.090903427163520e-01, 3.908612469610206e-01, 4.619346484911491e-01
                                        , 4.031004684146753e-01, 3.582180905364388e-01, 4.396354166475739e-01
                                        , 3.842841276325525e-01, 2.883507721006856e-01, 4.180414920665683e-01 };

    MATR GyroScalFac[NUM_TEMP_SAM][3] = {     1.217147270610938e+03, 1.210499770911360e+03, 1.212418993972874e+03
                                            , 1.217266001453792e+03, 1.210580691057395e+03, 1.212511715460854e+03
                                            , 1.217388762189049e+03, 1.210718135112465e+03, 1.212636593872809e+03
                                            , 1.217500893273029e+03, 1.210842845461002e+03, 1.212740688187718e+03
                                            , 1.217627954485883e+03, 1.210954845303744e+03, 1.212854683035058e+03
                                            , 1.217793581935149e+03, 1.211128251012742e+03, 1.213016957247592e+03
                                            , 1.217920113260003e+03, 1.211248860488966e+03, 1.213133172861772e+03
                                            , 1.217962553593530e+03, 1.211295187032934e+03, 1.213170655437808e+03
                                            , 1.218004563942304e+03, 1.211340097690135e+03, 1.213214526989506e+03
                                            , 1.218027482288507e+03, 1.211361649387463e+03, 1.213233964083529e+03 };

    MATR GyroInstErr[9] = {      9.999904943122093e-01,     2.082351705074039e-03,     1.638850885206196e-03,
                                                            -2.068500292576113e-03,     9.999803536184257e-01,    -2.082188970652525e-03,
                                                             3.036834612770142e-04,     2.504630264732947e-03,     9.999856176213234e-01};


    for(i = 0 ; i < NUM_TEMP_SAM;i++)
    {
        for(j = 0;j < 3 ; j++)
        {
            lp_compen -> GyroTempSamPoint[i][j] = GyroTempSamPoint[i][j];
            lp_compen -> GyroBias[i][j] = GyroBias[i][j];
            lp_compen -> GyroScalFac[i][j] = GyroScalFac[i][j];
        }
    }
    for(i = 0;i < 9;i++)
    {
        lp_compen -> GyroInstErr[i] = GyroInstErr[i];
    }
    //lp_compen -> GyroQDPara_Y[0] = 0.000563943552174e-3;
    //lp_compen -> GyroQDPara_Y[1] = -0.160424051378102e-3;
}*/







/*********************************************????*******************************************************/
/*????:Acc_Compen_Para_Init_60                                                                         */
/*??????:60?????????????                                                                */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                                           */
/*???:                                                                                                  */
/*????:                                                                                                */
/*????:?                                                                                              */
/*????:??                                                                                            */
/*??1:????????????                                                                           */
/*???:?                                                                                                */
/************************************************************************************************************/
/*void Acc_Compen_Para_Init_60(p_Compen lp_compen)
{
    IPARA i,j;
    MATR AccTempSamPoint[NUM_TEMP_SAM][3] ={ -2.942391330571074e+01,-2.827700950417886e+01,-2.932135097489914e+01
                                            ,-2.051815590999933e+01,-1.936234665806509e+01,-2.046330685607874e+01
                                            ,-1.039844715165270e+01,-9.164617321472909e+00,-1.034542048594484e+01
                                            ,-6.400215302773342e-01, 6.204768595925334e-01,-6.034517224882505e-01
                                            , 9.335968060815153e+00, 1.054275870668174e+01, 9.337255072398467e+00
                                            , 2.370927954113461e+01, 2.488827211717987e+01, 2.365982716888357e+01
                                            , 3.857734603214508e+01, 3.974213180162057e+01, 3.839412376795136e+01
                                            , 4.831273496904652e+01, 4.947819204239271e+01, 4.810653417980181e+01
                                            , 6.431589308648132e+01, 6.571831251794582e+01, 6.426340277698523e+01
                                            , 7.944093692605524e+01, 7.943490995618846e+01, 7.840342065021729e+01 };

    MATR AccBias[NUM_TEMP_SAM][3] = {    -1.869187477023829e+00, 1.053692046968313e+00,-8.211460374159834e-01
                                        ,-1.782512752163482e+00, 9.623861831415010e-01,-7.546028464243645e-01
                                        ,-1.685600895319923e+00, 8.616865019264361e-01,-6.717531959355553e-01
                                        ,-1.591054742037793e+00, 7.748192857951393e-01,-5.956651865081829e-01
                                        ,-1.501491109356232e+00, 6.902618479459739e-01,-5.249709197484037e-01
                                        ,-1.399696053839465e+00, 6.035019100967953e-01,-4.586990028489700e-01
                                        ,-1.386234833518229e+00, 5.548571192448610e-01,-4.590684546645931e-01
                                        ,-1.332742569202599e+00, 5.029147515772729e-01,-4.063054406981137e-01
                                        ,-1.247051138336678e+00, 4.268976740632883e-01,-3.235169976115346e-01
                                        ,-1.168870695250099e+00, 3.392351021359129e-01,-2.360124187009212e-01 };
    
    MATR AccScalFac[NUM_TEMP_SAM][3] = {  1.854641697686797e+01, 1.859113610194574e+01, 1.922173039517002e+01
                                        , 1.854855047398358e+01, 1.859242121403006e+01, 1.922669115890666e+01
                                        , 1.855047656058047e+01, 1.859392744631458e+01, 1.923181502728572e+01
                                        , 1.855175654782304e+01, 1.859458032971262e+01, 1.923620542348966e+01
                                        , 1.855080518764419e+01, 1.859495257906339e+01, 1.923942447662267e+01
                                        , 1.854535899212165e+01, 1.859139357195300e+01, 1.923942980913836e+01
                                        , 1.854249362692551e+01, 1.858970876995049e+01, 1.924034457703509e+01
                                        , 1.854562574616033e+01, 1.859418529824443e+01, 1.924739165393611e+01
                                        , 1.855120323792339e+01, 1.860022369488256e+01, 1.925732687942505e+01
                                        , 1.855994508865206e+01, 1.861068708904043e+01, 1.926961507874862e+01 };
                                                                                        
    MATR AccInstErr[9] = {     9.999657121688109e-01,    -5.805910246762376e-03,     1.932172180738592e-04,
                                                         7.670813076049749e-03,     9.998739318958332e-01,     4.760698070634520e-04,
                                                        -7.835029833568308e-04,     2.242161745110648e-04,     1.000034565305704e+00};

    for(i = 0 ; i < NUM_TEMP_SAM;i++)
    {
        for(j = 0;j < 3 ; j++)	
        {
            lp_compen -> AccTempSamPoint[i][j] = AccTempSamPoint[i][j];
            lp_compen -> AccBias[i][j] = AccBias[i][j];
            lp_compen -> AccScalFac[i][j] = AccScalFac[i][j];
        }
    }
    for(i = 0;i < 9;i++)
    {
        lp_compen -> AccInstErr[i] = AccInstErr[i];

    }
    //lp_compen -> AccQDPara_X[0] = -0.000002131431035;
    //lp_compen -> AccQDPara_X[1] = 0.001791887048436;

    //lp_compen -> AccQDPara_Y[0] = -0.000005585379081;
    //lp_compen -> AccQDPara_Y[1] = 0.003917815140706;

    //lp_compen -> AccQDPara_Z[0] = -0.000004385903713;
    //lp_compen -> AccQDPara_Z[1] = 0.002881613833479;

}*/




/*********************************************函数说明*******************************************************/
/*函数名称：GyroCompenCompute                                                                               */
/*函数功能描述：陀螺补偿计算                                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递                                                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void GyroCompenCompute(p_Compen lp_Compen,p_CmdNormalTempCompen lp_GyroNormalTempCompen,p_CmdFullTempCompen lp_GyroFullTempCompen,p_CmdANNCompenData lp_GyroANNCompenData)
{
//    UINT32 i = 0;
    //记录上一帧陀螺数据
    lp_Compen -> LastGyro[0] = lp_Compen -> Gyro[0];
    lp_Compen -> LastGyro[1] = lp_Compen -> Gyro[1];
    lp_Compen -> LastGyro[2] = lp_Compen -> Gyro[2];
    //TempSensCompen_60(lp_SenorRaw -> GyroTempRaw,lp_Compen -> GyroTemp);

    GetTempRangeNum(lp_Compen -> GyroTemp,lp_GyroFullTempCompen -> TempSamPoint,lp_Compen -> GyroTempRangeNum);
    //计算1s平均温度及温度梯度（温度差）
    ComputeGyroTempDiff(lp_Compen);
    //RTCompenPara(lp_Compen -> GyroBias,lp_Compen -> GyroTemp,lp_Compen -> GyroTempRangeNum,lp_Compen -> GyroTempSamPoint,lp_Compen -> RTGyroBias);
    
    lp_Compen -> RTGyroBias[0] = ANN_Predict(lp_Compen -> GyroTemp[0], lp_Compen -> GyroTemp_Diff[0],&lp_GyroANNCompenData -> ANNCompen_X);
    lp_Compen -> RTGyroBias[1] = ANN_Predict(lp_Compen -> GyroTemp[1], lp_Compen -> GyroTemp_Diff[1],&lp_GyroANNCompenData -> ANNCompen_Y);
    lp_Compen -> RTGyroBias[2] = ANN_Predict(lp_Compen -> GyroTemp[2], lp_Compen -> GyroTemp_Diff[2],&lp_GyroANNCompenData -> ANNCompen_Z);
    
    RTCompenPara(lp_GyroFullTempCompen -> Scale_Factor,lp_Compen -> GyroTemp,lp_Compen -> GyroTempRangeNum,lp_GyroFullTempCompen -> TempSamPoint,lp_Compen -> RTGyroScalFac);
    //LinerCompen_60(lp_SenorRaw -> GyroRaw,lp_Compen -> RTGyroBias,lp_Compen -> RTGyroScalFac,lp_Compen -> GyroInstErr,d_Gyro);
    LinerCompen_60_ANN_Order(lp_Compen -> GyroRaw,lp_Compen -> RTGyroBias,lp_Compen -> RTGyroScalFac,lp_GyroNormalTempCompen -> Misalignment_Compensation_Matrix,lp_Compen -> Gyro);
    //陀螺零偏修正
    lp_Compen -> Gyro[0] -= lp_GyroNormalTempCompen -> Bias_Correct_Val[0];
    lp_Compen -> Gyro[1] -= lp_GyroNormalTempCompen -> Bias_Correct_Val[1];
    lp_Compen -> Gyro[2] -= lp_GyroNormalTempCompen -> Bias_Correct_Val[2];
    
    //陀螺跳大数判断处理
    /*if(g_SysVar.Time > 30.0)
    {
        if((fabs(lp_Compen -> Gyro[0]) >= THRESHOLD_GYRO)||(fabs(lp_Compen -> LastGyro[0] - lp_Compen -> Gyro[0]) < MIN_DATA))
        {
            lp_Compen -> Gyro[0] = lp_Compen -> LastGyro[0];
            lp_Compen -> Count_GyroErr[0]++;
        }
        else
        {
            lp_Compen -> Count_GyroErr[0] = 0;
        }
        if(lp_Compen -> Count_GyroErr[0] >= COUNT_IMU_FAIL)
        {
            lp_Compen -> IMU_Valid &=~GYRO_X_VALID; 
        }
        else
        {
            lp_Compen -> IMU_Valid |= GYRO_X_VALID; 
        }
        
        if((fabs(lp_Compen -> Gyro[1]) >= THRESHOLD_GYRO)||(fabs(lp_Compen -> LastGyro[1] - lp_Compen -> Gyro[1]) < MIN_DATA))
        {
            lp_Compen -> Gyro[1] = lp_Compen -> LastGyro[1];
            lp_Compen -> Count_GyroErr[1]++;
        }
        else
        {
            lp_Compen -> Count_GyroErr[1] = 0;
        }
        if(lp_Compen -> Count_GyroErr[1] >= COUNT_IMU_FAIL)
        {
            lp_Compen -> IMU_Valid &=~GYRO_Y_VALID; 
        }
        else
        {
            lp_Compen -> IMU_Valid |= GYRO_Y_VALID; 
        }
        
        if((fabs(lp_Compen -> Gyro[2]) >= THRESHOLD_GYRO)||(fabs(lp_Compen -> LastGyro[2] - lp_Compen -> Gyro[2]) < MIN_DATA))
        {
            lp_Compen -> Gyro[2] = lp_Compen -> LastGyro[2];
            lp_Compen -> Count_GyroErr[2]++;
        }
        else
        {
            lp_Compen -> Count_GyroErr[2] = 0;
        }
        if(lp_Compen -> Count_GyroErr[2] >= COUNT_IMU_FAIL)
        {
            lp_Compen -> IMU_Valid &=~GYRO_Z_VALID; 
        }
        else
        {
            lp_Compen -> IMU_Valid |= GYRO_Z_VALID; 
        }
    }*/
    //else
    {
        lp_Compen -> IMU_Valid = 0x3F;
    }
    //启动过程补偿
     /*if((g_SysVar.isColdStart == YES) && (g_SysVar.Time <= 300.0))
     {
            lp_Compen -> Gyro[0] += 4.323999999999995e-05;
     }
     if((g_SysVar.isColdStart == YES) && (g_SysVar.Time > 300.0) && (g_SysVar.Time <= 1400.0))
     {
            lp_Compen -> Gyro[0] += 2.280909090909110e-08 * (1400.0 - g_SysVar.Time);
     }
     if(g_SysVar.Time > 1400.0)
     {
            g_SysVar.isColdStart = NO;
            jd_ex.Init_Start_Flag = 0;
     }
     if((lp_Compen -> GyroTemp[0]>= 43) && (lp_Compen -> GyroTemp[0] <= 59.7))
      lp_Compen -> Gyro[0] = lp_Compen -> Gyro[0] - 1.388888888888884e-05 * 2/3;*/
}





/*********************************************函数说明*******************************************************/
/*函数名称：AccCompenCompute                                                                               */
/*函数功能描述：陀螺补偿计算                                                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递                                                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void AccCompenCompute(p_Compen lp_Compen,p_CmdNormalTempCompen lp_AccNormalTempCompen,p_CmdFullTempCompen lp_AccFullTempCompen,p_CmdANNCompenData lp_AccANNCompenData)
{
    //记录上一帧加表数据
    lp_Compen -> LastAcc[0] = lp_Compen -> Acc[0];
    lp_Compen -> LastAcc[1] = lp_Compen -> Acc[1];
    lp_Compen -> LastAcc[2] = lp_Compen -> Acc[2];
    //TempSensCompen_60(lp_SenorRaw -> AccTempRaw,lp_Compen -> AccTemp);
    GetTempRangeNum(lp_Compen -> AccTemp,lp_AccFullTempCompen -> TempSamPoint,lp_Compen -> AccTempRangeNum);
    //RTCompenPara(lp_Compen -> AccBias,lp_Compen -> AccTemp,lp_Compen -> AccTempRangeNum,lp_Compen -> AccTempSamPoint,lp_Compen -> RTAccBias);
    ComputeAccTempDiff(lp_Compen);
    lp_Compen -> RTAccBias[0] = ANN_Predict(lp_Compen -> AccTemp[0],lp_Compen->AccTemp_Diff[0],&lp_AccANNCompenData -> ANNCompen_X);
    lp_Compen -> RTAccBias[1] = ANN_Predict(lp_Compen -> AccTemp[1],lp_Compen->AccTemp_Diff[1],&lp_AccANNCompenData -> ANNCompen_Y);
    lp_Compen -> RTAccBias[2] = ANN_Predict(lp_Compen -> AccTemp[2],lp_Compen->AccTemp_Diff[2],&lp_AccANNCompenData -> ANNCompen_Z);
    RTCompenPara(lp_AccFullTempCompen -> Scale_Factor,lp_Compen -> AccTemp,lp_Compen -> AccTempRangeNum,lp_AccFullTempCompen -> TempSamPoint,lp_Compen -> RTAccScalFac);

    LinerCompen_60_ANN_Order(lp_Compen->AccRaw, lp_Compen->RTAccBias, lp_Compen->RTAccScalFac, lp_AccNormalTempCompen->Misalignment_Compensation_Matrix, lp_Compen -> Acc);
    
	  //加速度零偏修正
    lp_Compen -> Acc[0] -= lp_AccNormalTempCompen ->Bias_Correct_Val[0];
    lp_Compen -> Acc[1] -= lp_AccNormalTempCompen ->Bias_Correct_Val[1];
    lp_Compen -> Acc[2] -= lp_AccNormalTempCompen ->Bias_Correct_Val[2];
	
	  //m/s2
		lp_Compen -> Acc[0] *= Gn_CQ;
		lp_Compen -> Acc[1] *= Gn_CQ;
		lp_Compen -> Acc[2] *= Gn_CQ;
    //内杆臂修正
    //InerLeverArmCompen(lp_Compen-> Gyro,lp_Compen -> Acc,lp_Compen->AccLeverArm);
    //加表跳大数判断处理
    /*if(g_SysVar.Time > 30.0)
    {
        if((fabs(lp_Compen -> Acc[0]) >= THRESHOLD_ACC)||(fabs(lp_Compen -> LastAcc[0] - lp_Compen -> Acc[0]) < MIN_DATA))
        {
            lp_Compen -> Acc[0] = lp_Compen -> LastAcc[0];
            lp_Compen -> Count_AccErr[0]++;
        }
        else
        {
            lp_Compen -> Count_AccErr[0] = 0;
        }
        if(lp_Compen -> Count_AccErr[0] >= COUNT_IMU_FAIL)
        {
            lp_Compen -> IMU_Valid &=~ACC_X_VALID; 
        }
        else
        {
            lp_Compen -> IMU_Valid |= ACC_X_VALID; 
        }
        
        if((fabs(lp_Compen -> Acc[1]) >= THRESHOLD_ACC)||(fabs(lp_Compen -> LastAcc[1] - lp_Compen -> Acc[1]) < MIN_DATA))
        {
            lp_Compen -> Acc[1] = lp_Compen -> LastAcc[1];
            lp_Compen -> Count_AccErr[1]++;
        }
        else
        {
            lp_Compen -> Count_AccErr[1] = 0;
        }
        if(lp_Compen -> Count_AccErr[1] >= COUNT_IMU_FAIL)
        {
            lp_Compen -> IMU_Valid &=~ACC_Y_VALID; 
        }
        else
        {
            lp_Compen -> IMU_Valid |= ACC_Y_VALID; 
        }
        
        if((fabs(lp_Compen -> Acc[2]) >= THRESHOLD_ACC)||(fabs(lp_Compen -> LastAcc[2] - lp_Compen -> Acc[2]) < MIN_DATA))
        {
            lp_Compen -> Acc[2] = lp_Compen -> LastAcc[2];
            lp_Compen -> Count_AccErr[2]++;
        }
        else
        {
            lp_Compen -> Count_AccErr[2] = 0;
        }
        if(lp_Compen -> Count_AccErr[2] >= COUNT_IMU_FAIL)
        {
            lp_Compen -> IMU_Valid &=~ACC_Z_VALID; 
        }
        else
        {
            lp_Compen -> IMU_Valid |= ACC_Z_VALID; 
        }
    }*/
   // else
    {
        lp_Compen -> IMU_Valid = 0x3F;
    }
}




/*********************************************函数说明*******************************************************/
/*函数名称：TempRangeNum                                                                                    */
/*函数功能描述：温度范围序号计算                                                                            */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：2014 年 11 月 24 日 13: 30                                                                 */
/*编写人：陈可                                                                                              */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递                                                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void GetTempRangeNum(VEC Temp[3],MATR TempSamPoint[NUM_TEMP_SAM][3],IPARA TempRangeNum[3])
{
    IPARA i,j;
    for(i = 0; i < 3; i++)
    {
        //温度区间编号：0：NUM_TEMP_SAM，其中区间0表示温度小于最低温度采样点的全部温度范围，区间NUM_TEMP_SAM表示温度大于最低温度采样点的全部温度范围
        TempRangeNum[i] = NUM_TEMP_SAM;
        for(j = 0; j < NUM_TEMP_SAM; j++)
        {
            if((Temp[i] <= TempSamPoint[j][i]))
            {
                TempRangeNum[i] = j;
                break;
            }
        }
        if(TempRangeNum[i] == 0)
        {
            //小于最低温度采样点，按最近的两个采样点线性拟合的结果进行补偿
            TempRangeNum[i] ++;
        }
        
        if(TempRangeNum[i] == NUM_TEMP_SAM)
        {
            //大于最高温度采样点，按最近的两个采样点线性拟合的结果进行补偿
            TempRangeNum[i] --;
        }
    }
}




/*********************************************函数说明*******************************************************/
/*函数名称：ComputeGyroTempDiff                                                                             */
/*函数功能描述：温度梯度、温度差计算                                                                        */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：2014 年 11 月 24 日 13: 30                                                                 */
/*编写人：陈可                                                                                              */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递                                                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeGyroTempDiff(p_Compen lp_Compen)
{
    UINT32 i = 0,j = 0;
    //DPARA GyroTemp_Move_Sum[3] = { 0.0,0.0,0.0 };
    DPARA GyroTemp_Move_Mean[3] = { 0.0,0.0,0.0};
    for (i = 0; i < 3; i++)
    {
        lp_Compen -> GyroTemp_1s_Sum[i] += lp_Compen -> GyroTemp[i];
    }
    lp_Compen -> GyroCount_1s++;
    if (lp_Compen -> GyroCount_1s == NUM_1S_COUNT)
    {
        for (i = 0; i < 3; i++)
        {
            lp_Compen -> GyroTemp_1s_Mean[i] = lp_Compen -> GyroTemp_1s_Sum[i] / NUM_1S_COUNT;
            lp_Compen -> GyroTemp_1s_Sum[i] = 0.0;
            //lp_Compen -> GyroTemp_Move_Mean_Buffer[i][lp_Compen -> GyroCircle_Count] = lp_Compen -> GyroTemp_1s_Mean[i];
            lp_Compen->GyroTemp_Move_Mean_Buffer[i][lp_Compen->GyroCircle_Count] = lp_Compen->GyroTemp[i];//lp_Compen->GyroTemp_1s_Mean[i];
            if (lp_Compen ->isGyroMove_Mean_BufferInit != YES)
            {
                for (j = 0; j < lp_Compen -> GyroCircle_Count + 1; j++)
                {
                    GyroTemp_Move_Mean[i] += lp_Compen -> GyroTemp_Move_Mean_Buffer[i][j];
                }
                GyroTemp_Move_Mean[i] /= (lp_Compen -> GyroCircle_Count + 1);
            }
            else
            {
                for (j = 0; j < NUM_TEMP_DIFF_BUFFER; j++)
                {
                    GyroTemp_Move_Mean[i] += lp_Compen -> GyroTemp_Move_Mean_Buffer[i][j];
                }
                GyroTemp_Move_Mean[i] /= NUM_TEMP_DIFF_BUFFER;
            }
        }
        lp_Compen -> GyroCount_1s = 0;
        lp_Compen -> GyroCircle_Count++;
        if (lp_Compen -> GyroCircle_Count == NUM_TEMP_DIFF_BUFFER)
        {
            lp_Compen -> isGyroMove_Mean_BufferInit = YES;
            lp_Compen -> GyroCircle_Count = 0;
        }
        for (i = 0;i < 3;i++)
        {
            //lp_Compen -> GyroTemp_Diff[i] = lp_Compen -> GyroTemp_1s_Mean[i] - GyroTemp_Move_Mean[i];
            lp_Compen->GyroTemp_Diff[i] = lp_Compen->GyroTemp[i] - GyroTemp_Move_Mean[i];
        }
    }
}





/*********************************************函数说明*******************************************************/
/*函数名称：ComputeAccTempDiff                                                                             */
/*函数功能描述：温度梯度、温度差计算                                                                        */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：2014 年 11 月 24 日 13: 30                                                                 */
/*编写人：陈可                                                                                              */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递                                                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeAccTempDiff(p_Compen lp_Compen)
{
    IPARA i = 0, j = 0;
    //DPARA AccTemp_Move_Sum[3] = { 0.0,0.0,0.0 };
    DPARA AccTemp_Move_Mean[3] = { 0.0,0.0,0.0 };
    for (i = 0; i < 3; i++)
    {
        lp_Compen->AccTemp_1s_Sum[i] += lp_Compen->AccTemp[i];
    }
    lp_Compen -> AccCount_1s++;
    if (lp_Compen -> AccCount_1s == NUM_1S_COUNT)
    {
        for (i = 0; i < 3; i++)
        {
            lp_Compen -> AccTemp_1s_Mean[i] = lp_Compen->AccTemp_1s_Sum[i] / NUM_1S_COUNT;
            lp_Compen -> AccTemp_1s_Sum[i] = 0.0;
            //lp_Compen -> AccTemp_Move_Mean_Buffer[i][lp_Compen->AccCircle_Count] = lp_Compen -> AccTemp_1s_Mean[i];
            lp_Compen->AccTemp_Move_Mean_Buffer[i][lp_Compen->AccCircle_Count] = lp_Compen->AccTemp[i];//lp_Compen->AccTemp_1s_Mean[i];
            if (lp_Compen ->isAccMove_Mean_BufferInit != YES)
            {
                for (j = 0; j < lp_Compen -> AccCircle_Count + 1; j++)
                {
                    AccTemp_Move_Mean[i] += lp_Compen -> AccTemp_Move_Mean_Buffer[i][j];

                }
                AccTemp_Move_Mean[i] /= (lp_Compen -> AccCircle_Count + 1);
            }
            else
            {
                for (j = 0; j < NUM_TEMP_DIFF_BUFFER; j++)
                {
                    AccTemp_Move_Mean[i] += lp_Compen -> AccTemp_Move_Mean_Buffer[i][j];
                }
                AccTemp_Move_Mean[i] /= NUM_TEMP_DIFF_BUFFER;
            }
        }
        lp_Compen->AccCount_1s = 0;
        lp_Compen->AccCircle_Count++;
        if (lp_Compen->AccCircle_Count == NUM_TEMP_DIFF_BUFFER)
        {
            lp_Compen->isAccMove_Mean_BufferInit = YES;
            lp_Compen->AccCircle_Count = 0;
        }
        for (i = 0; i < 3; i++)
        {
            //lp_Compen->AccTemp_Diff[i] = lp_Compen->AccTemp_1s_Mean[i] - AccTemp_Move_Mean[i];
            lp_Compen->AccTemp_Diff[i] = lp_Compen->AccTemp[i] - AccTemp_Move_Mean[i];
        }
    }
}




/*********************************************函数说明*******************************************************/
/*函数名称：RTCompenPara                                                                                    */
/*函数功能描述：实时补偿参数计算                                                                            */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：2014 年 11 月 24 日 13: 30                                                                 */
/*编写人：陈可                                                                                              */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递                                                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void RTCompenPara(MATR Para[NUM_TEMP_SAM][3],VEC Temp[3],IPARA TempRangeNum[3],MATR TempSamPoint[NUM_TEMP_SAM][3],VEC RTPara[3])
{
    IPARA i = 0;
    for(i = 0; i < 3; i++)
    {
        //对随温度变化的补偿参数进行分段线性拟合计算
        RTPara[i] = Para[TempRangeNum[i] - 1][i] + (Para[TempRangeNum[i]][i] - Para[TempRangeNum[i] - 1][i]) / (TempSamPoint[TempRangeNum[i]][i] - TempSamPoint[TempRangeNum[i] - 1][i]) * (Temp[i] - TempSamPoint[TempRangeNum[i] - 1][i]);
    }
}




/*********************************************函数说明*******************************************************/
/*函数名称：LinerCompen_60                                                                                  */
/*函数功能描述：（陀螺、加速度计）线性补偿                                                                    */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递                                                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void LinerCompen_60(INT32 Raw[3],DPARA RTBias[3],DPARA RTScalFac[3],MATR InstErr[9],DPARA CompenData[3])
{
    IPARA i = 0;
    DRAW RawTemp[3];
    //将实时计算的当前温度下的零偏从输入原始数据中扣除
    for(i = 0; i < 3 ; i++)
    {
        CompenData[i] = 0.0;     //对应输出补偿数先清零
        RawTemp[i] = (DPARA)Raw[i] - RTBias[i];
        RawTemp[i] /= RTScalFac[i];
    }
    Mat_Mul(InstErr, RawTemp, CompenData, 3, 3, 1);
}




/*********************************************函数说明*******************************************************/
/*函数名称：LinerCompen_60_ANN_Order                                                                        */
/*函数功能描述：（陀螺、加速度计）线性补偿,按先刻度因子后零偏的顺序补偿                                        */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                 */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递                                                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void LinerCompen_60_ANN_Order(DPARA Raw[3],DPARA RTBias[3],DPARA RTScalFac[3],MATR InstErr[9],DPARA CompenData[3])
{
    IPARA i = 0;
    DRAW RawTemp[3] = {0.0};
    //将实时计算的当前温度下的零偏从输入原始数据中扣除
    for(i = 0; i < 3 ; i++)
    {
        CompenData[i] = 0.0;     //对应输出补偿数先清零
        RawTemp[i] = (DRAW)Raw[i] / RTScalFac[i];
        RawTemp[i] = RawTemp[i] - RTBias[i];
    }
    //乘以安装误差角
    Mat_Mul(InstErr, RawTemp, CompenData, 3, 3, 1);
}




/*********************************************函数说明*******************************************************/
/*函数名称ComputeLeverArmAcc                                                                              */
/*函数功能描述:根据输入杆臂长度和陀螺补偿数据计算出杆臂加速度值                                             */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递                                                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeLeverArmAcc(DPARA Gyro[3],MATR AccLeverArm[9],DPARA Acc_r[3])
{
    DPARA r_Gyro[3];//弧度表示的陀螺输出
    IPARA i;

    for(i = 0;i < 3;i++)
    {
        r_Gyro[i] = Gyro[i] * D2R;//转为弧度单位，此处根据具体补偿输出的单位进行调整
    }
    Acc_r[0] =  - AccLeverArm[0] * (r_Gyro[1] * r_Gyro[1] + r_Gyro[2] * r_Gyro[2]) + AccLeverArm[1] * r_Gyro[0] * r_Gyro[1] + AccLeverArm[2] * r_Gyro[0] * r_Gyro[2];
    Acc_r[1] =  + AccLeverArm[3] * r_Gyro[0] * r_Gyro[1] - AccLeverArm[4] * (r_Gyro[0] * r_Gyro[0] + r_Gyro[2] * r_Gyro[2]) + AccLeverArm[5] * r_Gyro[1] * r_Gyro[2];
    Acc_r[2] =  + AccLeverArm[6] * r_Gyro[0] * r_Gyro[2] + AccLeverArm[7] * r_Gyro[1] * r_Gyro[2] - AccLeverArm[8] * (r_Gyro[0] * r_Gyro[0] + r_Gyro[1] * r_Gyro[1]);
}




/*********************************************函数说明*******************************************************/
/*函数名称ComputeLeverArmVn                                                                               */
/*函数功能描述:根据输入杆臂长度和陀螺补偿数据计算出导航系（n系下的分量）下的杆臂速度                        */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：与GPS组合修正杆臂速度时r_Webb最好为平均值，而非瞬时值                                              */
/*返回值：无                                                                                                */
/************************************************************************************************************/
/*void ComputeLeverArmVn(MATR Cnb[9],DPARA r_Webb[3],MATR VnLeverArm[3],VEL Vn_r[3])
{
    MATR Cbn[9];
    VEL Vb_r[3];
    //计算出杆臂速度Vn_r
    Vec_Cross(r_Webb, VnLeverArm, Vb_r);
    Mat_Tr(Cnb, Cbn, 3, 3);
    Mat_Mul(Cbn, Vb_r, Vn_r, 3, 3, 1);
}*/




/*********************************************函数说明*******************************************************/
/*函数名称：InerLeverArmCompen                                                                              */
/*函数功能描述：（加速度计）内杆臂补偿                                                                      */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递,Gyro[3]默认单位为°/s,r_Gyro[3]默认单位为rad/s                             */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void InerLeverArmCompen(DPARA Gyro[3],DPARA Acc[3],MATR AccLeverArm[9])
{
    DPARA Acc_r[3];
    IPARA i;

    ComputeLeverArmAcc(Gyro,AccLeverArm,Acc_r);//计算杆臂加速度

    for(i = 0;i < 3; i++)
    {
        Acc[i] -= Acc_r[i];//扣除内杆臂项
    }
}





/*********************************************函数说明*******************************************************/
/*函数名称：ComputeColdStartCompenVal                                                                       */
/*函数功能描述：60系统（陀螺、加速度计）温度传感器的补偿计算                                                */
/*版本号：  Ver 0.1                                                                                         */
/*编写日期/时间：                                                                                           */
/*编写人：                                                                                                  */
/*输入变量：                                                                                                */
/*输出变量：无                                                                                              */
/*测试用例：暂缺                                                                                            */
/*备注1：该函数输入变量为地址传递                                                                           */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeColdStartCompenVal(TIME Time, DPARA Para[2],DPARA *Output)
{
    *Output = Time * Para[0] + Para[1];
}





