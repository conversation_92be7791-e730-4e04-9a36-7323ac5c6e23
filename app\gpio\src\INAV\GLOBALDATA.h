#ifndef _GLOBALDATA_H
#define _GLOBALDATA_H
/***********************************************************************************************************************************/
/*GLOBALDATA.h                                                                                                                   */
/*  Ver 0.1                                                                                                                        */
/*/                                                                                                */
/*                                                                                                                             */
/*                                                                                                                             */
/*GNSSlocusGen.m                                                              */
/*                       */
/*******************************************************************************************************************************************/
#include "DATASTRUCT.h"
//#include "data.h"
//
SelfTest g_SelfTest;//

SysVar g_SysVar;  //

InitBind g_InitBind;//

Align g_Align;//

Navi g_Navi;//

Kalman g_Kalman;//Kalman

GNSSData g_GNSSData_In_Use,g_GNSSData_For_FineAlign;//GNSS

Compen g_Compen;//

InertialSysAlign g_InertialSysAlign;//

DynamicInertialSysAlign g_DynamicInertialSysAlign;

IMUSmoothAverage g_IMUSmoothAverage;

//CmdNormalTempCompenData g_CmdNormalTempCompenData;

//CmdFullTempCompenData g_CmdFullTempCompenData;

//CmdANNCompenData g_GyroANNCompen;

//CmdANNCompenData g_AccANNCompen;
#endif
